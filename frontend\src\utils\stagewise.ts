/**
 * Stagewise初始化工具
 * 
 * 功能说明：
 * 1. 初始化Stagewise工具栏
 * 2. 配置Vue特定功能
 * 3. 设置开发环境专用功能
 * 4. 确保仅在浏览器环境中运行
 */

import { initToolbar, type ToolbarConfig } from '@stagewise/toolbar'
import VuePlugin from '@stagewise-plugins/vue'
import { shouldEnableDevTools, waitForDOM, runDevTools } from './environment'

// 简化的配置接口
interface StagewiseConfig extends Partial<ToolbarConfig> {
  enabled?: boolean
}

// 默认配置
const defaultConfig: StagewiseConfig = {
  enabled: process.env.NODE_ENV === 'development',
  plugins: [VuePlugin],
}

// Stagewise状态
let isInitialized = false

/**
 * 初始化Stagewise工具栏
 */
export async function initStagewise(config: Partial<StagewiseConfig> = {}): Promise<void> {
  return runDevTools(async () => {
    try {
      // 检查环境
      if (!shouldEnableDevTools()) {
        console.warn('Stagewise: 环境不支持，跳过初始化')
        return
      }

    // 合并配置
    const finalConfig = { ...defaultConfig, ...config }

    if (!finalConfig.enabled) {
      console.log('Stagewise: 已禁用，跳过初始化')
      return
    }

    // 等待DOM加载完成
    await waitForDOM()

    // 初始化Stagewise工具栏
    initToolbar(finalConfig)
    isInitialized = true

    console.log('✅ Stagewise初始化完成')

    // 添加自定义样式
    addCustomStyles()

    // 设置快捷键
    setupShortcuts()

    } catch (error) {
      console.error('❌ Stagewise初始化失败:', error)
    }
  }) || Promise.resolve()
}

/**
 * 获取Vue组件信息
 */
function getVueComponentInfo(element: HTMLElement): any {
  try {
    // 尝试获取Vue实例
    const vueInstance = (element as any).__vueParentComponent || (element as any).__vue__
    
    if (vueInstance) {
      return {
        componentName: vueInstance.type?.name || vueInstance.type?.__name || 'Unknown',
        props: vueInstance.props || {},
        data: vueInstance.data || {},
        computed: vueInstance.computed || {},
        methods: Object.getOwnPropertyNames(vueInstance).filter(key => 
          typeof vueInstance[key] === 'function'
        ),
      }
    }
    
    return null
  } catch (error) {
    console.warn('获取Vue组件信息失败:', error)
    return null
  }
}

/**
 * 添加自定义样式
 */
function addCustomStyles(): void {
  const style = document.createElement('style')
  style.textContent = `
    /* Stagewise自定义样式 */
    .stagewise-toolbar {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      border-radius: 8px;
      backdrop-filter: blur(10px);
    }
    
    .stagewise-highlight {
      outline: 2px solid #667eea !important;
      outline-offset: 2px;
      background-color: rgba(102, 126, 234, 0.1) !important;
    }
    
    .stagewise-tooltip {
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 10000;
    }
  `
  document.head.appendChild(style)
}

/**
 * 设置快捷键
 */
function setupShortcuts(): void {
  document.addEventListener('keydown', (event) => {
    // Ctrl+Shift+S: 切换工具栏
    if (event.ctrlKey && event.shiftKey && event.key === 'S') {
      event.preventDefault()
      console.log('🔧 切换工具栏快捷键触发')
    }

    // Ctrl+Shift+C: 添加注释模式
    if (event.ctrlKey && event.shiftKey && event.key === 'C') {
      event.preventDefault()
      console.log('💬 注释模式快捷键触发')
    }

    // Escape: 退出当前模式
    if (event.key === 'Escape') {
      console.log('🚪 退出模式快捷键触发')
    }
  })
}

/**
 * 销毁Stagewise实例
 */
export function destroyStagewise(): void {
  if (isInitialized) {
    isInitialized = false
    console.log('🗑️ Stagewise已销毁')
  }
}

/**
 * 检查Stagewise是否已初始化
 */
export function isStagewiseInitialized(): boolean {
  return isInitialized
}

// 在页面卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', destroyStagewise)
}

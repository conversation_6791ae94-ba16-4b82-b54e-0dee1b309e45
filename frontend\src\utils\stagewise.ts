/**
 * Stagewise初始化工具
 * 
 * 功能说明：
 * 1. 初始化Stagewise工具栏
 * 2. 配置Vue特定功能
 * 3. 设置开发环境专用功能
 * 4. 确保仅在浏览器环境中运行
 */

import { createStagewiseToolbar } from '@stagewise/toolbar-vue'
import { VuePlugin } from '@stagewise-plugins/vue'
import { shouldEnableDevTools, waitForDOM, runDevTools } from './environment'

// 类型定义
interface StagewiseConfig {
  enabled: boolean
  toolbar: {
    position: string
    theme: string
    size: string
    autoHide: boolean
    zIndex: number
  }
  project: {
    name: string
    framework: string
    version: string
    description: string
  }
  ai: {
    enabled: boolean
    provider: string
    model: string
    contextWindow: number
  }
  selector: {
    highlightColor: string
    borderWidth: number
    showTooltip: boolean
    excludeSelectors: string[]
  }
  debug: {
    enabled: boolean
    logLevel: string
    showPerformance: boolean
  }
}

// 默认配置
const defaultConfig: StagewiseConfig = {
  enabled: process.env.NODE_ENV === 'development',
  toolbar: {
    position: 'bottom-right',
    theme: 'dark',
    size: 'medium',
    autoHide: false,
    zIndex: 9999,
  },
  project: {
    name: '营业厅语音对讲系统',
    framework: 'vue',
    version: '1.0.0',
    description: '基于uni-app的语音对讲系统前端',
  },
  ai: {
    enabled: true,
    provider: 'default',
    model: 'gpt-4',
    contextWindow: 4000,
  },
  selector: {
    highlightColor: '#667eea',
    borderWidth: 2,
    showTooltip: true,
    excludeSelectors: [
      '.stagewise-toolbar',
      '.stagewise-overlay',
      'script',
      'style',
      'meta',
      'link'
    ],
  },
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    logLevel: 'info',
    showPerformance: true,
  },
}

// Stagewise实例
let stagewiseInstance: any = null

/**
 * 初始化Stagewise工具栏
 */
export async function initStagewise(config: Partial<StagewiseConfig> = {}): Promise<void> {
  return runDevTools(async () => {
    try {
      // 检查环境
      if (!shouldEnableDevTools()) {
        console.warn('Stagewise: 环境不支持，跳过初始化')
        return
      }

    // 合并配置
    const finalConfig = { ...defaultConfig, ...config }

    if (!finalConfig.enabled) {
      console.log('Stagewise: 已禁用，跳过初始化')
      return
    }

    // 等待DOM加载完成
    await waitForDOM()

    // 创建Stagewise工具栏
    stagewiseInstance = createStagewiseToolbar({
      // 基本配置
      enabled: finalConfig.enabled,
      
      // 工具栏配置
      toolbar: finalConfig.toolbar,
      
      // 项目信息
      project: finalConfig.project,
      
      // AI配置
      ai: finalConfig.ai,
      
      // 选择器配置
      selector: finalConfig.selector,
      
      // 调试配置
      debug: finalConfig.debug,
      
      // Vue特定配置
      vue: {
        detectComponents: true, // 检测Vue组件
        showComponentTree: true, // 显示组件树
        trackReactivity: true, // 跟踪响应式数据
        devtools: true, // 集成Vue开发工具
      },
      
      // 插件配置
      plugins: [
        VuePlugin({
          // Vue插件特定配置
          componentInspection: true,
          propsInspection: true,
          stateInspection: true,
          eventsInspection: true,
        })
      ],
      
      // 回调函数
      onReady: () => {
        console.log('🎯 Stagewise工具栏已就绪')
        
        // 添加自定义样式
        addCustomStyles()
        
        // 设置快捷键
        setupShortcuts()
      },
      
      onElementSelected: (element: HTMLElement, context: any) => {
        console.log('🎯 元素已选择:', element, context)
        
        // 获取Vue组件信息
        const vueInfo = getVueComponentInfo(element)
        if (vueInfo) {
          console.log('📦 Vue组件信息:', vueInfo)
        }
      },
      
      onCommentAdded: (comment: any) => {
        console.log('💬 注释已添加:', comment)
      },
      
      onCodeGenerated: (code: string, context: any) => {
        console.log('🔧 代码已生成:', code, context)
      },
      
      onError: (error: Error) => {
        console.error('❌ Stagewise错误:', error)
      },
    })

    // 初始化插件
    await stagewiseInstance.init()

    console.log('✅ Stagewise初始化完成')

    } catch (error) {
      console.error('❌ Stagewise初始化失败:', error)
    }
  }) || Promise.resolve()
}

/**
 * 获取Vue组件信息
 */
function getVueComponentInfo(element: HTMLElement): any {
  try {
    // 尝试获取Vue实例
    const vueInstance = (element as any).__vueParentComponent || (element as any).__vue__
    
    if (vueInstance) {
      return {
        componentName: vueInstance.type?.name || vueInstance.type?.__name || 'Unknown',
        props: vueInstance.props || {},
        data: vueInstance.data || {},
        computed: vueInstance.computed || {},
        methods: Object.getOwnPropertyNames(vueInstance).filter(key => 
          typeof vueInstance[key] === 'function'
        ),
      }
    }
    
    return null
  } catch (error) {
    console.warn('获取Vue组件信息失败:', error)
    return null
  }
}

/**
 * 添加自定义样式
 */
function addCustomStyles(): void {
  const style = document.createElement('style')
  style.textContent = `
    /* Stagewise自定义样式 */
    .stagewise-toolbar {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      border-radius: 8px;
      backdrop-filter: blur(10px);
    }
    
    .stagewise-highlight {
      outline: 2px solid #667eea !important;
      outline-offset: 2px;
      background-color: rgba(102, 126, 234, 0.1) !important;
    }
    
    .stagewise-tooltip {
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 10000;
    }
  `
  document.head.appendChild(style)
}

/**
 * 设置快捷键
 */
function setupShortcuts(): void {
  document.addEventListener('keydown', (event) => {
    // Ctrl+Shift+S: 切换工具栏
    if (event.ctrlKey && event.shiftKey && event.key === 'S') {
      event.preventDefault()
      stagewiseInstance?.toggle()
    }
    
    // Ctrl+Shift+C: 添加注释模式
    if (event.ctrlKey && event.shiftKey && event.key === 'C') {
      event.preventDefault()
      stagewiseInstance?.enterCommentMode()
    }
    
    // Escape: 退出当前模式
    if (event.key === 'Escape') {
      stagewiseInstance?.exitCurrentMode()
    }
  })
}

/**
 * 销毁Stagewise实例
 */
export function destroyStagewise(): void {
  if (stagewiseInstance) {
    stagewiseInstance.destroy()
    stagewiseInstance = null
    console.log('🗑️ Stagewise已销毁')
  }
}

/**
 * 获取Stagewise实例
 */
export function getStagewiseInstance(): any {
  return stagewiseInstance
}

// 在页面卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', destroyStagewise)
}

{"name": "talking-app", "version": "1.0.0", "description": "营业厅语音对讲系统", "main": "main.js", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni -p h5", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "serve": "npm run dev:h5", "lint": "eslint --ext .js,.vue --ignore-path .gitignore .", "lint:fix": "eslint --ext .js,.vue --ignore-path .gitignore . --fix", "type-check": "vue-tsc --noEmit", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:android": "cross-env UNI_PLATFORM=app UNI_OS_NAME=android jest -i", "test:ios": "cross-env UNI_PLATFORM=app UNI_OS_NAME=ios jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:unit": "jest --testPathPattern=unit --testEnvironment=node", "test:components": "jest --testPathPattern=components --testEnvironment=node", "test:all": "npm run test:unit && npm run test:components", "test:h5:dev": "npm run dev:h5 & sleep 10 && npm run test:h5", "test:coverage": "jest --coverage --testEnvironment=node"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "pinia": "^2.2.6", "socket.io-client": "^4.8.1", "vue": "3.5.17", "vue-i18n": "9.14.4"}, "devDependencies": {"@dcloudio/types": "3.4.15", "@dcloudio/uni-automator": "^3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vue/eslint-config-typescript": "^13.0.0", "@vue/runtime-core": "3.5.17", "adbkit": "^2.11.1", "cross-env": "^7.0.3", "eslint": "^9.30.1", "eslint-plugin-vue": "^10.3.0", "jest": "^27.0.4", "jest-environment-node": "^27.5.1", "node-simctl": "^7.7.5", "playwright": "^1.53.2", "prettier": "^3.3.3", "puppeteer": "^14.0.0", "sass": "^1.79.6", "typescript": "^5.6.3", "vite": "^5.4.0", "vue-tsc": "^3.0.1"}, "uni-app": {"scripts": {}}}
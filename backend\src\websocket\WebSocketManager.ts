/**
 * WebSocket管理器
 * 
 * 功能说明：
 * 1. Socket.io服务器管理
 * 2. 连接认证和管理
 * 3. 事件处理和路由
 * 4. 用户状态同步
 * 5. 实时消息传输
 * 6. 语音数据流处理
 */

import { Server as SocketIOServer, Socket } from 'socket.io'
import { log as logger } from '@/utils/logger'
import { config } from '@/config/config'
import { authenticateSocket } from '@/middleware/auth'
import { validateWebSocketMessage, validateVoiceData } from '@/middleware/validation'

// 用户连接信息接口
interface UserConnection {
  socketId: string
  userId: number
  username: string
  displayName?: string
  status: 'online' | 'talking' | 'busy'
  connectedAt: Date
  lastActivity: Date
}

// 通话会话接口
interface CallSession {
  sessionId: string
  user1: UserConnection
  user2: UserConnection
  startTime: Date
  status: 'connecting' | 'active' | 'ended'
}

export class WebSocketManager {
  private io: SocketIOServer
  private connections: Map<string, UserConnection> = new Map()
  private userSockets: Map<number, string> = new Map() // userId -> socketId
  private callSessions: Map<string, CallSession> = new Map()
  private heartbeatInterval: NodeJS.Timeout | null = null

  constructor(io: SocketIOServer) {
    this.io = io
  }

  /**
   * 初始化WebSocket服务器
   */
  public initialize(): void {
    // 设置认证中间件
    this.io.use(authenticateSocket)

    // 连接事件处理
    this.io.on('connection', (socket: Socket) => {
      this.handleConnection(socket)
    })

    // 启动心跳检测
    this.startHeartbeat()

    logger.info('WebSocket server initialized')
  }

  /**
   * 处理新连接
   */
  private handleConnection(socket: Socket): void {
    const userId = (socket as any).userId
    const username = (socket as any).username
    const displayName = (socket as any).displayName

    // 创建用户连接信息
    const userConnection: UserConnection = {
      socketId: socket.id,
      userId,
      username,
      displayName,
      status: 'online',
      connectedAt: new Date(),
      lastActivity: new Date()
    }

    // 存储连接信息
    this.connections.set(socket.id, userConnection)
    this.userSockets.set(userId, socket.id)

    logger.websocket('connected', socket.id, userId.toString(), {
      username,
      displayName
    })

    // 广播用户上线
    this.broadcastUserStatusChange(userConnection, 'online')

    // 发送在线用户列表
    this.sendOnlineUsersList(socket)

    // 设置事件监听器
    this.setupEventListeners(socket)

    // 处理断开连接（使用最佳实践）
    socket.on('disconnect', (reason, details) => {
      this.handleDisconnection(socket, reason, details)
    })

    // 处理连接错误
    socket.on('error', (error) => {
      logger.error('Socket error:', error)
      this.handleSocketError(socket, error)
    })
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(socket: Socket): void {
    // 心跳响应
    socket.on('heartbeat', (data) => {
      this.handleHeartbeat(socket, data)
    })

    // 用户状态变更
    socket.on('user-status-change', (data) => {
      this.handleUserStatusChange(socket, data)
    })

    // 语音通话开始（支持确认）
    socket.on('voice-start', (data, ack) => {
      this.handleVoiceStart(socket, data, ack)
    })

    // 语音数据传输
    socket.on('voice-data', (data) => {
      this.handleVoiceData(socket, data)
    })

    // 语音通话结束
    socket.on('voice-end', (data) => {
      this.handleVoiceEnd(socket, data)
    })

    // 文本消息发送
    socket.on('message-send', (data) => {
      this.handleMessageSend(socket, data)
    })

    // 后台管理消息
    socket.on('backend-call', (data) => {
      this.handleBackendCall(socket, data)
    })

    // 测试消息处理
    socket.on('test-message', (data) => {
      this.handleTestMessage(socket, data)
    })

    // 错误处理
    socket.on('error', (error) => {
      logger.error('WebSocket error', error, {
        socketId: socket.id,
        type: 'websocket_error'
      })
    })
  }

  /**
   * 处理心跳
   */
  private handleHeartbeat(socket: Socket, _data: any): void {
    const connection = this.connections.get(socket.id)
    if (connection) {
      connection.lastActivity = new Date()
      socket.emit('heartbeat-response', {
        timestamp: Date.now(),
        status: 'ok'
      })
    }
  }

  /**
   * 处理用户状态变更
   */
  private handleUserStatusChange(socket: Socket, data: any): void {
    const validation = validateWebSocketMessage(data)
    if (!validation.isValid) {
      socket.emit('error', { message: validation.error })
      return
    }

    const connection = this.connections.get(socket.id)
    if (!connection) return

    const newStatus = data.status
    if (['online', 'talking', 'busy'].includes(newStatus)) {
      connection.status = newStatus
      connection.lastActivity = new Date()

      // 广播状态变更
      this.broadcastUserStatusChange(connection, newStatus)

      logger.websocket('status-change', socket.id, connection.userId.toString(), {
        newStatus,
        username: connection.username
      })
    }
  }

  /**
   * 处理语音通话开始（支持确认）
   */
  private handleVoiceStart(socket: Socket, data: any, ack?: Function): void {
    const validation = validateWebSocketMessage(data)
    if (!validation.isValid) {
      const error = { message: validation.error, code: 'VALIDATION_ERROR' }
      if (ack) {
        ack(error) // 使用确认回调返回错误
      } else {
        socket.emit('error', error)
      }
      return
    }

    const connection = this.connections.get(socket.id)
    if (!connection) {
      const error = { message: 'Connection not found', code: 'CONNECTION_ERROR' }
      if (ack) {
        ack(error)
      } else {
        socket.emit('error', error)
      }
      return
    }

    const targetUserId = data.targetUserId
    const targetSocketId = this.userSockets.get(targetUserId)

    if (!targetSocketId) {
      const error = {
        reason: 'Target user not online',
        code: 'USER_OFFLINE'
      }
      if (ack) {
        ack(error) // 使用确认回调返回错误
      } else {
        socket.emit('voice-start-failed', error)
      }
      return
    }

    const targetConnection = this.connections.get(targetSocketId)
    if (!targetConnection || targetConnection.status !== 'online') {
      socket.emit('voice-start-failed', { 
        reason: 'Target user not available',
        code: 'USER_NOT_AVAILABLE'
      })
      return
    }

    // 创建通话会话
    const sessionId = this.generateSessionId()
    const callSession: CallSession = {
      sessionId,
      user1: connection,
      user2: targetConnection,
      startTime: new Date(),
      status: 'connecting'
    }

    this.callSessions.set(sessionId, callSession)

    // 更新用户状态
    connection.status = 'talking'
    targetConnection.status = 'talking'

    // 通知双方用户
    socket.emit('voice-start-success', {
      sessionId,
      targetUser: {
        id: targetConnection.userId,
        username: targetConnection.username,
        displayName: targetConnection.displayName
      }
    })

    this.io.to(targetSocketId).emit('voice-call-incoming', {
      sessionId,
      fromUser: {
        id: connection.userId,
        username: connection.username,
        displayName: connection.displayName
      }
    })

    // 广播状态变更
    this.broadcastUserStatusChange(connection, 'talking')
    this.broadcastUserStatusChange(targetConnection, 'talking')

    logger.websocket('voice-start', socket.id, connection.userId.toString(), {
      sessionId,
      targetUserId,
      targetUsername: targetConnection.username
    })
  }

  /**
   * 处理语音数据传输
   */
  private handleVoiceData(socket: Socket, data: any): void {
    const voiceValidation = validateVoiceData(data)
    if (!voiceValidation.isValid) {
      socket.emit('error', { message: voiceValidation.error })
      return
    }

    const connection = this.connections.get(socket.id)
    if (!connection || connection.status !== 'talking') return

    const sessionId = data.sessionId
    const callSession = this.callSessions.get(sessionId)
    if (!callSession || callSession.status !== 'active') return

    // 确定目标用户
    const targetConnection = callSession.user1.socketId === socket.id 
      ? callSession.user2 
      : callSession.user1

    // 转发语音数据
    this.io.to(targetConnection.socketId).emit('voice-data', {
      sessionId,
      audioData: data.audioData,
      timestamp: Date.now(),
      fromUserId: connection.userId
    })

    // 更新活动时间
    connection.lastActivity = new Date()
  }

  /**
   * 处理语音通话结束
   */
  private handleVoiceEnd(socket: Socket, data: any): void {
    const connection = this.connections.get(socket.id)
    if (!connection) return

    const sessionId = data.sessionId
    const callSession = this.callSessions.get(sessionId)
    if (!callSession) return

    // 结束通话会话
    callSession.status = 'ended'
    
    // 恢复用户状态
    callSession.user1.status = 'online'
    callSession.user2.status = 'online'

    // 通知双方用户
    this.io.to(callSession.user1.socketId).emit('voice-end', {
      sessionId,
      reason: data.reason || 'normal',
      duration: Date.now() - callSession.startTime.getTime()
    })

    this.io.to(callSession.user2.socketId).emit('voice-end', {
      sessionId,
      reason: data.reason || 'normal',
      duration: Date.now() - callSession.startTime.getTime()
    })

    // 广播状态变更
    this.broadcastUserStatusChange(callSession.user1, 'online')
    this.broadcastUserStatusChange(callSession.user2, 'online')

    // 清理会话
    this.callSessions.delete(sessionId)

    logger.websocket('voice-end', socket.id, connection.userId.toString(), {
      sessionId,
      duration: Date.now() - callSession.startTime.getTime()
    })
  }

  /**
   * 处理文本消息发送
   */
  private handleMessageSend(socket: Socket, data: any): void {
    const validation = validateWebSocketMessage(data)
    if (!validation.isValid) {
      socket.emit('error', { message: validation.error })
      return
    }

    const connection = this.connections.get(socket.id)
    if (!connection) return

    const targetUserId = data.targetUserId
    const targetSocketId = this.userSockets.get(targetUserId)

    if (targetSocketId) {
      // 转发消息给目标用户
      this.io.to(targetSocketId).emit('message-receive', {
        messageId: data.messageId || this.generateMessageId(),
        content: data.content,
        fromUser: {
          id: connection.userId,
          username: connection.username,
          displayName: connection.displayName
        },
        timestamp: Date.now()
      })
    }

    // 确认消息发送
    socket.emit('message-sent', {
      messageId: data.messageId,
      timestamp: Date.now()
    })

    connection.lastActivity = new Date()
  }

  /**
   * 处理后台管理消息
   */
  private handleBackendCall(socket: Socket, data: any): void {
    // 广播给所有在线用户
    this.io.emit('backend-call', {
      id: data.id || this.generateMessageId(),
      message: data.message,
      priority: data.priority || 'normal',
      timestamp: Date.now(),
      from: 'backend'
    })

    logger.websocket('backend-call', socket.id, 'system', {
      message: data.message,
      priority: data.priority
    })
  }

  /**
   * 处理测试消息
   */
  private handleTestMessage(socket: Socket, data: any): void {
    const connection = this.connections.get(socket.id)
    if (!connection) return

    // 记录测试消息
    logger.info('Test message received', {
      socketId: socket.id,
      username: connection.username,
      message: data.message,
      timestamp: data.timestamp
    })

    // 回复测试消息确认
    socket.emit('test-message-response', {
      success: true,
      message: '测试消息已收到',
      originalData: data,
      serverTimestamp: Date.now(),
      connectionInfo: {
        username: connection.username,
        connectedAt: connection.connectedAt,
        status: connection.status
      }
    })

    // 更新用户活动时间
    connection.lastActivity = new Date()

    logger.websocket('test-message', socket.id, connection.username, {
      message: data.message,
      responseTime: Date.now() - (data.timestamp || Date.now())
    })
  }

  /**
   * 处理断开连接（使用最佳实践）
   */
  private handleDisconnection(socket: Socket, reason: string, details?: any): void {
    const connection = this.connections.get(socket.id)
    if (!connection) return

    // 记录详细的断开信息
    const disconnectionInfo = {
      username: connection.username,
      reason,
      details: details || {},
      duration: Date.now() - connection.connectedAt.getTime(),
      recovered: (socket as any).recovered || false
    }

    // 根据断开原因进行不同处理
    if (reason === 'transport error' && details) {
      logger.error('Transport error during disconnection:', details)
    } else if (reason === 'client namespace disconnect') {
      logger.info('Client initiated disconnect')
    } else if (reason === 'server namespace disconnect') {
      logger.info('Server initiated disconnect')
    }

    // 清理连接信息
    this.connections.delete(socket.id)
    this.userSockets.delete(connection.userId)

    // 处理正在进行的通话
    this.handleDisconnectionCallCleanup(connection)

    // 广播用户离线（除非是临时断开且支持恢复）
    if (reason !== 'transport close' && reason !== 'transport error') {
      this.broadcastUserStatusChange(connection, 'offline')
    }

    logger.websocket('disconnected', socket.id, connection.userId.toString(), disconnectionInfo)
  }

  /**
   * 处理Socket错误
   */
  private handleSocketError(socket: Socket, error: Error): void {
    const connection = this.connections.get(socket.id)
    logger.error('Socket error:', {
      socketId: socket.id,
      userId: connection?.userId,
      username: connection?.username,
      error: error.message,
      stack: error.stack
    })
  }

  /**
   * 处理断线时的通话清理
   */
  private handleDisconnectionCallCleanup(connection: UserConnection): void {
    // 查找相关的通话会话
    for (const [sessionId, callSession] of this.callSessions.entries()) {
      if (callSession.user1.socketId === connection.socketId || 
          callSession.user2.socketId === connection.socketId) {
        
        // 通知另一方用户通话结束
        const otherUser = callSession.user1.socketId === connection.socketId 
          ? callSession.user2 
          : callSession.user1

        this.io.to(otherUser.socketId).emit('voice-end', {
          sessionId,
          reason: 'peer_disconnected',
          duration: Date.now() - callSession.startTime.getTime()
        })

        // 恢复另一方用户状态
        otherUser.status = 'online'
        this.broadcastUserStatusChange(otherUser, 'online')

        // 清理会话
        this.callSessions.delete(sessionId)
        break
      }
    }
  }

  /**
   * 广播用户状态变更
   */
  private broadcastUserStatusChange(connection: UserConnection, status: string): void {
    this.io.emit('user-status-change', {
      userId: connection.userId,
      username: connection.username,
      displayName: connection.displayName,
      status,
      timestamp: Date.now()
    })

    // 发送更新的在线用户列表
    this.broadcastOnlineUsersList()
  }

  /**
   * 发送在线用户列表
   */
  private sendOnlineUsersList(socket: Socket): void {
    const onlineUsers = Array.from(this.connections.values()).map(conn => ({
      id: conn.userId,
      username: conn.username,
      displayName: conn.displayName,
      status: conn.status,
      lastActiveTime: conn.lastActivity.getTime()
    }))

    socket.emit('user-list-update', {
      users: onlineUsers,
      timestamp: Date.now()
    })
  }

  /**
   * 广播在线用户列表
   */
  private broadcastOnlineUsersList(): void {
    const onlineUsers = Array.from(this.connections.values()).map(conn => ({
      id: conn.userId,
      username: conn.username,
      displayName: conn.displayName,
      status: conn.status,
      lastActiveTime: conn.lastActivity.getTime()
    }))

    this.io.emit('user-list-update', {
      users: onlineUsers,
      timestamp: Date.now()
    })
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = new Date()
      const timeout = config.system.userTimeout

      // 检查超时的连接
      for (const [socketId, connection] of this.connections.entries()) {
        const timeSinceLastActivity = now.getTime() - connection.lastActivity.getTime()
        
        if (timeSinceLastActivity > timeout) {
          logger.warn('Connection timeout', {
            socketId,
            userId: connection.userId,
            username: connection.username,
            timeSinceLastActivity
          })

          // 强制断开超时连接
          const socket = this.io.sockets.sockets.get(socketId)
          if (socket) {
            socket.disconnect(true)
          }
        }
      }
    }, config.system.heartbeatInterval)
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 获取连接统计信息
   */
  public getStats(): any {
    return {
      totalConnections: this.connections.size,
      activeCalls: this.callSessions.size,
      onlineUsers: Array.from(this.connections.values()).filter(conn => conn.status === 'online').length,
      talkingUsers: Array.from(this.connections.values()).filter(conn => conn.status === 'talking').length
    }
  }

  /**
   * 关闭WebSocket服务器
   */
  public shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    // 通知所有客户端服务器即将关闭
    this.io.emit('server-shutdown', {
      message: 'Server is shutting down',
      timestamp: Date.now()
    })

    // 关闭所有连接
    this.io.close()

    logger.info('WebSocket server shutdown completed')
  }
}

#!/usr/bin/env node

/**
 * 认证修复验证脚本
 * 用于测试前端登录和Socket.io连接是否正常工作
 */

const http = require('http');
const { io } = require('socket.io-client');

console.log('🔍 开始验证认证修复效果...\n');

// 测试配置
const API_BASE = 'http://localhost:3001';
const SOCKET_URL = 'http://localhost:3001';
const TEST_USERNAME = 'testuser';

/**
 * 测试后端API健康状态
 */
async function testHealthCheck() {
  return new Promise((resolve, reject) => {
    console.log('1️⃣ 检查后端服务状态...');
    
    const req = http.get(`${API_BASE}/health`, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ 后端服务正常运行');
          console.log(`   响应: ${JSON.parse(data).status}\n`);
          resolve(true);
        } else {
          console.log(`❌ 后端服务异常 (状态码: ${res.statusCode})\n`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ 无法连接到后端服务: ${error.message}\n`);
      resolve(false);
    });

    req.setTimeout(5000, () => {
      console.log('❌ 后端服务连接超时\n');
      req.destroy();
      resolve(false);
    });
  });
}

/**
 * 测试登录API
 */
async function testLoginAPI() {
  return new Promise((resolve, reject) => {
    console.log('2️⃣ 测试登录API...');
    
    const postData = JSON.stringify({
      username: TEST_USERNAME
    });

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200 && response.success) {
            console.log('✅ 登录API正常工作');
            console.log(`   用户ID: ${response.data.user.id}`);
            console.log(`   Token长度: ${response.data.token.length} 字符`);
            console.log(`   Token前缀: ${response.data.token.substring(0, 20)}...\n`);
            resolve(response.data.token);
          } else {
            console.log(`❌ 登录API失败: ${response.message || '未知错误'}\n`);
            resolve(null);
          }
        } catch (error) {
          console.log(`❌ 登录API响应解析失败: ${error.message}\n`);
          resolve(null);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ 登录API请求失败: ${error.message}\n`);
      resolve(null);
    });

    req.setTimeout(5000, () => {
      console.log('❌ 登录API请求超时\n');
      req.destroy();
      resolve(null);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * 测试Socket.io连接和认证
 */
async function testSocketConnection(token) {
  return new Promise((resolve, reject) => {
    console.log('3️⃣ 测试Socket.io连接和认证...');
    
    if (!token) {
      console.log('❌ 无法测试Socket.io连接：缺少有效token\n');
      resolve(false);
      return;
    }

    const socket = io(SOCKET_URL, {
      auth: { token },
      timeout: 10000,
      transports: ['websocket', 'polling']
    });

    let resolved = false;

    socket.on('connect', () => {
      if (!resolved) {
        console.log('✅ Socket.io连接成功');
        console.log(`   Socket ID: ${socket.id}`);
        console.log(`   传输方式: ${socket.io.engine.transport.name}\n`);
        resolved = true;
        socket.disconnect();
        resolve(true);
      }
    });

    socket.on('connect_error', (error) => {
      if (!resolved) {
        console.log(`❌ Socket.io连接失败: ${error.message}`);
        if (error.message.includes('Authentication')) {
          console.log('   原因: JWT token认证失败');
        } else if (error.message.includes('timeout')) {
          console.log('   原因: 连接超时');
        }
        console.log('');
        resolved = true;
        resolve(false);
      }
    });

    // 超时处理
    setTimeout(() => {
      if (!resolved) {
        console.log('❌ Socket.io连接测试超时\n');
        resolved = true;
        socket.disconnect();
        resolve(false);
      }
    }, 10000);
  });
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🎯 营业厅语音对讲系统 - 认证修复验证\n');
  console.log('=' .repeat(50));
  
  try {
    // 1. 检查后端服务
    const healthOk = await testHealthCheck();
    if (!healthOk) {
      console.log('💡 请先启动后端服务：');
      console.log('   cd backend && npm run dev\n');
      return;
    }

    // 2. 测试登录API
    const token = await testLoginAPI();
    if (!token) {
      console.log('💡 登录API异常，请检查：');
      console.log('   - 数据库是否初始化');
      console.log('   - JWT密钥是否配置');
      console.log('   - 认证路由是否正确\n');
      return;
    }

    // 3. 测试Socket.io连接
    const socketOk = await testSocketConnection(token);
    
    // 4. 输出总结
    console.log('=' .repeat(50));
    if (healthOk && token && socketOk) {
      console.log('🎉 所有测试通过！认证修复成功！');
      console.log('');
      console.log('✅ 后端服务正常');
      console.log('✅ 登录API工作正常');
      console.log('✅ JWT token生成正确');
      console.log('✅ Socket.io认证成功');
      console.log('');
      console.log('🚀 现在可以正常使用前端应用了！');
    } else {
      console.log('⚠️  部分测试失败，请检查上述错误信息');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
  
  console.log('\n' + '=' .repeat(50));
}

// 运行测试
runTests().catch(console.error);

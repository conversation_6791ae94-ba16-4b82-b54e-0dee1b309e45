/**
 * Stagewise类型定义
 * 
 * 为Stagewise工具栏提供TypeScript类型支持
 */

declare module '@stagewise/toolbar' {
  export interface ToolbarConfig {
    plugins?: ToolbarPluginLoader[]
    experimental?: {
      mcpServer?: boolean
    }
  }

  export interface ToolbarPluginLoader {
    // Plugin loader interface
  }

  export function initToolbar(config?: ToolbarConfig): void
}

declare module '@stagewise-plugins/vue' {
  import type { ToolbarPluginLoader } from '@stagewise/toolbar'

  const plugin: ToolbarPluginLoader
  export default plugin
}

// 全局类型扩展
declare global {
  interface Window {
    __STAGEWISE__?: {
      instance?: any
      config?: any
      debug?: boolean
    }
  }

  interface HTMLElement {
    __vueParentComponent?: any
    __vue__?: any
  }
}

export {}

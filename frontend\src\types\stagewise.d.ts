/**
 * Stagewise类型定义
 * 
 * 为Stagewise工具栏提供TypeScript类型支持
 */

declare module '@stagewise/toolbar-vue' {
  export interface StagewiseToolbarConfig {
    enabled?: boolean
    toolbar?: {
      position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
      theme?: 'light' | 'dark' | 'auto'
      size?: 'small' | 'medium' | 'large'
      autoHide?: boolean
      zIndex?: number
    }
    project?: {
      name?: string
      framework?: string
      version?: string
      description?: string
    }
    ai?: {
      enabled?: boolean
      provider?: string
      model?: string
      contextWindow?: number
    }
    selector?: {
      highlightColor?: string
      borderWidth?: number
      showTooltip?: boolean
      excludeSelectors?: string[]
    }
    debug?: {
      enabled?: boolean
      logLevel?: 'error' | 'warn' | 'info' | 'debug'
      showPerformance?: boolean
    }
    vue?: {
      detectComponents?: boolean
      showComponentTree?: boolean
      trackReactivity?: boolean
      devtools?: boolean
    }
    plugins?: any[]
    onReady?: () => void
    onElementSelected?: (element: HTMLElement, context: any) => void
    onCommentAdded?: (comment: any) => void
    onCodeGenerated?: (code: string, context: any) => void
    onError?: (error: Error) => void
  }

  export interface StagewiseInstance {
    init(): Promise<void>
    destroy(): void
    toggle(): void
    enterCommentMode(): void
    exitCurrentMode(): void
    selectElement(element: HTMLElement): void
    addComment(text: string, element?: HTMLElement): void
    generateCode(prompt: string, context?: any): Promise<string>
  }

  export function createStagewiseToolbar(config: StagewiseToolbarConfig): StagewiseInstance
}

declare module '@stagewise-plugins/vue' {
  export interface VuePluginConfig {
    componentInspection?: boolean
    propsInspection?: boolean
    stateInspection?: boolean
    eventsInspection?: boolean
  }

  export function VuePlugin(config?: VuePluginConfig): any
}

// 全局类型扩展
declare global {
  interface Window {
    __STAGEWISE__?: {
      instance?: any
      config?: any
      debug?: boolean
    }
  }

  interface HTMLElement {
    __vueParentComponent?: any
    __vue__?: any
  }
}

export {}

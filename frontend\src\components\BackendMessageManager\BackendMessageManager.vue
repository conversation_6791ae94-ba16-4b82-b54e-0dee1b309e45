<!--
  后台消息管理组件
  
  功能说明：
  1. 实时接收后台管理消息
  2. 消息优先级处理和显示
  3. 消息叠加显示（通话中）
  4. 消息提醒和通知
  5. 消息历史管理
-->

<template>
  <view class="backend-message-manager">
    <!-- 消息叠加显示区域（通话中使用） -->
    <view 
      class="overlay-message" 
      v-if="overlayMessage && showOverlay"
      :class="overlayMessageClass"
    >
      <view class="overlay-header">
        <text class="overlay-title">{{ overlayMessage.title }}</text>
        <text class="overlay-priority">{{ priorityText }}</text>
      </view>
      <view class="overlay-content">
        <text class="overlay-text">{{ overlayMessage.content }}</text>
      </view>
      <view class="overlay-actions">
        <button 
          class="overlay-btn primary" 
          @click="handleOverlayConfirm"
          v-if="overlayMessage.priority === 'urgent'"
        >
          确认
        </button>
        <button 
          class="overlay-btn secondary" 
          @click="hideOverlayMessage"
        >
          关闭
        </button>
      </view>
    </view>

    <!-- 消息通知弹窗 -->
    <view 
      class="message-notification" 
      v-if="notification && showNotification"
      :class="notificationClass"
    >
      <view class="notification-icon">
        <text class="icon">📢</text>
      </view>
      <view class="notification-content">
        <text class="notification-title">{{ notification.title }}</text>
        <text class="notification-text">{{ notification.content }}</text>
      </view>
      <view class="notification-actions">
        <button class="notification-btn" @click="hideNotification">
          知道了
        </button>
      </view>
    </view>

    <!-- 消息列表（历史消息） -->
    <view class="message-list" v-if="showMessageList">
      <view class="list-header">
        <text class="list-title">后台消息</text>
        <button class="clear-btn" @click="clearAllMessages">清空</button>
      </view>
      <scroll-view class="list-content" scroll-y>
        <MessageCard
          v-for="message in messages"
          :key="message.id"
          :id="message.id"
          :type="message.type"
          :title="message.title"
          :content="message.content"
          :timestamp="message.timestamp"
          :status="message.status"
          :is-read="message.isRead"
          :show-actions="true"
          :show-reply-button="false"
          :show-mark-read-button="true"
          :show-delete-button="true"
          @click="handleMessageClick"
          @mark-read="handleMarkRead"
          @delete="handleDeleteMessage"
        />
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { MessageCard } from '@/components'
import { useSocketStore } from '@/stores/socket'
import { MessageStatus } from '@/types'

// 消息类型枚举（为了与MessageCard组件保持一致）
enum MessageType {
  BACKEND_CALL = 'backend-call',    // 后台呼叫消息
  SYSTEM = 'system',                // 系统消息
  USER = 'user',                    // 用户消息
  NOTIFICATION = 'notification'     // 通知消息
}

// Props定义
interface Props {
  showOverlay?: boolean        // 是否显示叠加消息
  showNotification?: boolean   // 是否显示通知弹窗
  showMessageList?: boolean    // 是否显示消息列表
  maxMessages?: number         // 最大消息数量
}

const props = withDefaults(defineProps<Props>(), {
  showOverlay: true,
  showNotification: true,
  showMessageList: false,
  maxMessages: 50
})

// Events定义
interface Emits {
  (e: 'message-received', message: any): void
  (e: 'urgent-message', message: any): void
  (e: 'message-confirmed', messageId: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const messages = ref<any[]>([])
const overlayMessage = ref<any>(null)
const notification = ref<any>(null)
const overlayTimer = ref<any>(null)
const notificationTimer = ref<any>(null)

// 计算属性
const overlayMessageClass = computed(() => {
  if (!overlayMessage.value) return ''
  
  return {
    'urgent': overlayMessage.value.priority === 'urgent',
    'high': overlayMessage.value.priority === 'high',
    'normal': overlayMessage.value.priority === 'normal',
    'low': overlayMessage.value.priority === 'low'
  }
})

const notificationClass = computed(() => {
  if (!notification.value) return ''
  
  return {
    'urgent': notification.value.priority === 'urgent',
    'high': notification.value.priority === 'high',
    'normal': notification.value.priority === 'normal'
  }
})

const priorityText = computed(() => {
  if (!overlayMessage.value) return ''
  
  const priorityMap: Record<string, string> = {
    'urgent': '紧急',
    'high': '重要',
    'normal': '普通',
    'low': '一般'
  }

  return priorityMap[overlayMessage.value?.priority] || '普通'
})

// 方法定义
const handleBackendMessage = (data: any) => {
  console.log('收到后台消息:', data)
  
  const message = {
    id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: MessageType.BACKEND_CALL,
    title: data.title || '后台消息',
    content: data.message || data.content,
    priority: data.priority || 'normal',
    timestamp: Date.now(),
    status: MessageStatus.UNREAD,
    isRead: false
  }
  
  // 添加到消息列表
  addMessage(message)
  
  // 根据优先级处理显示
  if (message.priority === 'urgent') {
    showUrgentMessage(message)
    emit('urgent-message', message)
  } else if (props.showOverlay) {
    showOverlayMessage(message)
  } else if (props.showNotification) {
    showNotificationMessage(message)
  }
  
  emit('message-received', message)
}

const addMessage = (message: any) => {
  messages.value.unshift(message)
  
  // 限制消息数量
  if (messages.value.length > props.maxMessages) {
    messages.value = messages.value.slice(0, props.maxMessages)
  }
}

const showOverlayMessage = (message: any) => {
  overlayMessage.value = message
  
  // 自动隐藏（非紧急消息）
  if (message.priority !== 'urgent') {
    clearTimeout(overlayTimer.value)
    overlayTimer.value = setTimeout(() => {
      hideOverlayMessage()
    }, 5000)
  }
}

const hideOverlayMessage = () => {
  overlayMessage.value = null
  clearTimeout(overlayTimer.value)
}

const showUrgentMessage = (message: any) => {
  // 紧急消息强制显示叠加层
  showOverlayMessage(message)
  
  // 播放提示音
  playNotificationSound()
}

const showNotificationMessage = (message: any) => {
  notification.value = message
  
  // 自动隐藏
  clearTimeout(notificationTimer.value)
  notificationTimer.value = setTimeout(() => {
    hideNotification()
  }, 3000)
  
  // 播放提示音
  playNotificationSound()
}

const hideNotification = () => {
  notification.value = null
  clearTimeout(notificationTimer.value)
}

const handleOverlayConfirm = () => {
  if (overlayMessage.value) {
    emit('message-confirmed', overlayMessage.value.id)
    markMessageAsRead(overlayMessage.value.id)
  }
  hideOverlayMessage()
}

const handleMessageClick = (messageProps: any) => {
  const messageId = messageProps.id || messageProps
  const message = messages.value.find(m => m.id === messageId)
  if (message) {
    showOverlayMessage(message)
  }
}

const handleMarkRead = (messageId: string) => {
  markMessageAsRead(messageId)
}

const markMessageAsRead = (messageId: string) => {
  const message = messages.value.find(m => m.id === messageId)
  if (message) {
    message.isRead = true
    message.status = MessageStatus.READ
  }
}

const handleDeleteMessage = (messageId: string) => {
  const index = messages.value.findIndex(m => m.id === messageId)
  if (index > -1) {
    messages.value.splice(index, 1)
  }
}

const clearAllMessages = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有后台消息吗？',
    success: (res) => {
      if (res.confirm) {
        messages.value = []
      }
    }
  })
}

const playNotificationSound = () => {
  // 播放系统提示音
  try {
    const audio = uni.createInnerAudioContext()
    audio.src = '/static/sounds/notification.mp3' // 需要添加提示音文件
    audio.play()
    
    // 清理音频资源
    audio.onEnded(() => {
      audio.destroy()
    })
    
    audio.onError(() => {
      audio.destroy()
    })
  } catch (error) {
    console.warn('播放提示音失败:', error)
  }
}

// 使用Socket store
const socketStore = useSocketStore()

// 监听Socket store中的消息变化
watch(() => socketStore.messages, (newMessages) => {
  // 检查是否有新的后台消息
  const latestMessage = newMessages[0]
  if (latestMessage && latestMessage.type === 'backend_call') {
    handleBackendMessage(latestMessage.data)
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  // Socket store会自动处理消息监听，这里不需要额外设置
  console.log('BackendMessageManager已挂载，开始监听后台消息')
})

onUnmounted(() => {
  // 清理定时器
  clearTimeout(overlayTimer.value)
  clearTimeout(notificationTimer.value)
})

// 暴露方法给父组件
defineExpose({
  showMessage: showOverlayMessage,
  hideMessage: hideOverlayMessage,
  clearMessages: clearAllMessages,
  getMessages: () => messages.value
})
</script>

<style lang="scss" scoped>
.backend-message-manager {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 叠加消息样式 */
.overlay-message {
  position: fixed;
  top: 20px;
  left: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  padding: 16px;
  border-left: 4px solid #667eea;
  
  &.urgent {
    border-left-color: #e53e3e;
    background: #fed7d7;
    
    .overlay-title {
      color: #e53e3e;
    }
  }
  
  &.high {
    border-left-color: #f56500;
    background: #feebc8;
    
    .overlay-title {
      color: #f56500;
    }
  }
  
  &.normal {
    border-left-color: #667eea;
    background: white;
  }
  
  &.low {
    border-left-color: #68d391;
    background: #f0fff4;
  }
}

.overlay-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.overlay-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

.overlay-priority {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  background: #667eea;
  color: white;
}

.overlay-content {
  margin-bottom: 12px;
}

.overlay-text {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

.overlay-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.overlay-btn {
  padding: 6px 16px;
  border-radius: 6px;
  font-size: 14px;
  border: none;
  
  &.primary {
    background: #667eea;
    color: white;
  }
  
  &.secondary {
    background: #e2e8f0;
    color: #4a5568;
  }
}

/* 通知弹窗样式 */
.message-notification {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 9998;
  padding: 20px;
  min-width: 280px;
  max-width: 90%;
  
  &.urgent {
    border: 2px solid #e53e3e;
  }
  
  &.high {
    border: 2px solid #f56500;
  }
  
  &.normal {
    border: 2px solid #667eea;
  }
}

.notification-icon {
  text-align: center;
  margin-bottom: 12px;
  
  .icon {
    font-size: 32px;
  }
}

.notification-content {
  text-align: center;
  margin-bottom: 16px;
}

.notification-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.notification-text {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

.notification-actions {
  text-align: center;
}

.notification-btn {
  padding: 8px 24px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}

/* 消息列表样式 */
.message-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.list-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

.clear-btn {
  padding: 4px 12px;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 4px;
  font-size: 12px;
}

.list-content {
  max-height: 400px;
  padding: 8px;
}
</style>

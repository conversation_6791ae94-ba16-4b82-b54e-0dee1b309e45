import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { io, Socket } from 'socket.io-client'
import type { User } from '@/types'
import { useUserStore } from './user'

// 通话状态类型
export type CallStatus = 'idle' | 'calling' | 'connected' | 'ended'

// Socket消息接口
export interface SocketMessage {
  id?: string
  type: string
  data: any
  timestamp: Date
}

/**
 * Socket Store - 管理WebSocket连接和实时通信状态
 * 
 * 功能说明：
 * - 管理WebSocket连接状态
 * - 处理实时消息收发
 * - 管理通话状态
 * - 处理用户在线状态
 */
export const useSocketStore = defineStore('socket', () => {
  // ===== 连接状态 =====
  const isConnected = ref(false) // WebSocket连接状态
  const reconnectAttempts = ref(0) // 重连尝试次数
  const maxReconnectAttempts = ref(5) // 最大重连次数
  
  // ===== 通话状态 =====
  const currentCall = ref<{
    callId: string
    participants: User[]
    status: CallStatus
    startTime?: Date
  } | null>(null) // 当前通话信息
  
  const incomingCall = ref<{
    callId: string
    caller: User
    timestamp: Date
  } | null>(null) // 来电信息
  
  // ===== 消息管理 =====
  const messages = ref<SocketMessage[]>([]) // 消息列表
  const unreadCount = ref(0) // 未读消息数量
  
  // ===== 用户状态 =====
  const onlineUsers = ref<User[]>([]) // 在线用户列表
  const userStatus = ref<Record<string, 'online' | 'offline' | 'busy'>>({}) // 用户状态映射
  
  // ===== 计算属性 =====
  const isInCall = computed(() => {
    return currentCall.value?.status === 'connected' || currentCall.value?.status === 'calling'
  })
  
  const canMakeCall = computed(() => {
    return isConnected.value && !isInCall.value
  })
  
  // ===== Socket.io实例 =====
  let socket: Socket | null = null
  let reconnectTimer: NodeJS.Timeout | null = null

  // ===== 连接管理 =====
  /**
   * 连接Socket.io服务器
   * @param url Socket.io服务器地址，默认为localhost:3001
   */
  const connect = (url: string = 'http://localhost:3001'): Promise<void> => {
    return new Promise((resolve, reject) => {
    try {
      // 创建Socket.io连接
      socket = io(url, {
        transports: ['websocket', 'polling'], // 支持WebSocket和轮询
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: maxReconnectAttempts.value,
        reconnectionDelay: 3000
      })

      socket.on('connect', () => {
        console.log('Socket.io连接已建立')
        isConnected.value = true
        reconnectAttempts.value = 0

        // 发送认证信息
        const userStore = useUserStore()
        if (userStore.userInfo && userStore.userInfo.username) {
          socket?.emit('user-login', {
            username: userStore.userInfo.username,
            displayName: userStore.userInfo.displayName || userStore.userInfo.username,
            loginTime: userStore.userInfo.loginTime
          })
        }

        resolve()
      })

      // 监听消息事件
      socket.on('message', (data: any) => {
        try {
          const message: SocketMessage = {
            type: data.type || 'unknown',
            data: data.data || data,
            timestamp: new Date(data.timestamp || Date.now())
          }
          handleMessage(message)
        } catch (error) {
          console.error('处理消息时出错:', error)
        }
      })

      socket.on('connect_error', (error) => {
        console.error('Socket.io连接错误:', error)
        isConnected.value = false
        reject(error)
      })

      socket.on('disconnect', (reason) => {
        console.log('Socket.io连接已断开:', reason)
        isConnected.value = false

        // 自动重连（Socket.io会自动处理重连，但我们也可以手动控制）
        if (reason === 'io server disconnect') {
          // 服务器主动断开连接，需要手动重连
          scheduleReconnect()
        }
      })

      // 监听重连事件
      socket.on('reconnect', (attemptNumber) => {
        console.log('Socket.io重连成功，尝试次数:', attemptNumber)
        reconnectAttempts.value = 0
      })

      socket.on('reconnect_attempt', (attemptNumber) => {
        console.log('Socket.io重连尝试:', attemptNumber)
        reconnectAttempts.value = attemptNumber
      })

      // 监听业务事件
      setupSocketEventListeners(socket)

    } catch (error) {
      console.error('创建Socket.io连接失败:', error)
      reject(error)
    }
    })
  }
  
  /**
   * 断开Socket.io连接
   */
  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    if (socket) {
      socket.disconnect()
      socket = null
    }

    isConnected.value = false
    reconnectAttempts.value = 0
  }

  /**
   * 安排重连
   */
  const scheduleReconnect = () => {
    if (reconnectAttempts.value < maxReconnectAttempts.value) {
      reconnectAttempts.value++
      console.log(`安排重连 (${reconnectAttempts.value}/${maxReconnectAttempts.value})`)

      reconnectTimer = setTimeout(() => {
        connect()
      }, 3000 * reconnectAttempts.value) // 递增延迟重连
    }
  }

  /**
   * 设置Socket.io事件监听器
   */
  const setupSocketEventListeners = (socketInstance: Socket) => {
    // 监听用户状态更新
    socketInstance.on('user-status-update', (data) => {
      handleUserStatusUpdate(data)
    })

    // 监听在线用户列表更新
    socketInstance.on('online-users-update', (data) => {
      onlineUsers.value = data.users || []
    })

    // 监听语音通话相关事件
    socketInstance.on('voice-incoming', (data) => {
      handleIncomingCall(data)
    })

    socketInstance.on('voice-accepted', (data) => {
      handleCallAccepted(data)
    })

    socketInstance.on('voice-rejected', (data) => {
      handleCallRejected(data)
    })

    socketInstance.on('voice-ended', (data) => {
      handleCallEnded(data)
    })

    // 监听后台消息
    socketInstance.on('backend-call', (data) => {
      handleBackendMessage(data)
    })

    // 监听文本消息
    socketInstance.on('message-receive', (data) => {
      handleTextMessage(data)
    })

    // 监听错误事件
    socketInstance.on('error', (error) => {
      console.error('Socket.io服务器错误:', error)
    })
  }

  // ===== 消息处理 =====
  /**
   * 发送消息到服务器
   * @param eventName 事件名称
   * @param data 要发送的数据
   */
  const sendMessage = (eventName: string, data?: any) => {
    if (socket && socket.connected) {
      socket.emit(eventName, data)
    } else {
      console.warn('Socket.io未连接，无法发送消息')
    }
  }
  
  /**
   * 处理接收到的消息
   * @param message 接收到的消息
   */
  const handleMessage = (message: SocketMessage) => {
    console.log('收到WebSocket消息:', message)
    
    switch (message.type) {
      case 'call_request':
        // 处理来电请求
        handleIncomingCall(message.data)
        break
        
      case 'call_accepted':
        // 处理通话接受
        handleCallAccepted(message.data)
        break
        
      case 'call_rejected':
        // 处理通话拒绝
        handleCallRejected(message.data)
        break
        
      case 'call_ended':
        // 处理通话结束
        handleCallEnded(message.data)
        break
        
      case 'user_status':
        // 处理用户状态更新
        handleUserStatusUpdate(message.data)
        break
        
      case 'message':
        // 处理普通消息
        handleTextMessage(message.data)
        break
        
      default:
        console.log('未知消息类型:', message.type)
    }
  }
  
  // ===== 通话管理 =====
  /**
   * 发起通话
   * @param targetUserId 目标用户ID
   */
  const makeCall = (targetUserId: string) => {
    if (!canMakeCall.value) {
      console.warn('当前无法发起通话')
      return
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    sendMessage('voice-start', {
      targetUserId,
      sessionId,
      timestamp: Date.now()
    })

    // 更新本地通话状态
    currentCall.value = {
      callId: sessionId,
      participants: [], // 将在服务器响应后更新
      status: 'calling',
      startTime: new Date()
    }
  }

  /**
   * 接受来电
   */
  const acceptCall = () => {
    if (!incomingCall.value) return

    sendMessage('voice-accept', {
      sessionId: incomingCall.value.callId,
      timestamp: Date.now()
    })

    // 清除来电状态
    incomingCall.value = null
  }
  
  /**
   * 拒绝来电
   */
  const rejectCall = () => {
    if (!incomingCall.value) return

    sendMessage('voice-reject', {
      sessionId: incomingCall.value.callId,
      timestamp: Date.now()
    })

    // 清除来电状态
    incomingCall.value = null
  }

  /**
   * 结束通话
   */
  const endCall = () => {
    if (!currentCall.value) return

    sendMessage('voice-end', {
      sessionId: currentCall.value.callId,
      timestamp: Date.now()
    })

    // 清除通话状态
    currentCall.value = null
  }
  
  // ===== 事件处理函数 =====
  const handleIncomingCall = (data: any) => {
    incomingCall.value = {
      callId: data.sessionId || data.callId,
      caller: {
        id: data.fromUserId,
        username: data.fromUsername || data.caller?.username,
        displayName: data.fromDisplayName || data.caller?.displayName
      } as User,
      timestamp: new Date(data.timestamp || Date.now())
    }

    // 播放来电铃声
    uni.showModal({
      title: '来电',
      content: `${incomingCall.value.caller.displayName || incomingCall.value.caller.username} 正在呼叫您`,
      showCancel: true,
      confirmText: '接听',
      cancelText: '拒绝',
      success: (res) => {
        if (res.confirm) {
          acceptCall()
        } else {
          rejectCall()
        }
      }
    })
  }

  const handleBackendMessage = (data: any) => {
    // 处理后台消息
    const message: SocketMessage = {
      id: data.id || Date.now().toString(),
      type: 'backend_call',
      data: data,
      timestamp: new Date(data.timestamp || Date.now())
    }

    messages.value.unshift(message)
    unreadCount.value++

    // 显示通知
    uni.showToast({
      title: '收到后台消息',
      icon: 'none'
    })
  }

  const handleUserStatusUpdate = (data: any) => {
    if (data.userId && data.status) {
      userStatus.value[data.userId] = data.status
    }

    // 更新在线用户列表中的用户状态
    const userIndex = onlineUsers.value.findIndex(user => user.id === data.userId)
    if (userIndex !== -1) {
      onlineUsers.value[userIndex].status = data.status
    }
  }

  const handleTextMessage = (data: any) => {
    const message: SocketMessage = {
      id: data.id || Date.now().toString(),
      type: 'text_message',
      data: data,
      timestamp: new Date(data.timestamp || Date.now())
    }

    messages.value.unshift(message)
    unreadCount.value++
  }
  
  const handleCallAccepted = (data: any) => {
    const sessionId = data.sessionId || data.callId
    if (currentCall.value?.callId === sessionId && currentCall.value) {
      currentCall.value.status = 'connected'
      currentCall.value.participants = data.participants || []

      uni.showToast({
        title: '通话已接通',
        icon: 'success'
      })
    }
  }

  const handleCallRejected = (data: any) => {
    const sessionId = data.sessionId || data.callId
    if (currentCall.value?.callId === sessionId) {
      currentCall.value = null
      uni.showToast({
        title: '通话被拒绝',
        icon: 'none'
      })
    }
  }

  const handleCallEnded = (data: any) => {
    const sessionId = data.sessionId || data.callId
    if (currentCall.value?.callId === sessionId) {
      currentCall.value = null
      uni.showToast({
        title: '通话已结束',
        icon: 'none'
      })
    }
  }
  
  // ===== 清理函数 =====
  const clearMessages = () => {
    messages.value = []
    unreadCount.value = 0
  }
  
  const markAsRead = () => {
    unreadCount.value = 0
  }
  
  return {
    // 状态
    isConnected,
    reconnectAttempts,
    currentCall,
    incomingCall,
    messages,
    unreadCount,
    onlineUsers,
    userStatus,
    
    // 计算属性
    isInCall,
    canMakeCall,
    
    // 方法
    connect,
    disconnect,
    sendMessage,
    makeCall,
    acceptCall,
    rejectCall,
    endCall,
    clearMessages,
    markAsRead
  }
})

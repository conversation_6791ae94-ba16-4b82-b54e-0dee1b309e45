import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { io, Socket } from 'socket.io-client'
import type { User } from '@/types'
import { useUserStore } from './user'

// 通话状态类型
export type CallStatus = 'idle' | 'calling' | 'connected' | 'ended'

// Socket消息接口
export interface SocketMessage {
  id?: string
  type: string
  data: any
  timestamp: Date
}

/**
 * Socket Store - 管理WebSocket连接和实时通信状态
 * 
 * 功能说明：
 * - 管理WebSocket连接状态
 * - 处理实时消息收发
 * - 管理通话状态
 * - 处理用户在线状态
 */
export const useSocketStore = defineStore('socket', () => {
  // ===== 连接状态 =====
  const isConnected = ref(false) // WebSocket连接状态
  const reconnectAttempts = ref(0) // 重连尝试次数
  const maxReconnectAttempts = ref(5) // 最大重连次数
  const connectionQuality = ref<'excellent' | 'good' | 'poor' | 'unknown'>('unknown') // 连接质量
  const lastPingTime = ref<number>(0) // 最后一次ping时间
  const averageLatency = ref<number>(0) // 平均延迟
  
  // ===== 通话状态 =====
  const currentCall = ref<{
    callId: string
    participants: User[]
    status: CallStatus
    startTime?: Date
  } | null>(null) // 当前通话信息
  
  const incomingCall = ref<{
    callId: string
    caller: User
    timestamp: Date
  } | null>(null) // 来电信息
  
  // ===== 消息管理 =====
  const messages = ref<SocketMessage[]>([]) // 消息列表
  const unreadCount = ref(0) // 未读消息数量
  
  // ===== 用户状态 =====
  const onlineUsers = ref<User[]>([]) // 在线用户列表
  const userStatus = ref<Record<string, 'online' | 'offline' | 'busy'>>({}) // 用户状态映射
  
  // ===== 计算属性 =====
  const isInCall = computed(() => {
    return currentCall.value?.status === 'connected' || currentCall.value?.status === 'calling'
  })
  
  const canMakeCall = computed(() => {
    return isConnected.value && !isInCall.value
  })
  
  // ===== Socket.io实例 =====
  let socket: Socket | null = null
  let reconnectTimer: NodeJS.Timeout | null = null

  // ===== 连接管理 =====
  /**
   * 连接Socket.io服务器
   * @param url Socket.io服务器地址，默认为localhost:3001
   */
  const connect = (url: string = 'http://localhost:3001'): Promise<void> => {
    return new Promise((resolve, reject) => {
    try {
      // 获取用户认证信息
      const userStore = useUserStore()
      const token = userStore.sessionToken

      console.log('Socket.io连接配置:', {
        url,
        hasToken: !!token,
        tokenLength: token?.length || 0,
        userInfo: userStore.userInfo
      })

      if (!token) {
        reject(new Error('认证token缺失，请先登录'))
        return
      }

      // 创建Socket.io连接 - 使用最佳实践配置
      socket = io(url, {
        transports: ['websocket', 'polling'], // 支持WebSocket和轮询
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: maxReconnectAttempts.value,
        reconnectionDelay: 3000,
        reconnectionDelayMax: 5000, // 最大重连延迟
        randomizationFactor: 0.5, // 重连延迟随机化因子
        // 连接状态恢复配置（最佳实践）
        connectionStateRecovery: {
          maxDisconnectionDuration: 2 * 60 * 1000, // 2分钟内可恢复
          skipMiddlewares: true, // 跳过中间件以提高恢复性能
        },
        // 重试机制配置（Context7最佳实践）
        retries: 3, // 最大重试次数
        ackTimeout: 10000, // 默认确认超时时间
        auth: {
          token: token // 使用后端返回的JWT token进行认证
        }
      })

      socket.on('connect', () => {
        console.log('Socket.io连接已建立')
        isConnected.value = true
        reconnectAttempts.value = 0

        // 检查连接状态恢复
        if (socket.recovered) {
          console.log('连接状态已恢复，会话数据保持完整')
        } else {
          console.log('新的连接会话')
        }

        // 发送用户登录信息（如果认证成功，后端会自动处理）
        if (userStore.userInfo && userStore.userInfo.username) {
          socket?.emit('user-login', {
            username: userStore.userInfo.username,
            displayName: userStore.userInfo.displayName || userStore.userInfo.username,
            loginTime: userStore.userInfo.loginTime || Date.now()
          })
        }

        resolve()
      })

      // 监听消息事件
      socket.on('message', (data: any) => {
        try {
          const message: SocketMessage = {
            type: data.type || 'unknown',
            data: data.data || data,
            timestamp: new Date(data.timestamp || Date.now())
          }
          handleMessage(message)
        } catch (error) {
          console.error('处理消息时出错:', error)
        }
      })

      socket.on('connect_error', (error) => {
        console.error('Socket.io连接错误:', error)
        isConnected.value = false

        // 根据错误类型提供更详细的错误信息
        let errorMessage = '连接失败'
        if (error.message) {
          if (error.message.includes('Authentication') || error.message.includes('authentication')) {
            errorMessage = '认证失败，请重新登录'
            console.error('认证失败详情:', {
              error: error.message,
              token: token ? `${token.substring(0, 10)}...` : 'null',
              userInfo: userStore.userInfo?.username
            })
          } else if (error.message.includes('timeout')) {
            errorMessage = '连接超时，请检查网络'
          } else if (error.message.includes('ECONNREFUSED')) {
            errorMessage = '服务器连接被拒绝，请检查服务器是否启动'
          } else if (error.message.includes('ENOTFOUND')) {
            errorMessage = '服务器地址无法解析，请检查网络配置'
          } else {
            errorMessage = `连接错误: ${error.message}`
          }
        }

        reject(new Error(errorMessage))
      })

      socket.on('disconnect', (reason, details) => {
        console.log('Socket.io连接已断开:', reason, details)
        isConnected.value = false

        // 根据断开原因决定是否需要手动重连
        if (reason === 'io server disconnect') {
          // 服务器主动断开连接，需要手动重连
          console.log('服务器主动断开连接，准备重连...')
          scheduleReconnect()
        } else if (reason === 'transport error' && details) {
          // 传输错误，记录详细信息
          console.error('传输错误详情:', details)
          if (details.description && details.description.includes('401')) {
            // 认证失败，需要重新登录
            console.error('认证失败，需要重新登录')
            // 这里可以触发重新登录流程
          }
        }
        // 其他情况下Socket.io会自动重连
      })

      // 监听Manager级别的重连事件（最佳实践）
      socket.io.on('reconnect', (attemptNumber) => {
        console.log('Socket.io重连成功，尝试次数:', attemptNumber)
        reconnectAttempts.value = 0
        isConnected.value = true
      })

      socket.io.on('reconnect_attempt', (attemptNumber) => {
        console.log('Socket.io重连尝试:', attemptNumber)
        reconnectAttempts.value = attemptNumber
      })

      socket.io.on('reconnect_error', (error) => {
        console.error('Socket.io重连错误:', error)
      })

      socket.io.on('reconnect_failed', () => {
        console.error('Socket.io重连失败，已达到最大重连次数')
        // 可以在这里触发用户手动重连或重新登录
      })

      // 监听业务事件
      setupSocketEventListeners(socket)

    } catch (error) {
      console.error('创建Socket.io连接失败:', error)
      reject(error)
    }
    })
  }
  
  /**
   * 断开Socket.io连接 - 包含清理逻辑
   */
  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    if (socket) {
      // 清理所有事件监听器（最佳实践）
      socket.removeAllListeners()
      socket.io.removeAllListeners()

      // 断开连接
      socket.disconnect()
      socket = null
    }

    // 重置状态
    isConnected.value = false
    reconnectAttempts.value = 0
    onlineUsers.value = []
    messages.value = []
    currentCall.value = null
    incomingCall.value = null
    unreadCount.value = 0
  }

  /**
   * 安排重连
   */
  const scheduleReconnect = () => {
    if (reconnectAttempts.value < maxReconnectAttempts.value) {
      reconnectAttempts.value++
      console.log(`安排重连 (${reconnectAttempts.value}/${maxReconnectAttempts.value})`)

      reconnectTimer = setTimeout(() => {
        connect()
      }, 3000 * reconnectAttempts.value) // 递增延迟重连
    }
  }

  /**
   * 设置Socket.io事件监听器
   */
  const setupSocketEventListeners = (socketInstance: Socket) => {
    // 监听用户状态更新
    socketInstance.on('user-status-update', (data) => {
      handleUserStatusUpdate(data)
    })

    // 监听在线用户列表更新
    socketInstance.on('online-users-update', (data) => {
      onlineUsers.value = data.users || []
    })

    // 监听语音通话相关事件
    socketInstance.on('voice-incoming', (data) => {
      handleIncomingCall(data)
    })

    socketInstance.on('voice-accepted', (data) => {
      handleCallAccepted(data)
    })

    socketInstance.on('voice-rejected', (data) => {
      handleCallRejected(data)
    })

    socketInstance.on('voice-ended', (data) => {
      handleCallEnded(data)
    })

    // 监听后台消息
    socketInstance.on('backend-call', (data) => {
      handleBackendMessage(data)
    })

    // 监听文本消息
    socketInstance.on('message-receive', (data) => {
      handleTextMessage(data)
    })

    // 监听错误事件
    socketInstance.on('error', (error) => {
      console.error('Socket.io服务器错误:', error)
    })

    // 监听ping/pong事件用于连接质量监控（最佳实践）
    socketInstance.on('ping', () => {
      lastPingTime.value = Date.now()
    })

    socketInstance.on('pong', (latency: number) => {
      const currentLatency = Date.now() - lastPingTime.value
      averageLatency.value = averageLatency.value === 0
        ? currentLatency
        : (averageLatency.value + currentLatency) / 2

      // 根据延迟评估连接质量
      if (currentLatency < 100) {
        connectionQuality.value = 'excellent'
      } else if (currentLatency < 300) {
        connectionQuality.value = 'good'
      } else {
        connectionQuality.value = 'poor'
      }

      console.log(`连接延迟: ${currentLatency}ms, 平均延迟: ${Math.round(averageLatency.value)}ms, 质量: ${connectionQuality.value}`)
    })
  }

  // ===== 消息处理 =====
  /**
   * 发送消息到服务器
   * @param eventName 事件名称
   * @param data 要发送的数据
   * @param options 发送选项
   */
  const sendMessage = (eventName: string, data?: any, options?: {
    timeout?: number
    callback?: (error?: Error, response?: any) => void
  }) => {
    if (socket && socket.connected) {
      if (options?.callback) {
        // 带确认的消息发送
        if (options.timeout) {
          socket.timeout(options.timeout).emit(eventName, data, (error: Error, response: any) => {
            if (error) {
              console.error(`消息发送超时 (${options.timeout}ms):`, eventName)
              options.callback?.(error)
            } else {
              options.callback?.(undefined, response)
            }
          })
        } else {
          socket.emit(eventName, data, options.callback)
        }
      } else {
        // 普通消息发送
        socket.emit(eventName, data)
      }
    } else {
      console.warn('Socket.io未连接，无法发送消息')
      options?.callback?.(new Error('Socket未连接'))
    }
  }

  /**
   * 发送带确认的消息（Promise版本）- 使用最佳实践
   * @param eventName 事件名称
   * @param data 要发送的数据
   * @param timeout 超时时间（毫秒）
   */
  const sendMessageWithAck = async (eventName: string, data?: any, timeout: number = 5000): Promise<any> => {
    if (!socket || !socket.connected) {
      throw new Error('Socket未连接')
    }

    try {
      // 使用Context7推荐的Promise-based确认方式
      const response = await socket.timeout(timeout).emitWithAck(eventName, data)
      console.log(`消息确认成功 [${eventName}]:`, response)
      return response
    } catch (error: any) {
      // 详细的错误处理
      if (error.message && error.message.includes('timeout')) {
        console.error(`消息确认超时 (${timeout}ms) [${eventName}]:`, data)
        throw new Error(`服务器响应超时，请检查网络连接`)
      } else {
        console.error(`消息确认失败 [${eventName}]:`, error)
        throw new Error(`消息发送失败: ${error.message || '未知错误'}`)
      }
    }
  }
  
  /**
   * 处理接收到的消息
   * @param message 接收到的消息
   */
  const handleMessage = (message: SocketMessage) => {
    console.log('收到WebSocket消息:', message)
    
    switch (message.type) {
      case 'call_request':
        // 处理来电请求
        handleIncomingCall(message.data)
        break
        
      case 'call_accepted':
        // 处理通话接受
        handleCallAccepted(message.data)
        break
        
      case 'call_rejected':
        // 处理通话拒绝
        handleCallRejected(message.data)
        break
        
      case 'call_ended':
        // 处理通话结束
        handleCallEnded(message.data)
        break
        
      case 'user_status':
        // 处理用户状态更新
        handleUserStatusUpdate(message.data)
        break
        
      case 'message':
        // 处理普通消息
        handleTextMessage(message.data)
        break
        
      default:
        console.log('未知消息类型:', message.type)
    }
  }
  
  // ===== 通话管理 =====
  /**
   * 发起通话
   * @param targetUserId 目标用户ID
   */
  const makeCall = async (targetUserId: string) => {
    if (!canMakeCall.value) {
      console.warn('当前无法发起通话')
      return
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    try {
      // 使用带确认的消息发送，确保服务器收到请求
      await sendMessageWithAck('voice-start', {
        targetUserId,
        sessionId,
        timestamp: Date.now()
      }, 10000) // 10秒超时

      // 更新本地通话状态
      currentCall.value = {
        callId: sessionId,
        participants: [], // 将在服务器响应后更新
        status: 'calling',
        startTime: new Date()
      }

      console.log('通话请求已发送:', sessionId)
    } catch (error) {
      console.error('发起通话失败:', error)
      uni.showToast({
        title: '发起通话失败',
        icon: 'error'
      })
    }
  }

  /**
   * 接受来电
   */
  const acceptCall = () => {
    if (!incomingCall.value) return

    sendMessage('voice-accept', {
      sessionId: incomingCall.value.callId,
      timestamp: Date.now()
    })

    // 清除来电状态
    incomingCall.value = null
  }
  
  /**
   * 拒绝来电
   */
  const rejectCall = () => {
    if (!incomingCall.value) return

    sendMessage('voice-reject', {
      sessionId: incomingCall.value.callId,
      timestamp: Date.now()
    })

    // 清除来电状态
    incomingCall.value = null
  }

  /**
   * 结束通话
   */
  const endCall = () => {
    if (!currentCall.value) return

    sendMessage('voice-end', {
      sessionId: currentCall.value.callId,
      timestamp: Date.now()
    })

    // 清除通话状态
    currentCall.value = null
  }
  
  // ===== 事件处理函数 =====
  const handleIncomingCall = (data: any) => {
    incomingCall.value = {
      callId: data.sessionId || data.callId,
      caller: {
        id: data.fromUserId,
        username: data.fromUsername || data.caller?.username,
        displayName: data.fromDisplayName || data.caller?.displayName
      } as User,
      timestamp: new Date(data.timestamp || Date.now())
    }

    // 播放来电铃声
    uni.showModal({
      title: '来电',
      content: `${incomingCall.value.caller.displayName || incomingCall.value.caller.username} 正在呼叫您`,
      showCancel: true,
      confirmText: '接听',
      cancelText: '拒绝',
      success: (res) => {
        if (res.confirm) {
          acceptCall()
        } else {
          rejectCall()
        }
      }
    })
  }

  const handleBackendMessage = (data: any) => {
    // 处理后台消息
    const message: SocketMessage = {
      id: data.id || Date.now().toString(),
      type: 'backend_call',
      data: data,
      timestamp: new Date(data.timestamp || Date.now())
    }

    messages.value.unshift(message)
    unreadCount.value++

    // 显示通知
    uni.showToast({
      title: '收到后台消息',
      icon: 'none'
    })
  }

  const handleUserStatusUpdate = (data: any) => {
    if (data.userId && data.status) {
      userStatus.value[data.userId] = data.status
    }

    // 更新在线用户列表中的用户状态
    const userIndex = onlineUsers.value.findIndex(user => user.id === data.userId)
    if (userIndex !== -1) {
      onlineUsers.value[userIndex].status = data.status
    }
  }

  const handleTextMessage = (data: any) => {
    const message: SocketMessage = {
      id: data.id || Date.now().toString(),
      type: 'text_message',
      data: data,
      timestamp: new Date(data.timestamp || Date.now())
    }

    messages.value.unshift(message)
    unreadCount.value++
  }
  
  const handleCallAccepted = (data: any) => {
    const sessionId = data.sessionId || data.callId
    if (currentCall.value?.callId === sessionId && currentCall.value) {
      currentCall.value.status = 'connected'
      currentCall.value.participants = data.participants || []

      uni.showToast({
        title: '通话已接通',
        icon: 'success'
      })
    }
  }

  const handleCallRejected = (data: any) => {
    const sessionId = data.sessionId || data.callId
    if (currentCall.value?.callId === sessionId) {
      currentCall.value = null
      uni.showToast({
        title: '通话被拒绝',
        icon: 'none'
      })
    }
  }

  const handleCallEnded = (data: any) => {
    const sessionId = data.sessionId || data.callId
    if (currentCall.value?.callId === sessionId) {
      currentCall.value = null
      uni.showToast({
        title: '通话已结束',
        icon: 'none'
      })
    }
  }
  
  // ===== 清理函数 =====
  const clearMessages = () => {
    messages.value = []
    unreadCount.value = 0
  }
  
  const markAsRead = () => {
    unreadCount.value = 0
  }
  
  return {
    // 连接状态
    isConnected,
    reconnectAttempts,
    connectionQuality,
    averageLatency,

    // 通话状态
    currentCall,
    incomingCall,

    // 用户和消息
    messages,
    unreadCount,
    onlineUsers,
    userStatus,

    // 计算属性
    isInCall,
    canMakeCall,

    // 方法
    connect,
    disconnect,
    sendMessage,
    sendMessageWithAck,
    makeCall,
    acceptCall,
    rejectCall,
    endCall,
    clearMessages,
    markAsRead
  }
})

/**
 * 系统管理相关路由
 * 
 * 功能说明：
 * 1. 系统状态监控
 * 2. 系统配置管理
 * 3. 统计信息查询
 * 4. 系统日志查询
 * 5. 健康检查和诊断
 */

import { Router, Request, Response } from 'express'
import { logger } from '@/utils/logger'
import { DatabaseManager } from '@/database/DatabaseManager'
import { validateRequest } from '@/middleware/validation'
import os from 'os'
import fs from 'fs'

const router = Router()
const dbManager = DatabaseManager.getInstance()

// 系统配置验证规则
const configValidation = {
  key: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100
  },
  value: {
    required: true,
    type: 'string',
    maxLength: 1000
  },
  description: {
    required: false,
    type: 'string',
    maxLength: 500
  }
}

// 统计查询验证规则
const statsQueryValidation = {
  startDate: {
    required: false,
    type: 'string'
  },
  endDate: {
    required: false,
    type: 'string'
  },
  type: {
    required: false,
    type: 'string',
    enum: ['users', 'messages', 'calls', 'all']
  }
}

/**
 * 获取系统状态
 * GET /api/system/status
 */
router.get('/status', async (req: Request, res: Response) => {
  try {
    const systemInfo = {
      server: {
        uptime: process.uptime(),
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        memory: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      },
      system: {
        hostname: os.hostname(),
        type: os.type(),
        release: os.release(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpus: os.cpus().length,
        loadAverage: os.loadavg()
      },
      database: {
        connected: true, // 简化处理，实际应该检查数据库连接状态
        version: 'SQLite 3.x'
      },
      timestamp: new Date().toISOString()
    }

    res.json({
      success: true,
      message: 'System status retrieved successfully',
      data: systemInfo
    })

  } catch (error) {
    logger.error('Get system status error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_SYSTEM_STATUS_ERROR'
    })
  }
})

/**
 * 获取系统统计信息
 * GET /api/system/stats
 */
router.get('/stats', validateRequest(statsQueryValidation, 'query'), async (req: Request, res: Response) => {
  try {
    const { startDate, endDate, type = 'all' } = req.query

    const dateRange = {
      startDate: startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 默认30天前
      endDate: endDate ? new Date(endDate as string) : new Date()
    }

    const stats: any = {}

    if (type === 'all' || type === 'users') {
      stats.users = await dbManager.getUserStats(dateRange)
    }

    if (type === 'all' || type === 'messages') {
      stats.messages = await dbManager.getMessageStats(dateRange)
    }

    if (type === 'all' || type === 'calls') {
      stats.calls = await dbManager.getCallStats(dateRange)
    }

    res.json({
      success: true,
      message: 'System statistics retrieved successfully',
      data: {
        stats,
        dateRange,
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    logger.error('Get system stats error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_SYSTEM_STATS_ERROR'
    })
  }
})

/**
 * 获取系统配置
 * GET /api/system/config
 */
router.get('/config', async (req: Request, res: Response) => {
  try {
    const configs = await dbManager.getSystemConfigs()

    res.json({
      success: true,
      message: 'System configurations retrieved successfully',
      data: {
        configs: configs.map(config => ({
          key: config.key,
          value: config.value,
          description: config.description,
          updatedAt: config.updatedAt
        }))
      }
    })

  } catch (error) {
    logger.error('Get system config error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_SYSTEM_CONFIG_ERROR'
    })
  }
})

/**
 * 更新系统配置
 * PUT /api/system/config
 */
router.put('/config', validateRequest(configValidation), async (req: Request, res: Response) => {
  try {
    const userId = (req as any).userId
    const { key, value, description } = req.body

    await dbManager.setSystemConfig(key, value, description, userId)

    logger.info(`System config updated: ${key} by user ${userId}`)

    res.json({
      success: true,
      message: 'System configuration updated successfully',
      data: {
        key,
        value,
        description,
        updatedAt: new Date().toISOString(),
        updatedBy: userId
      }
    })

  } catch (error) {
    logger.error('Update system config error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'UPDATE_SYSTEM_CONFIG_ERROR'
    })
  }
})

/**
 * 获取系统日志
 * GET /api/system/logs
 */
router.get('/logs', async (req: Request, res: Response) => {
  try {
    const { level = 'all', limit = 100, page = 1 } = req.query

    // 简化处理，实际应该从日志文件或日志数据库中读取
    const logs = await dbManager.getSystemLogs({
      level: level as string,
      limit: parseInt(limit as string),
      page: parseInt(page as string)
    })

    res.json({
      success: true,
      message: 'System logs retrieved successfully',
      data: {
        logs: logs.logs,
        pagination: {
          page: logs.page,
          limit: logs.limit,
          total: logs.total,
          totalPages: Math.ceil(logs.total / logs.limit)
        }
      }
    })

  } catch (error) {
    logger.error('Get system logs error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_SYSTEM_LOGS_ERROR'
    })
  }
})

/**
 * 系统健康检查
 * GET /api/system/health
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const healthChecks = {
      database: await checkDatabaseHealth(),
      memory: checkMemoryHealth(),
      disk: await checkDiskHealth(),
      websocket: checkWebSocketHealth()
    }

    const overallHealth = Object.values(healthChecks).every(check => check.status === 'healthy')

    res.json({
      success: true,
      message: 'Health check completed',
      data: {
        overall: overallHealth ? 'healthy' : 'unhealthy',
        checks: healthChecks,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    logger.error('Health check error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'HEALTH_CHECK_ERROR'
    })
  }
})

/**
 * 清理系统数据
 * POST /api/system/cleanup
 */
router.post('/cleanup', async (req: Request, res: Response) => {
  try {
    const userId = (req as any).userId
    const { type = 'logs', days = 30 } = req.body

    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    let cleanupResult: any = {}

    switch (type) {
      case 'logs':
        cleanupResult = await dbManager.cleanupOldLogs(cutoffDate)
        break
      case 'messages':
        cleanupResult = await dbManager.cleanupOldMessages(cutoffDate)
        break
      case 'sessions':
        cleanupResult = await dbManager.cleanupOldSessions(cutoffDate)
        break
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid cleanup type',
          code: 'INVALID_CLEANUP_TYPE'
        })
    }

    logger.info(`System cleanup performed: ${type} older than ${days} days by user ${userId}`)

    res.json({
      success: true,
      message: 'System cleanup completed successfully',
      data: {
        type,
        days,
        cutoffDate,
        cleanupResult,
        performedBy: userId,
        performedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    logger.error('System cleanup error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'SYSTEM_CLEANUP_ERROR'
    })
  }
})

// 辅助函数：检查数据库健康状态
async function checkDatabaseHealth(): Promise<{ status: string; message: string; responseTime?: number }> {
  try {
    const start = Date.now()
    await dbManager.healthCheck()
    const responseTime = Date.now() - start

    return {
      status: 'healthy',
      message: 'Database connection is working',
      responseTime
    }
  } catch {
    return {
      status: 'unhealthy',
      message: 'Database connection failed'
    }
  }
}

// 辅助函数：检查内存健康状态
function checkMemoryHealth(): { status: string; message: string; usage: any } {
  const memUsage = process.memoryUsage()
  const totalMemory = os.totalmem()
  const freeMemory = os.freemem()
  const usedMemoryPercent = ((totalMemory - freeMemory) / totalMemory) * 100

  const status = usedMemoryPercent > 90 ? 'unhealthy' : 'healthy'
  const message = status === 'healthy' ? 'Memory usage is normal' : 'High memory usage detected'

  return {
    status,
    message,
    usage: {
      process: memUsage,
      system: {
        total: totalMemory,
        free: freeMemory,
        usedPercent: usedMemoryPercent
      }
    }
  }
}

// 辅助函数：检查磁盘健康状态
async function checkDiskHealth(): Promise<{ status: string; message: string; usage?: any }> {
  try {
    const _stats = fs.statSync(process.cwd())
    return {
      status: 'healthy',
      message: 'Disk access is working'
    }
  } catch {
    return {
      status: 'unhealthy',
      message: 'Disk access failed'
    }
  }
}

// 辅助函数：检查WebSocket健康状态
function checkWebSocketHealth(): { status: string; message: string } {
  // 简化处理，实际应该检查WebSocket服务器状态
  return {
    status: 'healthy',
    message: 'WebSocket server is running'
  }
}

export default router

main.vue:347  获取在线用户列表异常: {errMsg: 'request:fail'}
fetchOnlineUsers @ main.vue:347
await in fetchOnlineUsers
initializePage @ main.vue:211
await in initializePage
(匿名) @ main.vue:188
(匿名) @ vue.runtime.esm.js:4209
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
hook.__weh.hook.__weh @ vue.runtime.esm.js:4187
flushPostFlushCbs @ vue.runtime.esm.js:1591
flushJobs @ vue.runtime.esm.js:1629
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
(匿名) @ vue.runtime.esm.js:7494
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
(匿名) @ vue.runtime.esm.js:3805
Promise.then
setup @ vue.runtime.esm.js:3804
callWithErrorHandling @ vue.runtime.esm.js:1418
setupStatefulComponent @ vue.runtime.esm.js:8980
setupComponent @ vue.runtime.esm.js:8941
mountComponent @ vue.runtime.esm.js:7265
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
callWithErrorHandling @ vue.runtime.esm.js:1418
flushJobs @ vue.runtime.esm.js:1623
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
scheduler @ vue.runtime.esm.js:3219
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ vue.runtime.esm.js:5200
initRouter @ uni-h5.es.js:16225
install @ uni-h5.es.js:16294
use @ vue.runtime.esm.js:5200
(匿名) @ main.ts:17
main.vue:215  网络服务连接失败: Error: 网络连接错误，无法获取用户列表
    at fetchOnlineUsers (main.vue:353:13)
    at async initializePage (main.vue:211:9)
    at async main.vue:188:3
initializePage @ main.vue:215
await in initializePage
(匿名) @ main.vue:188
(匿名) @ vue.runtime.esm.js:4209
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
hook.__weh.hook.__weh @ vue.runtime.esm.js:4187
flushPostFlushCbs @ vue.runtime.esm.js:1591
flushJobs @ vue.runtime.esm.js:1629
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
(匿名) @ vue.runtime.esm.js:7494
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
(匿名) @ vue.runtime.esm.js:3805
Promise.then
setup @ vue.runtime.esm.js:3804
callWithErrorHandling @ vue.runtime.esm.js:1418
setupStatefulComponent @ vue.runtime.esm.js:8980
setupComponent @ vue.runtime.esm.js:8941
mountComponent @ vue.runtime.esm.js:7265
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
callWithErrorHandling @ vue.runtime.esm.js:1418
flushJobs @ vue.runtime.esm.js:1623
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
scheduler @ vue.runtime.esm.js:3219
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ vue.runtime.esm.js:5200
initRouter @ uni-h5.es.js:16225
install @ uni-h5.es.js:16294
use @ vue.runtime.esm.js:5200
(匿名) @ main.ts:17
main.vue:314   GET http://localhost:3001/api/users/online net::ERR_CONNECTION_REFUSED
(匿名) @ uni-h5.es.js:20538
(匿名) @ uni-h5.es.js:3066
invokeApi @ uni-h5.es.js:2917
(匿名) @ uni-h5.es.js:2939
(匿名) @ uni-h5.es.js:2938
fetchOnlineUsers @ main.vue:314
initializePage @ main.vue:211
await in initializePage
(匿名) @ main.vue:188
(匿名) @ vue.runtime.esm.js:4209
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
hook.__weh.hook.__weh @ vue.runtime.esm.js:4187
flushPostFlushCbs @ vue.runtime.esm.js:1591
flushJobs @ vue.runtime.esm.js:1629
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
(匿名) @ vue.runtime.esm.js:7494
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
(匿名) @ vue.runtime.esm.js:3805
Promise.then
setup @ vue.runtime.esm.js:3804
callWithErrorHandling @ vue.runtime.esm.js:1418
setupStatefulComponent @ vue.runtime.esm.js:8980
setupComponent @ vue.runtime.esm.js:8941
mountComponent @ vue.runtime.esm.js:7265
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
mountChildren @ vue.runtime.esm.js:6947
processFragment @ vue.runtime.esm.js:7161
patch @ vue.runtime.esm.js:6673
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
mountChildren @ vue.runtime.esm.js:6947
mountElement @ vue.runtime.esm.js:6854
processElement @ vue.runtime.esm.js:6819
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7375
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
setupRenderEffect @ vue.runtime.esm.js:7510
mountComponent @ vue.runtime.esm.js:7277
processComponent @ vue.runtime.esm.js:7231
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
callWithErrorHandling @ vue.runtime.esm.js:1418
flushJobs @ vue.runtime.esm.js:1623
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
scheduler @ vue.runtime.esm.js:3219
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ vue.runtime.esm.js:5200
initRouter @ uni-h5.es.js:16225
install @ uni-h5.es.js:16294
use @ vue.runtime.esm.js:5200
(匿名) @ main.ts:17
websocket.js:119  WebSocket connection to 'ws://localhost:3001/socket.io/?EIO=4&transport=websocket' failed: 
createSocket @ websocket.js:119
doOpen @ websocket.js:24
open @ transport.js:47
_open @ socket.js:197
SocketWithoutUpgrade @ socket.js:150
SocketWithUpgrade @ socket.js:565
Socket @ socket.js:725
open @ manager.js:111
(匿名) @ manager.js:337
socket.ts:156  Socket.io连接错误: TransportError: websocket error
    at WS.onError (transport.js:39:37)
    at ws.onerror (websocket.js:49:39)
(匿名) @ socket.ts:156
Emitter.emit @ index.js:136
onerror @ socket.js:439
Emitter.emit @ index.js:136
onError @ manager.js:124
Emitter.emit @ index.js:136
_onError @ socket.js:495
Emitter.emit @ index.js:136
onError @ transport.js:39
ws.onerror @ websocket.js:49
socket.ts:217  Socket.io重连错误: TransportError: websocket error
    at WS.onError (transport.js:39:37)
    at ws.onerror (websocket.js:49:39)
(匿名) @ socket.ts:217
Emitter.emit @ index.js:136
(匿名) @ manager.js:341
onError @ manager.js:126
Emitter.emit @ index.js:136
_onError @ socket.js:495
Emitter.emit @ index.js:136
onError @ transport.js:39
ws.onerror @ websocket.js:49
websocket.js:119  WebSocket connection to 'ws://localhost:3001/socket.io/?EIO=4&transport=websocket' failed: 
createSocket @ websocket.js:119
doOpen @ websocket.js:24
open @ transport.js:47
_open @ socket.js:197
SocketWithoutUpgrade @ socket.js:150
SocketWithUpgrade @ socket.js:565
Socket @ socket.js:725
open @ manager.js:111
(匿名) @ manager.js:337
socket.ts:156  Socket.io连接错误: TransportError: websocket error
    at WS.onError (transport.js:39:37)
    at ws.onerror (websocket.js:49:39)
(匿名) @ socket.ts:156
Emitter.emit @ index.js:136
onerror @ socket.js:439
Emitter.emit @ index.js:136
onError @ manager.js:124
Emitter.emit @ index.js:136
_onError @ socket.js:495
Emitter.emit @ index.js:136
onError @ transport.js:39
ws.onerror @ websocket.js:49
socket.ts:217  Socket.io重连错误: TransportError: websocket error
    at WS.onError (transport.js:39:37)
    at ws.onerror (websocket.js:49:39)
(匿名) @ socket.ts:217
Emitter.emit @ index.js:136
(匿名) @ manager.js:341
onError @ manager.js:126
Emitter.emit @ index.js:136
_onError @ socket.js:495
Emitter.emit @ index.js:136
onError @ transport.js:39
ws.onerror @ websocket.js:49
websocket.js:119  WebSocket connection to 'ws://localhost:3001/socket.io/?EIO=4&transport=websocket' failed: 
createSocket @ websocket.js:119
doOpen @ websocket.js:24
open @ transport.js:47
_open @ socket.js:197
SocketWithoutUpgrade @ socket.js:150
SocketWithUpgrade @ socket.js:565
Socket @ socket.js:725
open @ manager.js:111
(匿名) @ manager.js:337
socket.ts:156  Socket.io连接错误: TransportError: websocket error
    at WS.onError (transport.js:39:37)
    at ws.onerror (websocket.js:49:39)
(匿名) @ socket.ts:156
Emitter.emit @ index.js:136
onerror @ socket.js:439
Emitter.emit @ index.js:136
onError @ manager.js:124
Emitter.emit @ index.js:136
_onError @ socket.js:495
Emitter.emit @ index.js:136
onError @ transport.js:39
ws.onerror @ websocket.js:49
socket.ts:217  Socket.io重连错误: TransportError: websocket error
    at WS.onError (transport.js:39:37)
    at ws.onerror (websocket.js:49:39)
(匿名) @ socket.ts:217
Emitter.emit @ index.js:136
(匿名) @ manager.js:341
onError @ manager.js:126
Emitter.emit @ index.js:136
_onError @ socket.js:495
Emitter.emit @ index.js:136
onError @ transport.js:39
ws.onerror @ websocket.js:49

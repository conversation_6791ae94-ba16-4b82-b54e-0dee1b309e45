main.vue:26  [Vue warn]: Property "isSocketConnected" was accessed during render but is not defined on instance. 
 at <Text>
at <Button>
at <Button>
at <View>
at <View>
at <View>
at <View>
at <Main>
at <AsyncComponentWrapper>
at <PageBody>
at <Page>
at <Anonymous>
at <KeepAlive>
at <RouterView>
at <Layout>
at <App>
warnHandler @ uni-h5.es.js:16314
callWithErrorHandling @ vue.runtime.esm.js:1418
warn$1 @ vue.runtime.esm.js:1243
get @ vue.runtime.esm.js:4462
(匿名) @ main.vue:26
renderFnWithContext @ vue.runtime.esm.js:2068
renderSlot @ vue.runtime.esm.js:4303
(匿名) @ Button.vue:15
renderFnWithContext @ vue.runtime.esm.js:2068
(匿名) @ uni-h5.es.js:15747
renderComponentRoot @ vue.runtime.esm.js:2128
componentUpdateFn @ vue.runtime.esm.js:7447
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
patchKeyedChildren @ vue.runtime.esm.js:7653
patchChildren @ vue.runtime.esm.js:7567
processFragment @ vue.runtime.esm.js:7205
patch @ vue.runtime.esm.js:6673
patchKeyedChildren @ vue.runtime.esm.js:7653
patchChildren @ vue.runtime.esm.js:7567
patchElement @ vue.runtime.esm.js:6994
processElement @ vue.runtime.esm.js:6830
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
callWithErrorHandling @ vue.runtime.esm.js:1418
flushJobs @ vue.runtime.esm.js:1623
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
(匿名) @ vue.runtime.esm.js:7494
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
(匿名) @ uni-h5.es.js:2090
setTimeout
handleHoverStart @ uni-h5.es.js:2089
onTouchstartPassive @ uni-h5.es.js:2069
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10263
main.vue:26  [Vue warn]: Property "isSocketConnected" was accessed during render but is not defined on instance. 
 at <Text>
at <Button>
at <Button>
at <View>
at <View>
at <View>
at <View>
at <Main>
at <AsyncComponentWrapper>
at <PageBody>
at <Page>
at <Anonymous>
at <KeepAlive>
at <RouterView>
at <Layout>
at <App>
warnHandler @ uni-h5.es.js:16314
callWithErrorHandling @ vue.runtime.esm.js:1418
warn$1 @ vue.runtime.esm.js:1243
get @ vue.runtime.esm.js:4462
(匿名) @ main.vue:26
renderFnWithContext @ vue.runtime.esm.js:2068
renderSlot @ vue.runtime.esm.js:4303
(匿名) @ Button.vue:15
renderFnWithContext @ vue.runtime.esm.js:2068
(匿名) @ uni-h5.es.js:15747
renderComponentRoot @ vue.runtime.esm.js:2128
componentUpdateFn @ vue.runtime.esm.js:7447
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
patchKeyedChildren @ vue.runtime.esm.js:7673
patchChildren @ vue.runtime.esm.js:7567
processFragment @ vue.runtime.esm.js:7205
patch @ vue.runtime.esm.js:6673
patchKeyedChildren @ vue.runtime.esm.js:7653
patchChildren @ vue.runtime.esm.js:7567
patchElement @ vue.runtime.esm.js:6994
processElement @ vue.runtime.esm.js:6830
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
patchBlockChildren @ vue.runtime.esm.js:7089
processFragment @ vue.runtime.esm.js:7179
patch @ vue.runtime.esm.js:6673
patchKeyedChildren @ vue.runtime.esm.js:7653
patchChildren @ vue.runtime.esm.js:7567
patchElement @ vue.runtime.esm.js:6994
processElement @ vue.runtime.esm.js:6830
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
callWithErrorHandling @ vue.runtime.esm.js:1418
flushJobs @ vue.runtime.esm.js:1623
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
(匿名) @ vue.runtime.esm.js:7494
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
handleRefresh @ main.vue:667
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
emit @ vue.runtime.esm.js:1941
(匿名) @ vue.runtime.esm.js:9171
handleClick @ Button.vue:72
patchedFn @ vue.runtime.esm.js:10283
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10254
socket.ts:86 Socket.io连接配置: {url: 'ws://localhost:3001', hasToken: true, tokenLength: 167, userInfo: Proxy(Object)}
socket.ts:121 🎉 Socket.io连接已建立
socket.ts:131 🆕 新的连接会话
socket.ts:135 ✅ 连接状态验证: {socketConnected: true, storeConnected: true, socketId: 'txhS-Y34qRCcY0eBAAAE', recovered: false}
main.vue:453 Socket.io连接成功
main.vue:455 Socket.io连接成功
main.vue:501 在线用户列表获取成功: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}}
main.vue:26  [Vue warn]: Property "isSocketConnected" was accessed during render but is not defined on instance. 
 at <Text>
at <Button>
at <Button>
at <View>
at <View>
at <View>
at <View>
at <Main>
at <AsyncComponentWrapper>
at <PageBody>
at <Page>
at <Anonymous>
at <KeepAlive>
at <RouterView>
at <Layout>
at <App>
warnHandler @ uni-h5.es.js:16314
callWithErrorHandling @ vue.runtime.esm.js:1418
warn$1 @ vue.runtime.esm.js:1243
get @ vue.runtime.esm.js:4462
(匿名) @ main.vue:26
renderFnWithContext @ vue.runtime.esm.js:2068
renderSlot @ vue.runtime.esm.js:4303
(匿名) @ Button.vue:15
renderFnWithContext @ vue.runtime.esm.js:2068
(匿名) @ uni-h5.es.js:15747
renderComponentRoot @ vue.runtime.esm.js:2128
componentUpdateFn @ vue.runtime.esm.js:7447
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
patchKeyedChildren @ vue.runtime.esm.js:7673
patchChildren @ vue.runtime.esm.js:7567
processFragment @ vue.runtime.esm.js:7205
patch @ vue.runtime.esm.js:6673
patchKeyedChildren @ vue.runtime.esm.js:7653
patchChildren @ vue.runtime.esm.js:7567
patchElement @ vue.runtime.esm.js:6994
processElement @ vue.runtime.esm.js:6830
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
patchBlockChildren @ vue.runtime.esm.js:7089
processFragment @ vue.runtime.esm.js:7179
patch @ vue.runtime.esm.js:6673
patchKeyedChildren @ vue.runtime.esm.js:7653
patchChildren @ vue.runtime.esm.js:7567
patchElement @ vue.runtime.esm.js:6994
processElement @ vue.runtime.esm.js:6830
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
callWithErrorHandling @ vue.runtime.esm.js:1418
flushJobs @ vue.runtime.esm.js:1623
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
(匿名) @ vue.runtime.esm.js:7494
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
showToast @ main.vue:739
initializePage @ main.vue:373
await in initializePage
handleRefresh @ main.vue:673
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
emit @ vue.runtime.esm.js:1941
(匿名) @ vue.runtime.esm.js:9171
handleClick @ Button.vue:72
patchedFn @ vue.runtime.esm.js:10283
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10254
main.vue:26  [Vue warn]: Property "isSocketConnected" was accessed during render but is not defined on instance. 
 at <Text>
at <Button>
at <Button>
at <View>
at <View>
at <View>
at <View>
at <Main>
at <AsyncComponentWrapper>
at <PageBody>
at <Page>
at <Anonymous>
at <KeepAlive>
at <RouterView>
at <Layout>
at <App>
warnHandler @ uni-h5.es.js:16314
callWithErrorHandling @ vue.runtime.esm.js:1418
warn$1 @ vue.runtime.esm.js:1243
get @ vue.runtime.esm.js:4462
(匿名) @ main.vue:26
renderFnWithContext @ vue.runtime.esm.js:2068
renderSlot @ vue.runtime.esm.js:4303
(匿名) @ Button.vue:15
renderFnWithContext @ vue.runtime.esm.js:2068
(匿名) @ uni-h5.es.js:15747
renderComponentRoot @ vue.runtime.esm.js:2128
componentUpdateFn @ vue.runtime.esm.js:7447
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
updateComponent @ vue.runtime.esm.js:7308
processComponent @ vue.runtime.esm.js:7242
patch @ vue.runtime.esm.js:6699
patchKeyedChildren @ vue.runtime.esm.js:7653
patchChildren @ vue.runtime.esm.js:7567
processFragment @ vue.runtime.esm.js:7205
patch @ vue.runtime.esm.js:6673
patchKeyedChildren @ vue.runtime.esm.js:7653
patchChildren @ vue.runtime.esm.js:7567
patchElement @ vue.runtime.esm.js:6994
processElement @ vue.runtime.esm.js:6830
patch @ vue.runtime.esm.js:6687
componentUpdateFn @ vue.runtime.esm.js:7456
run @ vue.runtime.esm.js:180
instance.update @ vue.runtime.esm.js:7500
callWithErrorHandling @ vue.runtime.esm.js:1418
flushJobs @ vue.runtime.esm.js:1623
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
(匿名) @ vue.runtime.esm.js:7494
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
(匿名) @ uni-h5.es.js:2061
setTimeout
(匿名) @ uni-h5.es.js:2060
requestAnimationFrame
hoverReset @ uni-h5.es.js:2058
(匿名) @ uni-h5.es.js:2092
setTimeout
handleHoverStart @ uni-h5.es.js:2089
onMousedown @ uni-h5.es.js:2075
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10263

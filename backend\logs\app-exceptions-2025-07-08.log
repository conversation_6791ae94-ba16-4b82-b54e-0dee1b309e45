2025-07-08 09:22:12.974 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use ::1:3001
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8) {"error":{"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001},"exception":true,"date":"Tue Jul 08 2025 09:22:12 GMT+0800 (中国标准时间)","process":{"pid":8060,"uid":null,"gid":null,"cwd":"G:\\codingProject\\talking\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v20.11.1","argv":["G:\\codingProject\\talking\\backend\\node_modules\\ts-node\\dist\\bin.js","G:\\codingProject\\talking\\backend\\src\\scripts\\start-dev.ts"],"memoryUsage":{"rss":144175104,"heapTotal":90570752,"heapUsed":68016776,"external":7795458,"arrayBuffers":4715367}},"os":{"loadavg":[0,0,0],"uptime":1114385.984},"trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.doListen","line":2075,"method":"doListen","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookup [as oncomplete]","line":109,"method":"onlookup [as oncomplete]","native":false}]}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.725 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use ::1:3001
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8) {"error":{"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001},"exception":true,"date":"Tue Jul 08 2025 09:22:54 GMT+0800 (中国标准时间)","process":{"pid":20120,"uid":null,"gid":null,"cwd":"G:\\codingProject\\talking\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v20.11.1","argv":["G:\\codingProject\\talking\\backend\\node_modules\\ts-node\\dist\\bin.js","G:\\codingProject\\talking\\backend\\src\\scripts\\start-dev.ts"],"memoryUsage":{"rss":143736832,"heapTotal":90570752,"heapUsed":68021640,"external":7795634,"arrayBuffers":4715543}},"os":{"loadavg":[0,0,0],"uptime":1114427.734},"trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.doListen","line":2075,"method":"doListen","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookup [as oncomplete]","line":109,"method":"onlookup [as oncomplete]","native":false}]}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)

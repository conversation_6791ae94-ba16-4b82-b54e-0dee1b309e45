2025-07-08 09:22:12.974 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use ::1:3001
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8) {"error":{"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001},"exception":true,"date":"Tue Jul 08 2025 09:22:12 GMT+0800 (中国标准时间)","process":{"pid":8060,"uid":null,"gid":null,"cwd":"G:\\codingProject\\talking\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v20.11.1","argv":["G:\\codingProject\\talking\\backend\\node_modules\\ts-node\\dist\\bin.js","G:\\codingProject\\talking\\backend\\src\\scripts\\start-dev.ts"],"memoryUsage":{"rss":144175104,"heapTotal":90570752,"heapUsed":68016776,"external":7795458,"arrayBuffers":4715367}},"os":{"loadavg":[0,0,0],"uptime":1114385.984},"trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.doListen","line":2075,"method":"doListen","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookup [as oncomplete]","line":109,"method":"onlookup [as oncomplete]","native":false}]}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.725 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use ::1:3001
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8) {"error":{"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001},"exception":true,"date":"Tue Jul 08 2025 09:22:54 GMT+0800 (中国标准时间)","process":{"pid":20120,"uid":null,"gid":null,"cwd":"G:\\codingProject\\talking\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v20.11.1","argv":["G:\\codingProject\\talking\\backend\\node_modules\\ts-node\\dist\\bin.js","G:\\codingProject\\talking\\backend\\src\\scripts\\start-dev.ts"],"memoryUsage":{"rss":143736832,"heapTotal":90570752,"heapUsed":68021640,"external":7795634,"arrayBuffers":4715543}},"os":{"loadavg":[0,0,0],"uptime":1114427.734},"trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.doListen","line":2075,"method":"doListen","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookup [as oncomplete]","line":109,"method":"onlookup [as oncomplete]","native":false}]}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 10:06:54.132 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use ::1:3001
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8) {"error":{"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001},"exception":true,"date":"Tue Jul 08 2025 10:06:54 GMT+0800 (中国标准时间)","process":{"pid":20148,"uid":null,"gid":null,"cwd":"G:\\codingProject\\talking\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v20.11.1","argv":["G:\\codingProject\\talking\\backend\\node_modules\\ts-node\\dist\\bin.js","G:\\codingProject\\talking\\backend\\src\\scripts\\start-dev.ts"],"memoryUsage":{"rss":143110144,"heapTotal":90570752,"heapUsed":68715848,"external":7796827,"arrayBuffers":4716736}},"os":{"loadavg":[0,0,0],"uptime":1117067.14},"trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.doListen","line":2075,"method":"doListen","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookup [as oncomplete]","line":109,"method":"onlookup [as oncomplete]","native":false}]}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 10:11:49.749 [ERROR]: uncaughtException: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11) {"error":{},"exception":true,"date":"Tue Jul 08 2025 10:11:49 GMT+0800 (中国标准时间)","process":{"pid":37712,"uid":null,"gid":null,"cwd":"G:\\codingProject\\talking\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v20.11.1","argv":["G:\\codingProject\\talking\\backend\\node_modules\\ts-node\\dist\\bin.js","G:\\codingProject\\talking\\backend\\src\\scripts\\start-dev.ts"],"memoryUsage":{"rss":97927168,"heapTotal":45342720,"heapUsed":43441360,"external":7762470,"arrayBuffers":4652169}},"os":{"loadavg":[0,0,0],"uptime":1117362.765},"trace":[{"column":12,"file":"G:\\codingProject\\talking\\backend\\src\\websocket\\WebSocketManager.ts","function":"WebSocketManager.handleConnection","line":92,"method":"handleConnection","native":false},{"column":12,"file":"G:\\codingProject\\talking\\backend\\src\\websocket\\WebSocketManager.ts","function":null,"line":60,"method":null,"native":false},{"column":28,"file":"node:events","function":"Namespace.emit","line":518,"method":"emit","native":false},{"column":12,"file":"node:domain","function":"Namespace.emit","line":488,"method":"emit","native":false},{"column":22,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\typed-events.js","function":"Namespace.emitReserved","line":56,"method":"emitReserved","native":false},{"column":14,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\namespace.js","function":"Namespace._doConnect","line":276,"method":"_doConnect","native":false},{"column":22,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\namespace.js","function":null,"line":238,"method":null,"native":false},{"column":11,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":77,"method":null,"native":false}]}
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:13:19.660 [ERROR]: uncaughtException: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11) {"error":{},"exception":true,"date":"Tue Jul 08 2025 10:13:19 GMT+0800 (中国标准时间)","process":{"pid":19212,"uid":null,"gid":null,"cwd":"G:\\codingProject\\talking\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v20.11.1","argv":["G:\\codingProject\\talking\\backend\\node_modules\\ts-node\\dist\\bin.js","G:\\codingProject\\talking\\backend\\src\\scripts\\start-dev.ts"],"memoryUsage":{"rss":97918976,"heapTotal":45080576,"heapUsed":43467672,"external":7770998,"arrayBuffers":4660322}},"os":{"loadavg":[0,0,0],"uptime":1117452.671},"trace":[{"column":12,"file":"G:\\codingProject\\talking\\backend\\src\\websocket\\WebSocketManager.ts","function":"WebSocketManager.handleConnection","line":92,"method":"handleConnection","native":false},{"column":12,"file":"G:\\codingProject\\talking\\backend\\src\\websocket\\WebSocketManager.ts","function":null,"line":60,"method":null,"native":false},{"column":28,"file":"node:events","function":"Namespace.emit","line":518,"method":"emit","native":false},{"column":12,"file":"node:domain","function":"Namespace.emit","line":488,"method":"emit","native":false},{"column":22,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\typed-events.js","function":"Namespace.emitReserved","line":56,"method":"emitReserved","native":false},{"column":14,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\namespace.js","function":"Namespace._doConnect","line":276,"method":"_doConnect","native":false},{"column":22,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\namespace.js","function":null,"line":238,"method":null,"native":false},{"column":11,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":77,"method":null,"native":false}]}
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:15:41.922 [ERROR]: uncaughtException: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11) {"error":{},"exception":true,"date":"Tue Jul 08 2025 10:15:41 GMT+0800 (中国标准时间)","process":{"pid":43676,"uid":null,"gid":null,"cwd":"G:\\codingProject\\talking\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v20.11.1","argv":["G:\\codingProject\\talking\\backend\\node_modules\\ts-node\\dist\\bin.js","G:\\codingProject\\talking\\backend\\src\\scripts\\start-dev.ts"],"memoryUsage":{"rss":143036416,"heapTotal":91361280,"heapUsed":70154104,"external":7835258,"arrayBuffers":4755167}},"os":{"loadavg":[0,0,0],"uptime":1117594.937},"trace":[{"column":12,"file":"G:\\codingProject\\talking\\backend\\src\\websocket\\WebSocketManager.ts","function":"WebSocketManager.handleConnection","line":92,"method":"handleConnection","native":false},{"column":12,"file":"G:\\codingProject\\talking\\backend\\src\\websocket\\WebSocketManager.ts","function":null,"line":60,"method":null,"native":false},{"column":28,"file":"node:events","function":"Namespace.emit","line":518,"method":"emit","native":false},{"column":12,"file":"node:domain","function":"Namespace.emit","line":488,"method":"emit","native":false},{"column":22,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\typed-events.js","function":"Namespace.emitReserved","line":56,"method":"emitReserved","native":false},{"column":14,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\namespace.js","function":"Namespace._doConnect","line":276,"method":"_doConnect","native":false},{"column":22,"file":"G:\\codingProject\\talking\\backend\\node_modules\\socket.io\\dist\\namespace.js","function":null,"line":238,"method":null,"native":false},{"column":11,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":77,"method":null,"native":false}]}
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)

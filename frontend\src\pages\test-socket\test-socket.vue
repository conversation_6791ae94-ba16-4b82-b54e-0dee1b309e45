<template>
  <view class="test-container">
    <view class="header">
      <text class="title">Socket.io 连接测试</text>
    </view>
    
    <view class="status-section">
      <view class="status-item">
        <text class="label">连接状态:</text>
        <text :class="['status', connectionStatus]">{{ connectionText }}</text>
      </view>
      
      <view class="status-item">
        <text class="label">用户信息:</text>
        <text class="value">{{ userInfo }}</text>
      </view>
      
      <view class="status-item">
        <text class="label">重连次数:</text>
        <text class="value">{{ reconnectAttempts }}</text>
      </view>
    </view>
    
    <view class="actions">
      <button @click="testConnect" :disabled="isConnected" class="btn btn-primary">
        连接测试
      </button>
      
      <button @click="testDisconnect" :disabled="!isConnected" class="btn btn-danger">
        断开连接
      </button>
      
      <button @click="testMessage" :disabled="!isConnected" class="btn btn-success">
        发送测试消息
      </button>
      
      <button @click="clearLogs" class="btn btn-secondary">
        清空日志
      </button>
    </view>
    
    <view class="logs-section">
      <text class="logs-title">连接日志:</text>
      <scroll-view class="logs-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <text class="log-time">{{ formatTime(log.timestamp) }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSocketStore } from '@/stores/socket'
import { useUserStore } from '@/stores/user'

// 使用stores
const socketStore = useSocketStore()
const userStore = useUserStore()

// 响应式数据
const logs = ref<Array<{
  timestamp: number
  message: string
  type: 'info' | 'success' | 'error' | 'warning'
}>>([])

// 计算属性
const isConnected = computed(() => socketStore.isConnected)
const reconnectAttempts = computed(() => socketStore.reconnectAttempts)

const connectionStatus = computed(() => {
  return isConnected.value ? 'connected' : 'disconnected'
})

const connectionText = computed(() => {
  return isConnected.value ? '已连接' : '未连接'
})

const userInfo = computed(() => {
  if (userStore.userInfo) {
    return `${userStore.userInfo.username} (${userStore.userInfo.displayName || '无昵称'})`
  }
  return '未登录'
})

// 方法
const addLog = (message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') => {
  logs.value.unshift({
    timestamp: Date.now(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

const testConnect = async () => {
  try {
    addLog('开始连接Socket.io服务器...', 'info')
    await socketStore.connect()
    addLog('Socket.io连接成功！', 'success')
  } catch (error: any) {
    addLog(`连接失败: ${error.message}`, 'error')
  }
}

const testDisconnect = () => {
  try {
    addLog('断开Socket.io连接...', 'info')
    socketStore.disconnect()
    addLog('Socket.io连接已断开', 'warning')
  } catch (error: any) {
    addLog(`断开连接失败: ${error.message}`, 'error')
  }
}

const testMessage = () => {
  try {
    const testData = {
      message: 'Hello from test page!',
      timestamp: Date.now(),
      user: userStore.userInfo?.username
    }
    
    socketStore.sendMessage('test-message', testData)
    addLog('测试消息已发送', 'success')
  } catch (error: any) {
    addLog(`发送消息失败: ${error.message}`, 'error')
  }
}

const clearLogs = () => {
  logs.value = []
  addLog('日志已清空', 'info')
}

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  addLog('Socket.io测试页面已加载', 'info')
  
  // 检查用户登录状态
  if (!userStore.userInfo) {
    addLog('用户未登录，请先登录', 'warning')
  } else {
    addLog(`当前用户: ${userStore.userInfo.username}`, 'info')
  }
})

onUnmounted(() => {
  // 页面卸载时不自动断开连接，让其他页面继续使用
})
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

.status-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      font-weight: bold;
      color: #666;
    }
    
    .value {
      color: #333;
    }
    
    .status {
      padding: 4px 12px;
      border-radius: 4px;
      color: white;
      font-size: 12px;
      
      &.connected {
        background-color: #4CAF50;
      }
      
      &.disconnected {
        background-color: #f44336;
      }
    }
  }
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  
  .btn {
    flex: 1;
    min-width: 120px;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &.btn-primary {
      background-color: #2196F3;
    }
    
    &.btn-danger {
      background-color: #f44336;
    }
    
    &.btn-success {
      background-color: #4CAF50;
    }
    
    &.btn-secondary {
      background-color: #9E9E9E;
    }
  }
}

.logs-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
  .logs-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    display: block;
  }
  
  .logs-container {
    height: 300px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: #fafafa;
    
    .log-item {
      display: flex;
      margin-bottom: 8px;
      padding: 6px;
      border-radius: 4px;
      
      &.info {
        background-color: #e3f2fd;
        border-left: 3px solid #2196F3;
      }
      
      &.success {
        background-color: #e8f5e8;
        border-left: 3px solid #4CAF50;
      }
      
      &.error {
        background-color: #ffebee;
        border-left: 3px solid #f44336;
      }
      
      &.warning {
        background-color: #fff3e0;
        border-left: 3px solid #ff9800;
      }
      
      .log-time {
        color: #666;
        font-size: 12px;
        margin-right: 10px;
        min-width: 80px;
      }
      
      .log-message {
        color: #333;
        font-size: 14px;
        flex: 1;
      }
    }
  }
}
</style>

/**
 * 前端配置管理
 * 
 * 功能说明：
 * 1. 统一前端配置管理
 * 2. 环境配置切换
 * 3. 运行时配置更新
 * 4. 配置缓存和持久化
 */

import { 
  createConfigManager,
  getConfig,
  getConfigValue,
  setConfigValue,
  onConfigChange
} from '../../../shared/utils/configManager'
import { 
  AppConfig,
  Environment,
  FrontendConfig,
  DEFAULT_CONFIG
} from '../../../shared/types/config'

// 前端特定的配置覆盖
const frontendConfigOverrides: Partial<AppConfig> = {
  frontend: {
    apiBaseUrl: process.env.NODE_ENV === 'production' 
      ? 'https://your-domain.com/api'
      : 'http://localhost:3001/api',
    websocketUrl: process.env.NODE_ENV === 'production'
      ? 'https://your-domain.com'
      : 'http://localhost:3001',
    enableDevTools: process.env.NODE_ENV === 'development',
    enableMocking: process.env.NODE_ENV === 'development',
    theme: {
      primaryColor: '#667eea',
      secondaryColor: '#764ba2',
      darkMode: false
    },
    features: {
      voiceChat: true,
      fileUpload: true,
      notifications: true,
      analytics: process.env.NODE_ENV === 'production'
    },
    performance: {
      enableLazyLoading: true,
      enableCodeSplitting: true,
      enableServiceWorker: process.env.NODE_ENV === 'production',
      cacheStrategy: 'stale-while-revalidate'
    }
  },
  // 前端不需要的配置可以设置为默认值或简化版本
  database: {
    ...DEFAULT_CONFIG.database,
    filename: 'frontend.db' // 前端可能用于本地存储
  },
  security: {
    ...DEFAULT_CONFIG.security,
    // 前端不应该包含敏感的密钥信息
    jwtSecret: '',
    jwtRefreshSecret: ''
  }
}

// 创建前端配置管理器
const frontendConfigManager = createConfigManager(frontendConfigOverrides)

// 从本地存储加载用户配置
const loadUserConfig = (): void => {
  try {
    const userConfig = uni.getStorageSync('userConfig')
    if (userConfig) {
      frontendConfigManager.mergeConfig(userConfig)
    }
  } catch (error) {
    console.warn('Failed to load user config from storage:', error)
  }
}

// 保存用户配置到本地存储
const saveUserConfig = (config: Partial<AppConfig>): void => {
  try {
    uni.setStorageSync('userConfig', config)
  } catch (error) {
    console.warn('Failed to save user config to storage:', error)
  }
}

// 监听配置变更并保存到本地存储
onConfigChange('frontend', (_path, _oldValue, _newValue) => {
  const currentConfig = getConfig()
  saveUserConfig({
    frontend: currentConfig.frontend,
    // 只保存用户可配置的部分
    audio: {
      sampleRate: currentConfig.audio.sampleRate,
      bitRate: currentConfig.audio.bitRate,
      channels: currentConfig.audio.channels,
      format: currentConfig.audio.format,
      frameSize: currentConfig.audio.frameSize,
      maxDuration: currentConfig.audio.maxDuration,
      uploadPath: currentConfig.audio.uploadPath,
      maxFileSize: currentConfig.audio.maxFileSize,
      enableCompression: currentConfig.audio.enableCompression,
      compressionQuality: currentConfig.audio.compressionQuality,
      enableNoiseReduction: currentConfig.audio.enableNoiseReduction,
      enableEchoCancellation: currentConfig.audio.enableEchoCancellation
    }
  })
})

// 初始化配置
loadUserConfig()

// 导出配置
export const config = getConfig()

// 导出前端特定的配置获取函数
export const getFrontendConfig = (): FrontendConfig => {
  return getConfigValue<FrontendConfig>('frontend')
}

// 导出API配置
export const getApiConfig = () => {
  const frontendConfig = getFrontendConfig()
  return {
    baseURL: frontendConfig.apiBaseUrl,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  }
}

// 导出WebSocket配置
export const getWebSocketConfig = () => {
  const frontendConfig = getFrontendConfig()
  return {
    url: frontendConfig.websocketUrl,
    reconnectAttempts: 5,
    reconnectDelay: 3000,
    heartbeatInterval: 30000
  }
}

// 导出主题配置
export const getThemeConfig = () => {
  const frontendConfig = getFrontendConfig()
  return frontendConfig.theme
}

// 导出功能开关配置
export const getFeatureConfig = () => {
  const frontendConfig = getFrontendConfig()
  return frontendConfig.features
}

// 导出性能配置
export const getPerformanceConfig = () => {
  const frontendConfig = getFrontendConfig()
  return frontendConfig.performance
}

// 更新主题配置
export const updateThemeConfig = (theme: Partial<FrontendConfig['theme']>): void => {
  const currentTheme = getThemeConfig()
  const newTheme = { ...currentTheme, ...theme }
  setConfigValue('frontend.theme', newTheme)
}

// 切换暗色模式
export const toggleDarkMode = (): void => {
  const currentTheme = getThemeConfig()
  updateThemeConfig({ darkMode: !currentTheme.darkMode })
}

// 更新功能开关
export const updateFeatureConfig = (features: Partial<FrontendConfig['features']>): void => {
  const currentFeatures = getFeatureConfig()
  const newFeatures = { ...currentFeatures, ...features }
  setConfigValue('frontend.features', newFeatures)
}

// 检查功能是否启用
export const isFeatureEnabled = (feature: keyof FrontendConfig['features']): boolean => {
  const features = getFeatureConfig()
  return features[feature] === true
}

// 获取环境信息
export const getEnvironment = (): Environment => {
  return process.env.NODE_ENV === 'production' 
    ? Environment.PRODUCTION 
    : Environment.DEVELOPMENT
}

// 是否为开发环境
export const isDevelopment = (): boolean => {
  return getEnvironment() === Environment.DEVELOPMENT
}

// 是否为生产环境
export const isProduction = (): boolean => {
  return getEnvironment() === Environment.PRODUCTION
}

// 获取调试配置
export const getDebugConfig = () => {
  const frontendConfig = getFrontendConfig()
  return {
    enableDevTools: frontendConfig.enableDevTools,
    enableMocking: frontendConfig.enableMocking,
    isDevelopment: isDevelopment(),
    isProduction: isProduction()
  }
}

// 动态更新API地址（用于开发调试）
export const updateApiBaseUrl = (url: string): void => {
  setConfigValue('frontend.apiBaseUrl', url)
}

// 动态更新WebSocket地址（用于开发调试）
export const updateWebSocketUrl = (url: string): void => {
  setConfigValue('frontend.websocketUrl', url)
}

// 重置配置为默认值
export const resetConfig = (): void => {
  frontendConfigManager.reset()
  // 清除本地存储
  try {
    uni.removeStorageSync('userConfig')
  } catch (error) {
    console.warn('Failed to clear user config from storage:', error)
  }
}

// 导出配置管理器（用于高级功能）
export { frontendConfigManager }

// 导出共享的配置工具
export {
  getConfig,
  getConfigValue,
  setConfigValue,
  onConfigChange
}

// 默认导出
export default {
  config,
  getFrontendConfig,
  getApiConfig,
  getWebSocketConfig,
  getThemeConfig,
  getFeatureConfig,
  getPerformanceConfig,
  updateThemeConfig,
  toggleDarkMode,
  updateFeatureConfig,
  isFeatureEnabled,
  getEnvironment,
  isDevelopment,
  isProduction,
  getDebugConfig,
  updateApiBaseUrl,
  updateWebSocketUrl,
  resetConfig
}

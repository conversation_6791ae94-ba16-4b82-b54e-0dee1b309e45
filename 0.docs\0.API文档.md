# 营业厅语音对讲系统 - 后端API文档

**版本**: v1.0.0  
**最后更新**: 2025年7月7日  
**基于实际代码**: 此文档基于项目中的真实代码生成，确保准确性和时效性

## 📋 文档概述

### 系统简介

营业厅语音对讲系统是一个基于Node.js + TypeScript + Socket.io的实时语音通信系统，采用现代化的技术栈：

- **后端**: Node.js + Express + TypeScript + Socket.io + SQLite
- **前端**: uni-app + Vue 3 + Pinia + TypeScript
- **数据库**: SQLite (支持WAL模式，高并发)
- **认证**: JWT Bearer Token
- **实时通信**: WebSocket (Socket.io v4.7.0)

### 核心功能特性

- ✅ **实时语音对讲**: 支持一对一实时语音通话
- ✅ **用户认证系统**: JWT认证，自动登录/登出
- ✅ **用户状态管理**: 在线/离线/对讲中/忙碌状态
- ✅ **消息持久化**: 消息历史记录和查询
- ✅ **系统监控**: 健康检查、统计信息、配置管理
- ✅ **错误处理**: 统一错误格式和日志记录
- ✅ **自动重连**: WebSocket断线自动重连机制

### 服务器信息

- **基础URL**: `http://localhost:3001` (注意端口是3001)
- **WebSocket URL**: `ws://localhost:3001/socket.io/`
- **协议**: HTTP/HTTPS + WebSocket (Socket.io)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Bearer Token
- **数据库**: SQLite (WAL模式)

---

## 🔐 认证系统 API

### 1. 用户登录

**接口地址**: `POST /api/auth/login`

**功能说明**: 用户登录系统，支持新用户自动注册

**请求头**:
```
Content-Type: application/json
```

**请求参数**:
```json
{
  "username": "张三"  // 必填，用户名，2-50字符，支持中英文数字下划线
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "username": "张三",
      "displayName": "张三",
      "status": "online",
      "createdAt": "2025-01-04T10:00:00.000Z",
      "lastLoginAt": "2025-01-04T10:00:00.000Z",
      "permissions": {
        "microphone": true,
        "speaker": true,
        "voice_call": true,
        "message_send": true,
        "message_receive": true,
        "backend_call": true
      }
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "24h"
  }
}
```

**错误响应** (400):
```json
{
  "success": false,
  "message": "用户名长度必须在2-50个字符之间",
  "code": "INVALID_USERNAME"
}
```

### 2. 用户登出

**接口地址**: `POST /api/auth/logout`

**功能说明**: 用户登出系统，更新用户状态为离线

**请求头**:
```
Authorization: Bearer <JWT_TOKEN>
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### 3. Token刷新

**接口地址**: `POST /api/auth/refresh`

**请求参数**:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "24h"
  }
}
```

---

## 👥 用户管理 API

### 1. 获取当前用户信息

**接口地址**: `GET /api/users/profile`

**请求头**:
```
Authorization: Bearer <JWT_TOKEN>
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "User profile retrieved successfully",
  "data": {
    "user": {
      "id": 1,
      "username": "张三",
      "displayName": "张三",
      "status": "online",
      "createdAt": "2025-01-04T10:00:00.000Z",
      "lastLoginAt": "2025-01-04T10:00:00.000Z",
      "lastLogoutAt": null
    }
  }
}
```

### 2. 更新用户信息

**接口地址**: `PUT /api/users/profile`

**请求参数**:
```json
{
  "displayName": "张三-客服",  // 可选，显示名称
  "status": "online"          // 可选，用户状态
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "User profile updated successfully",
  "data": {
    "user": {
      "id": 1,
      "username": "张三",
      "displayName": "张三-客服",
      "status": "online",
      "createdAt": "2025-01-04T10:00:00.000Z",
      "lastLoginAt": "2025-01-04T10:00:00.000Z",
      "lastLogoutAt": null
    }
  }
}
```

### 3. 获取在线用户列表

**接口地址**: `GET /api/users/online`

**成功响应** (200):
```json
{
  "success": true,
  "message": "Online users retrieved successfully",
  "data": {
    "users": [
      {
        "id": 2,
        "username": "李四",
        "displayName": "李四-客服",
        "status": "online",
        "lastLoginAt": "2025-01-04T09:30:00.000Z"
      },
      {
        "id": 3,
        "username": "王五",
        "displayName": "王五-主管",
        "status": "talking",
        "lastLoginAt": "2025-01-04T09:45:00.000Z"
      }
    ],
    "total": 2
  }
}
```

### 4. 搜索用户

**接口地址**: `GET /api/users/search`

**查询参数**:
- `keyword`: 搜索关键词（可选）
- `status`: 用户状态筛选，all|online|offline|talking|busy（可选，默认all）
- `page`: 页码（可选，默认1）
- `limit`: 每页数量（可选，默认20，最大100）

**成功响应** (200):
```json
{
  "success": true,
  "message": "Users search completed successfully",
  "data": {
    "users": [
      {
        "id": 2,
        "username": "李四",
        "displayName": "李四-客服",
        "status": "online",
        "lastLoginAt": "2025-01-04T09:30:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

---

## 💬 消息管理 API

### 1. 发送消息

**接口地址**: `POST /api/messages/send`

**请求参数**:
```json
{
  "content": "请到3号窗口服务客户",       // 必填，消息内容
  "type": "text",                    // 可选，消息类型，默认text
  "targetUserId": 2,                 // 可选，目标用户ID
  "priority": "normal"               // 可选，优先级，默认normal
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "messageId": 123,
    "content": "请到3号窗口服务客户",
    "type": "text",
    "priority": "normal",
    "status": "sent",
    "createdAt": "2025-01-04T10:00:00.000Z",
    "sender": {
      "id": 1,
      "username": "张三",
      "displayName": "张三-客服"
    }
  }
}
```

### 2. 获取消息历史

**接口地址**: `GET /api/messages/history`

**查询参数**:
- `page`: 页码（可选，默认1）
- `limit`: 每页数量（可选，默认20，最大100）
- `type`: 消息类型筛选（可选，默认all）
- `startDate`: 开始日期（可选）
- `endDate`: 结束日期（可选）
- `keyword`: 搜索关键词（可选）

**成功响应** (200):
```json
{
  "success": true,
  "message": "Message history retrieved successfully",
  "data": {
    "messages": [
      {
        "id": 123,
        "content": "请到3号窗口服务客户",
        "type": "text",
        "priority": "normal",
        "status": "sent",
        "createdAt": "2025-01-04T10:00:00.000Z",
        "sender": {
          "id": 1,
          "username": "张三",
          "displayName": "张三-客服"
        },
        "receiver": {
          "id": 2,
          "username": "李四",
          "displayName": "李四-客服"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

---

## 🖥️ 系统管理 API

### 1. 系统状态检查

**接口地址**: `GET /api/system/status`

**成功响应** (200):
```json
{
  "success": true,
  "message": "System status retrieved successfully",
  "data": {
    "server": {
      "uptime": 3600,
      "nodeVersion": "v18.17.0",
      "platform": "win32",
      "arch": "x64",
      "pid": 12345,
      "memory": {
        "rss": 45678912,
        "heapTotal": 23456789,
        "heapUsed": 12345678,
        "external": 1234567
      }
    },
    "system": {
      "hostname": "DESKTOP-ABC123",
      "type": "Windows_NT",
      "release": "10.0.26100",
      "totalMemory": 17179869184,
      "freeMemory": 8589934592,
      "cpus": 8,
      "loadAverage": [0.5, 0.3, 0.2]
    },
    "database": {
      "connected": true,
      "version": "SQLite 3.x"
    },
    "timestamp": "2025-01-04T10:00:00.000Z"
  }
}
```

### 2. 系统统计信息

**接口地址**: `GET /api/system/stats`

**查询参数**:
- `startDate`: 开始日期（可选，默认30天前）
- `endDate`: 结束日期（可选，默认当前时间）
- `type`: 统计类型，all|users|messages|calls（可选，默认all）

**成功响应** (200):
```json
{
  "success": true,
  "message": "System statistics retrieved successfully",
  "data": {
    "stats": {
      "users": {
        "total": 10,
        "online": 5,
        "talking": 2,
        "newToday": 1
      },
      "messages": {
        "total": 150,
        "todayCount": 25,
        "averagePerDay": 15
      },
      "calls": {
        "total": 50,
        "todayCount": 8,
        "averageDuration": 120
      }
    },
    "dateRange": {
      "startDate": "2024-12-05T10:00:00.000Z",
      "endDate": "2025-01-04T10:00:00.000Z"
    },
    "generatedAt": "2025-01-04T10:00:00.000Z"
  }
}
```

### 3. 系统配置管理

**获取配置**: `GET /api/system/config`
**更新配置**: `PUT /api/system/config`

**更新配置请求参数**:
```json
{
  "key": "max_concurrent_calls",
  "value": "100",
  "description": "最大并发通话数"
}
```

---

## 🔄 WebSocket 事件协议

### 连接信息

- **WebSocket URL**: `ws://localhost:3001/socket.io/`
- **协议**: Socket.io v4.7.0
- **传输方式**: WebSocket, HTTP长轮询（自动降级）
- **认证**: 通过连接参数传递token

### 连接建立

```javascript
const socket = io('http://localhost:3001', {
  query: {
    token: 'your-jwt-token'
  },
  transports: ['websocket', 'polling']
})
```

## 客户端发送事件

### 1. 用户登录

**事件名**: `user-login`

**数据格式**:
```json
{
  "username": "张三"
}
```

### 2. 发起语音通话

**事件名**: `voice-start`

**数据格式**:
```json
{
  "targetUserId": 2
}
```

### 3. 语音数据传输

**事件名**: `voice-data`

**数据格式**:
```json
{
  "sessionId": "session_12345",
  "audioData": "base64_encoded_audio_data",
  "timestamp": 1704110400000
}
```

### 4. 结束语音通话

**事件名**: `voice-end`

**数据格式**:
```json
{
  "sessionId": "session_12345",
  "reason": "normal"
}
```

### 5. 用户状态变更

**事件名**: `user-status-change`

**数据格式**:
```json
{
  "status": "talking"
}
```

### 6. 发送文本消息

**事件名**: `message-send`

**数据格式**:
```json
{
  "targetUserId": 2,
  "content": "你好",
  "messageId": "msg_12345"
}
```

### 7. 心跳检测

**事件名**: `heartbeat`

**数据格式**:
```json
{
  "clientTime": 1704110400000,
  "id": "heartbeat_12345"
}
```

## 服务端推送事件

### 1. 用户上线通知

**事件名**: `user-online`

**数据格式**:
```json
{
  "userId": 1,
  "username": "张三",
  "displayName": "张三-客服",
  "status": "online"
}
```

### 2. 用户下线通知

**事件名**: `user-offline`

**数据格式**:
```json
{
  "userId": 1,
  "username": "张三"
}
```

### 3. 用户状态更新

**事件名**: `user-status-update`

**数据格式**:
```json
{
  "userId": 1,
  "username": "张三",
  "status": "talking",
  "previousStatus": "online"
}
```

### 4. 语音通话建立成功

**事件名**: `voice-start-success`

**数据格式**:
```json
{
  "sessionId": "session_12345",
  "targetUser": {
    "id": 2,
    "username": "李四",
    "displayName": "李四-客服"
  }
}
```

### 5. 收到语音通话请求

**事件名**: `voice-call-incoming`

**数据格式**:
```json
{
  "sessionId": "session_12345",
  "fromUser": {
    "id": 1,
    "username": "张三",
    "displayName": "张三-客服"
  }
}
```

### 6. 语音数据转发

**事件名**: `voice-data`

**数据格式**:
```json
{
  "sessionId": "session_12345",
  "audioData": "base64_encoded_audio_data",
  "timestamp": 1704110400000,
  "fromUserId": 1
}
```

### 7. 语音通话结束

**事件名**: `voice-end`

**数据格式**:
```json
{
  "sessionId": "session_12345",
  "reason": "normal",
  "duration": 120000
}
```

### 8. 通话忙线提示

**事件名**: `voice-start-failed`

**数据格式**:
```json
{
  "reason": "Target user not available",
  "code": "USER_NOT_AVAILABLE"
}
```

### 9. 接收文本消息

**事件名**: `message-receive`

**数据格式**:
```json
{
  "messageId": "msg_12345",
  "content": "你好",
  "fromUser": {
    "id": 1,
    "username": "张三",
    "displayName": "张三-客服"
  },
  "timestamp": 1704110400000
}
```

### 10. 在线用户列表

**事件名**: `online-users`

**数据格式**:
```json
[
  {
    "id": 1,
    "username": "张三",
    "displayName": "张三-客服",
    "status": "online"
  },
  {
    "id": 2,
    "username": "李四",
    "displayName": "李四-客服",
    "status": "talking"
  }
]
```

### 11. 错误消息

**事件名**: `error`

**数据格式**:
```json
{
  "message": "权限不足",
  "code": "PERMISSION_DENIED",
  "details": {
    "requiredPermission": "voice_call"
  }
}
```

---

## 📊 数据库设计

### 用户表 (users)

```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100),
  avatar_url VARCHAR(255),
  status VARCHAR(20) DEFAULT 'offline',
  last_active_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 会话表 (sessions)

```sql
CREATE TABLE sessions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id VARCHAR(100) NOT NULL UNIQUE,
  user1_id INTEGER NOT NULL,
  user2_id INTEGER NOT NULL,
  status VARCHAR(20) DEFAULT 'active',
  start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  end_time DATETIME,
  duration INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user1_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (user2_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 消息表 (messages)

```sql
CREATE TABLE messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  message_id VARCHAR(100) NOT NULL UNIQUE,
  type VARCHAR(20) NOT NULL,
  from_user_id INTEGER,
  to_user_id INTEGER,
  session_id INTEGER,
  content TEXT,
  metadata TEXT,
  priority VARCHAR(20) DEFAULT 'normal',
  status VARCHAR(20) DEFAULT 'sent',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (to_user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
);
```

### 系统配置表 (system_configs)

```sql
CREATE TABLE system_configs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  config_key VARCHAR(100) NOT NULL UNIQUE,
  config_value TEXT,
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔧 错误码说明

### HTTP状态码

| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 请求成功 | 正常API调用 |
| 400 | 请求参数错误 | 缺少必填参数、数据格式错误 |
| 401 | 认证失败 | Token无效、Token过期 |
| 403 | 权限不足 | 无相应操作权限 |
| 404 | 资源不存在 | 用户不存在、消息不存在 |
| 429 | 请求过于频繁 | 触发速率限制 |
| 500 | 服务器内部错误 | 数据库连接失败、代码异常 |

### 业务错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| INVALID_USERNAME | 用户名格式错误 | 检查用户名长度和字符规范 |
| USER_NOT_FOUND | 用户不存在 | 确认用户名是否正确 |
| USER_OFFLINE | 用户不在线 | 等待用户上线或选择其他用户 |
| USER_NOT_AVAILABLE | 用户不可用 | 用户正在通话中，稍后重试 |
| PERMISSION_DENIED | 权限不足 | 检查用户权限设置 |
| SESSION_EXPIRED | 会话过期 | 重新登录获取新token |
| RATE_LIMIT_EXCEEDED | 请求频率超限 | 降低请求频率 |

---

## 📝 使用示例

### JavaScript 客户端示例

#### API调用示例

```javascript
// 用户登录
async function login(username) {
  const response = await fetch('http://localhost:3001/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username: username
    })
  });
  
  const result = await response.json();
  if (result.success) {
    // 保存token
    localStorage.setItem('token', result.data.token);
    return result.data.user;
  } else {
    throw new Error(result.message);
  }
}

// 获取在线用户
async function getOnlineUsers() {
  const token = localStorage.getItem('token');
  const response = await fetch('http://localhost:3001/api/users/online', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  return result.data.users;
}

// 发送消息
async function sendMessage(content, targetUserId) {
  const token = localStorage.getItem('token');
  const response = await fetch('http://localhost:3001/api/messages/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      content: content,
      targetUserId: targetUserId
    })
  });
  
  const result = await response.json();
  return result.data;
}
```

#### WebSocket连接示例

```javascript
// 连接WebSocket
const token = localStorage.getItem('token');
const socket = io('http://localhost:3001', {
  query: {
    token: token
  },
  transports: ['websocket', 'polling']
});

// 监听连接事件
socket.on('connect', () => {
  console.log('WebSocket连接已建立');
  
  // 发送用户登录事件
  socket.emit('user-login', {
    username: '张三'
  });
});

// 监听用户状态更新
socket.on('user-status-update', (data) => {
  console.log(`用户 ${data.username} 状态更新为: ${data.status}`);
});

// 监听语音通话请求
socket.on('voice-call-incoming', (data) => {
  console.log(`收到来自 ${data.fromUser.username} 的语音通话请求`);
  // 显示接听界面
  showIncomingCallDialog(data);
});

// 发起语音通话
function startVoiceCall(targetUserId) {
  socket.emit('voice-start', {
    targetUserId: targetUserId
  });
}

// 监听语音通话建立成功
socket.on('voice-start-success', (data) => {
  console.log('语音通话建立成功:', data);
  // 开始录音和播放
  startVoiceSession(data.sessionId);
});

// 发送语音数据
function sendVoiceData(sessionId, audioData) {
  socket.emit('voice-data', {
    sessionId: sessionId,
    audioData: audioData,
    timestamp: Date.now()
  });
}

// 结束语音通话
function endVoiceCall(sessionId) {
  socket.emit('voice-end', {
    sessionId: sessionId,
    reason: 'normal'
  });
}
```

### uni-app客户端示例

```javascript
// 使用项目中的Socket.io管理器
import { useSocketStore } from '@/stores/socket'

// 连接Socket.io
async function connectToServer() {
  try {
    const socketStore = useSocketStore();
    await socketStore.connect();
    console.log('Socket.io连接成功');

    // Socket store会自动处理事件监听
    // 监听用户状态更新
    watch(() => socketStore.onlineUsers, (users) => {
      console.log('在线用户更新:', users);
    });

    // 监听来电
    watch(() => socketStore.incomingCall, (call) => {
      if (call) {
        // 显示来电界面
        uni.showModal({
          title: '来电',
          content: `${call.caller.displayName || call.caller.username} 向您发起语音通话`,
          showCancel: true,
          confirmText: '接听',
          cancelText: '拒绝',
          success: (res) => {
            if (res.confirm) {
              // 接听电话
              socketStore.acceptCall();
            } else {
              // 拒绝电话
              socketStore.rejectCall();
            }
          }
        });
      }
    });

  } catch (error) {
    console.error('Socket.io连接失败:', error);
  }
}

// 发起语音通话
function startCall(targetUserId) {
  sendMessage('voice-start', {
    targetUserId: targetUserId
  });
}
```

---

## 🚀 部署和运维

### 环境要求

- Node.js 18.x 或更高版本
- npm 8.x 或更高版本
- SQLite3 数据库
- PM2 进程管理器（生产环境推荐）

### 环境变量配置

创建 `.env` 文件：

```bash
# 服务器配置
PORT=3001
HOST=localhost
NODE_ENV=production
CORS_ORIGIN=http://localhost:8080,https://your-domain.com

# 数据库配置
DB_FILENAME=talking.db
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=30000

# WebSocket配置
WS_CORS_ORIGIN=http://localhost:8080,https://your-domain.com
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000
WS_MAX_CONNECTIONS=100

# 安全配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=12

# 日志配置
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14
```

### 启动命令

```bash
# 开发环境
npm run dev

# 生产环境
npm start

# PM2 管理
pm2 start ecosystem.config.js
pm2 status
pm2 logs voice-chat-server
pm2 restart voice-chat-server
pm2 stop voice-chat-server
```

### 健康检查

```bash
# 检查服务状态
curl http://localhost:3001/health

# 检查API可用性
curl -H "Authorization: Bearer your-token" http://localhost:3001/api/system/status
```

---

## 📈 性能优化建议

### 数据库优化

```sql
-- 添加索引提升查询性能
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_sessions_status ON sessions(status);

-- 定期清理历史数据
DELETE FROM messages WHERE created_at < datetime('now', '-30 days');
DELETE FROM sessions WHERE status = 'ended' AND end_time < datetime('now', '-7 days');
```

### WebSocket优化

- 设置合理的心跳检测间隔（30秒）
- 限制单用户最大连接数
- 实现连接池管理和负载均衡
- 使用消息压缩减少带宽占用

### 服务器优化

- 使用PM2集群模式提高并发处理能力
- 配置Nginx反向代理和负载均衡
- 启用gzip压缩减少传输数据量
- 实现Redis缓存提升响应速度

---

## 🔒 安全说明

### 数据安全

- 所有API接口使用HTTPS加密传输（生产环境）
- WebSocket连接支持WSS安全协议
- JWT Token采用HS256算法签名
- 数据库文件权限控制，仅服务进程可访问

### 访问控制

- JWT Token认证，24小时有效期
- 刷新Token机制，7天有效期
- API请求速率限制，15分钟内最多100个请求
- WebSocket连接数限制，单IP最多10个连接

### 日志和监控

```bash
# 访问日志
tail -f logs/app-access.log

# 错误日志
tail -f logs/app-error.log

# 系统监控
pm2 monit
```

---

## 🐛 故障排除

### 常见问题

#### 1. WebSocket连接失败

**症状**: 前端无法建立WebSocket连接

**解决方案**:
```bash
# 检查服务器状态
pm2 status

# 检查端口占用
netstat -tulpn | grep 3001

# 检查防火墙设置
# Windows
netsh advfirewall firewall add rule name="Voice Chat" dir=in action=allow protocol=TCP localport=3001

# 重启服务
pm2 restart voice-chat-server
```

#### 2. 数据库连接错误

**症状**: API返回500错误，日志显示数据库错误

**解决方案**:
```bash
# 检查数据库文件权限
ls -la data/talking.db

# 修复数据库文件权限
chmod 664 data/talking.db

# 重建数据库（谨慎操作）
rm data/talking.db
npm start  # 会自动创建新数据库
```

#### 3. JWT Token验证失败

**症状**: 认证接口返回401错误

**解决方案**:
- 检查Token是否过期
- 确认请求头格式：`Authorization: Bearer <token>`
- 验证JWT_SECRET环境变量设置是否正确

---

## 📚 相关文档

- [前端开发指南](./useUNIAPP.md)
- [WebSocket通信协议](./WebSocket通信协议规范.md)
- [数据库设计规范](./数据库设计规范文档.md)
- [错误处理规范](./错误处理和日志规范.md)
- [系统部署指南](./APK打包指南.md)

---

## 📞 技术支持

- **项目版本**: v1.0.0
- **文档版本**: v1.0.0
- **最后更新**: 2025年1月4日
- **维护状态**: 活跃开发中

---

*本文档基于营业厅语音对讲系统实际代码生成，确保与系统实现保持一致。如有疑问请查看源代码或联系开发团队。*

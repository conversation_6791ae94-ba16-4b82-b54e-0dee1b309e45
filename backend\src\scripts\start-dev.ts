/**
 * 开发环境启动脚本
 * 
 * 功能说明：
 * 1. 初始化数据库
 * 2. 启动后端服务器
 * 3. 运行基础测试
 * 4. 提供开发环境信息
 */

import app from '../app'
import { DatabaseManager } from '../database/DatabaseManager'
import { config } from '../config/config'
import { logger } from '../utils/logger'
import { runIntegrationTests } from '../test/integration-test'

class DevServer {
  private dbManager: DatabaseManager

  constructor() {
    this.dbManager = DatabaseManager.getInstance()
  }

  /**
   * 启动开发服务器
   */
  public async start(): Promise<void> {
    try {
      logger.info('🚀 启动开发服务器...')
      
      // 1. 运行集成测试（可选）
      if (process.env['RUN_TESTS'] === 'true') {
        await this.runTests()
      }

      // 2. 启动HTTP服务器（包含数据库初始化）
      await this.startHttpServer()
      
      // 3. 显示开发信息
      this.showDevInfo()
      
    } catch (error) {
      logger.error('启动开发服务器失败:', error)
      process.exit(1)
    }
  }



  /**
   * 运行集成测试
   */
  private async runTests(): Promise<void> {
    logger.info('🧪 运行集成测试...')
    
    try {
      await runIntegrationTests()
      logger.info('✅ 集成测试通过')
    } catch (error) {
      logger.warn('⚠️ 集成测试失败，但继续启动服务器:', error)
    }
  }

  /**
   * 启动HTTP服务器
   */
  private async startHttpServer(): Promise<void> {
    try {
      // 使用Application类的start方法来启动完整的服务器（包括WebSocket和数据库）
      await app.start()

      logger.info(`🌐 服务器启动成功（包含WebSocket）`)
      logger.info(`📍 HTTP地址: http://${config.server.host}:${config.server.port}`)
      logger.info(`🔌 WebSocket地址: ws://${config.server.host}:${config.server.port}/socket.io`)

    } catch (error) {
      if ((error as any).code === 'EADDRINUSE') {
        logger.error(`❌ 端口 ${config.server.port} 已被占用`)
      } else {
        logger.error('❌ 服务器启动失败:', error)
      }
      throw error
    }
  }

  /**
   * 显示开发信息
   */
  private showDevInfo(): void {
    const divider = '='.repeat(60)
    
    console.log(`\n${divider}`)
    console.log('🎉 语音对讲系统开发服务器已启动')
    console.log(`${divider}`)
    console.log(`📍 服务器地址: http://${config.server.host}:${config.server.port}`)
    console.log(`🔌 WebSocket地址: ws://${config.server.host}:${config.server.port}`)
    console.log(`📊 API文档: http://${config.server.host}:${config.server.port}/api-docs`)
    console.log(`🗄️ 数据库: ${config.database.filename}`)
    console.log(`📝 日志级别: ${config.logging.level}`)
    console.log(`🔧 环境: ${process.env['NODE_ENV'] || 'development'}`)
    console.log(`${divider}`)
    console.log('📋 可用的API端点:')
    console.log('  POST /api/auth/login          - 用户登录')
    console.log('  POST /api/auth/logout         - 用户登出')
    console.log('  GET  /api/users/profile       - 获取用户信息')
    console.log('  GET  /api/users/online        - 获取在线用户')
    console.log('  POST /api/messages/send       - 发送消息')
    console.log('  GET  /api/messages/history    - 获取消息历史')
    console.log('  GET  /api/system/status       - 系统状态')
    console.log(`${divider}`)
    console.log('🔌 WebSocket事件:')
    console.log('  user-status-change            - 用户状态变更')
    console.log('  backend-call                  - 后台管理消息')
    console.log('  voice-start/voice-data/voice-end - 语音通话')
    console.log('  call-request/call-response    - 通话请求')
    console.log(`${divider}`)
    console.log('💡 开发提示:')
    console.log('  - 使用 Ctrl+C 停止服务器')
    console.log('  - 修改代码后需要重启服务器')
    console.log('  - 查看日志文件: logs/app.log')
    console.log('  - 数据库文件: data/voice_intercom.db')
    console.log(`${divider}\n`)
  }

  /**
   * 优雅关闭服务器
   */
  public async shutdown(): Promise<void> {
    logger.info('🛑 正在关闭服务器...')
    
    try {
      // 关闭数据库连接
      await this.dbManager.close()
      logger.info('✅ 数据库连接已关闭')
      
      // 服务器关闭由Application类的graceful shutdown处理
      logger.info('👋 服务器已优雅关闭')
      
    } catch (error) {
      logger.error('关闭服务器时出错:', error)
    }
  }
}

// 创建开发服务器实例
const devServer = new DevServer()

// 处理进程信号
process.on('SIGINT', async () => {
  console.log('\n收到 SIGINT 信号，正在关闭服务器...')
  await devServer.shutdown()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('\n收到 SIGTERM 信号，正在关闭服务器...')
  await devServer.shutdown()
  process.exit(0)
})

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error)
  devServer.shutdown().then(() => {
    process.exit(1)
  })
})

process.on('unhandledRejection', (reason) => {
  logger.error('未处理的Promise拒绝:', reason)
  devServer.shutdown().then(() => {
    process.exit(1)
  })
})

// 启动服务器
devServer.start().catch((error) => {
  logger.error('启动服务器失败:', error)
  process.exit(1)
})

/**
 * 代码质量检查脚本
 * 
 * 功能说明：
 * 1. 检查代码重复度
 * 2. 验证模块依赖关系
 * 3. 检查类型安全性
 * 4. 验证配置一致性
 * 5. 生成质量报告
 */

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'

// ESLint问题详情接口
interface ESLintIssue {
  file: string
  line: number
  column: number
  message: string
  ruleId: string
  severity: 'error' | 'warning'
}

// 依赖问题详情接口
interface DependencyIssue {
  name: string
  type: 'unused' | 'outdated' | 'vulnerable' | 'missing'
  currentVersion?: string
  recommendedVersion?: string
  description: string
}

interface QualityMetrics {
  duplicateCodePercentage: number
  typeScriptErrors: number
  eslintWarnings: number
  eslintErrors: number
  eslintIssues: ESLintIssue[]
  testCoverage: number
  moduleComplexity: number
  dependencyIssues: DependencyIssue[]
  configurationIssues: string[]
}

interface QualityReport {
  timestamp: string
  overallScore: number
  metrics: QualityMetrics
  recommendations: string[]
  passedChecks: string[]
  failedChecks: string[]
}

class CodeQualityChecker {
  private projectRoot: string
  private report: QualityReport

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot
    this.report = {
      timestamp: new Date().toISOString(),
      overallScore: 0,
      metrics: {
        duplicateCodePercentage: 0,
        typeScriptErrors: 0,
        eslintWarnings: 0,
        eslintErrors: 0,
        eslintIssues: [],
        testCoverage: 0,
        moduleComplexity: 0,
        dependencyIssues: [],
        configurationIssues: []
      },
      recommendations: [],
      passedChecks: [],
      failedChecks: []
    }
  }

  /**
   * 运行完整的质量检查
   */
  async runQualityCheck(): Promise<QualityReport> {
    console.log('🔍 开始代码质量检查...\n')

    try {
      await this.checkDuplicateCode()
      await this.checkTypeScript()
      await this.checkESLint()
      await this.checkTestCoverage()
      await this.checkModuleComplexity()
      await this.checkDependencies()
      await this.checkConfiguration()
      await this.validateRefactoring()

      this.calculateOverallScore()
      this.generateRecommendations()

      console.log('\n✅ 代码质量检查完成')
      return this.report
    } catch (error) {
      console.error('❌ 质量检查过程中出现错误:', error)
      throw error
    }
  }

  /**
   * 检查代码重复度
   */
  private async checkDuplicateCode(): Promise<void> {
    console.log('📊 检查代码重复度...')
    
    try {
      // 使用jscpd检查代码重复
      execSync('npx jscpd --threshold 10 --reporters json --output ./jscpd-report.json', {
        cwd: this.projectRoot,
        encoding: 'utf8'
      })

      // 尝试读取生成的JSON报告文件
      const reportPath = path.join(this.projectRoot, 'jscpd-report.json')
      let duplicatePercentage = 0

      if (fs.existsSync(reportPath)) {
        const duplicateData = JSON.parse(fs.readFileSync(reportPath, 'utf8'))
        duplicatePercentage = duplicateData.statistics?.total?.percentage || 0
        // 清理临时文件
        fs.unlinkSync(reportPath)
      } else {
        // 如果没有报告文件，说明没有重复代码
        duplicatePercentage = 0
      }

      this.report.metrics.duplicateCodePercentage = duplicatePercentage

      if (duplicatePercentage < 5) {
        this.report.passedChecks.push('代码重复度低于5%')
      } else if (duplicatePercentage < 10) {
        this.report.passedChecks.push('代码重复度在可接受范围内')
        this.report.recommendations.push('考虑进一步减少代码重复')
      } else {
        this.report.failedChecks.push(`代码重复度过高: ${duplicatePercentage}%`)
        this.report.recommendations.push('需要重构重复代码')
      }

      console.log(`   代码重复度: ${duplicatePercentage}%`)
    } catch {
      console.log('   ⚠️  无法检查代码重复度 (可能需要安装jscpd)')
      this.report.recommendations.push('安装jscpd工具以检查代码重复度')
    }
  }

  /**
   * 检查TypeScript类型错误
   */
  private async checkTypeScript(): Promise<void> {
    console.log('🔧 检查TypeScript类型错误...')
    
    try {
      execSync('npx tsc --noEmit', {
        cwd: this.projectRoot,
        encoding: 'utf8'
      })
      
      this.report.metrics.typeScriptErrors = 0
      this.report.passedChecks.push('TypeScript类型检查通过')
      console.log('   ✅ 无TypeScript类型错误')
    } catch (error: any) {
      const errorOutput = error.toString()
      const errorCount = (errorOutput.match(/error TS\d+:/g) || []).length
      
      this.report.metrics.typeScriptErrors = errorCount
      this.report.failedChecks.push(`发现${errorCount}个TypeScript类型错误`)
      this.report.recommendations.push('修复TypeScript类型错误')
      
      console.log(`   ❌ 发现${errorCount}个TypeScript类型错误`)
    }
  }

  /**
   * 检查ESLint规则
   */
  private async checkESLint(): Promise<void> {
    console.log('📝 检查ESLint规则...')

    try {
      let result: string
      try {
        result = execSync('npx eslint . --format json', {
          cwd: this.projectRoot,
          encoding: 'utf8'
        })
      } catch (error: any) {
        // ESLint返回非零退出码时仍然可能有有效的JSON输出
        if (error.stdout) {
          result = error.stdout
        } else {
          throw error
        }
      }

      const eslintResults = JSON.parse(result)
      let errorCount = 0
      let warningCount = 0
      const issues: ESLintIssue[] = []

      eslintResults.forEach((file: any) => {
        const relativePath = path.relative(this.projectRoot, file.filePath)

        file.messages.forEach((message: any) => {
          const issue: ESLintIssue = {
            file: relativePath,
            line: message.line || 0,
            column: message.column || 0,
            message: message.message,
            ruleId: message.ruleId || 'unknown',
            severity: message.severity === 2 ? 'error' : 'warning'
          }

          issues.push(issue)

          if (message.severity === 2) errorCount++
          else if (message.severity === 1) warningCount++
        })
      })

      this.report.metrics.eslintErrors = errorCount
      this.report.metrics.eslintWarnings = warningCount
      this.report.metrics.eslintIssues = issues

      if (errorCount === 0 && warningCount === 0) {
        this.report.passedChecks.push('ESLint检查通过')
        console.log('   ✅ 无ESLint错误或警告')
      } else {
        if (errorCount > 0) {
          this.report.failedChecks.push(`发现${errorCount}个ESLint错误`)
          console.log(`   🔴 发现${errorCount}个ESLint错误`)
          // 显示前5个错误
          const errors = issues.filter(issue => issue.severity === 'error').slice(0, 5)
          errors.forEach(error => {
            console.log(`      ${error.file}:${error.line}:${error.column} - ${error.message} (${error.ruleId})`)
          })
          if (errorCount > 5) {
            console.log(`      ... 还有${errorCount - 5}个错误`)
          }
        }
        if (warningCount > 0) {
          this.report.recommendations.push(`修复${warningCount}个ESLint警告`)
          console.log(`   🟡 发现${warningCount}个ESLint警告`)
          // 显示前3个警告
          const warnings = issues.filter(issue => issue.severity === 'warning').slice(0, 3)
          warnings.forEach(warning => {
            console.log(`      ${warning.file}:${warning.line}:${warning.column} - ${warning.message} (${warning.ruleId})`)
          })
          if (warningCount > 3) {
            console.log(`      ... 还有${warningCount - 3}个警告`)
          }
        }
      }
    } catch (error: any) {
      console.log('   ⚠️  无法运行ESLint检查')
      console.log(`   错误详情: ${error.message}`)
      this.report.recommendations.push('配置ESLint以进行代码质量检查')
    }
  }

  /**
   * 检查测试覆盖率
   */
  private async checkTestCoverage(): Promise<void> {
    console.log('🧪 检查测试覆盖率...')
    
    try {
      execSync('npm test -- --coverage --coverageReporters=json-summary', {
        cwd: this.projectRoot,
        encoding: 'utf8'
      })
      
      const coverageFile = path.join(this.projectRoot, 'coverage/coverage-summary.json')
      if (fs.existsSync(coverageFile)) {
        const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'))
        const totalCoverage = coverage.total.lines.pct
        
        this.report.metrics.testCoverage = totalCoverage
        
        if (totalCoverage >= 80) {
          this.report.passedChecks.push(`测试覆盖率良好: ${totalCoverage}%`)
        } else if (totalCoverage >= 60) {
          this.report.recommendations.push('提高测试覆盖率至80%以上')
        } else {
          this.report.failedChecks.push(`测试覆盖率过低: ${totalCoverage}%`)
          this.report.recommendations.push('大幅提高测试覆盖率')
        }
        
        console.log(`   测试覆盖率: ${totalCoverage}%`)
      }
    } catch {
      console.log('   ⚠️  无法获取测试覆盖率')
      this.report.recommendations.push('配置测试覆盖率检查')
    }
  }

  /**
   * 检查模块复杂度
   */
  private async checkModuleComplexity(): Promise<void> {
    console.log('📈 检查模块复杂度...')
    
    try {
      // 简单的复杂度检查：统计文件行数和函数数量
      const sourceFiles = this.getSourceFiles()
      let totalLines = 0
      let _totalFunctions = 0
      let complexFiles = 0
      
      sourceFiles.forEach(file => {
        const content = fs.readFileSync(file, 'utf8')
        const lines = content.split('\n').length
        const functions = (content.match(/function\s+\w+|=>\s*{|:\s*\(/g) || []).length
        
        totalLines += lines
        _totalFunctions += functions
        
        if (lines > 300) {
          complexFiles++
        }
      })
      
      const avgComplexity = totalLines / sourceFiles.length
      this.report.metrics.moduleComplexity = avgComplexity
      
      if (complexFiles === 0) {
        this.report.passedChecks.push('模块复杂度合理')
      } else {
        this.report.recommendations.push(`${complexFiles}个文件过于复杂，建议拆分`)
      }
      
      console.log(`   平均文件行数: ${Math.round(avgComplexity)}`)
      console.log(`   复杂文件数量: ${complexFiles}`)
    } catch {
      console.log('   ⚠️  无法检查模块复杂度')
    }
  }

  /**
   * 检查依赖关系
   */
  private async checkDependencies(): Promise<void> {
    console.log('📦 检查依赖关系...')

    try {
      // 检查package.json依赖
      const packageJsonPath = path.join(this.projectRoot, 'package.json')
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }

        console.log(`   📋 总依赖数: ${Object.keys(dependencies).length}`)

        // 检查各种依赖问题
        const dependencyIssues: DependencyIssue[] = []

        // 检查未使用的依赖
        console.log('   🔍 检查未使用的依赖...')
        const unusedDeps = await this.findUnusedDependencies(dependencies)
        unusedDeps.forEach(depName => {
          dependencyIssues.push({
            name: depName,
            type: 'unused',
            currentVersion: dependencies[depName],
            description: `依赖 ${depName} 在代码中未被使用`
          })
        })

        // 检查过时的依赖
        console.log('   📅 检查过时的依赖...')
        const outdatedDeps = await this.findOutdatedDependencies(dependencies)
        outdatedDeps.forEach(({ name, current, latest }) => {
          dependencyIssues.push({
            name,
            type: 'outdated',
            currentVersion: current,
            recommendedVersion: latest,
            description: `依赖 ${name} 有新版本可用: ${current} → ${latest}`
          })
        })

        // 检查安全漏洞
        console.log('   🔒 检查安全漏洞...')
        const vulnerableDeps = await this.findVulnerableDependencies()
        vulnerableDeps.forEach(({ name, severity, description }) => {
          dependencyIssues.push({
            name,
            type: 'vulnerable',
            currentVersion: dependencies[name],
            description: `依赖 ${name} 存在${severity}级安全漏洞: ${description}`
          })
        })

        this.report.metrics.dependencyIssues = dependencyIssues

        if (dependencyIssues.length === 0) {
          this.report.passedChecks.push('依赖关系清洁')
          console.log('   ✅ 依赖关系良好')
        } else {
          const unusedCount = dependencyIssues.filter(d => d.type === 'unused').length
          const outdatedCount = dependencyIssues.filter(d => d.type === 'outdated').length
          const vulnerableCount = dependencyIssues.filter(d => d.type === 'vulnerable').length

          if (unusedCount > 0) {
            this.report.recommendations.push(`移除${unusedCount}个未使用的依赖`)
            console.log(`   🟡 发现${unusedCount}个未使用的依赖`)
            dependencyIssues.filter(d => d.type === 'unused').slice(0, 3).forEach(dep => {
              console.log(`      ${dep.name}@${dep.currentVersion} - ${dep.description}`)
            })
            if (unusedCount > 3) {
              console.log(`      ... 还有${unusedCount - 3}个未使用的依赖`)
            }
          }

          if (outdatedCount > 0) {
            this.report.recommendations.push(`更新${outdatedCount}个过时的依赖`)
            console.log(`   🟠 发现${outdatedCount}个过时的依赖`)
            dependencyIssues.filter(d => d.type === 'outdated').slice(0, 3).forEach(dep => {
              console.log(`      ${dep.name}: ${dep.currentVersion} → ${dep.recommendedVersion}`)
            })
            if (outdatedCount > 3) {
              console.log(`      ... 还有${outdatedCount - 3}个过时的依赖`)
            }
          }

          if (vulnerableCount > 0) {
            this.report.failedChecks.push(`发现${vulnerableCount}个存在安全漏洞的依赖`)
            console.log(`   🔴 发现${vulnerableCount}个存在安全漏洞的依赖`)
            dependencyIssues.filter(d => d.type === 'vulnerable').slice(0, 3).forEach(dep => {
              console.log(`      ${dep.name}@${dep.currentVersion} - ${dep.description}`)
            })
            if (vulnerableCount > 3) {
              console.log(`      ... 还有${vulnerableCount - 3}个安全漏洞`)
            }
          }
        }

        // 汇总显示结果
        console.log('\n   📊 依赖检查结果:')
        console.log(`   总依赖数: ${Object.keys(dependencies).length}`)
        console.log(`   问题依赖: ${dependencyIssues.length}`)

        if (dependencyIssues.length > 0) {
          const unusedCount = dependencyIssues.filter(d => d.type === 'unused').length
          const outdatedCount = dependencyIssues.filter(d => d.type === 'outdated').length
          const vulnerableCount = dependencyIssues.filter(d => d.type === 'vulnerable').length

          if (unusedCount > 0) console.log(`   - 未使用: ${unusedCount}个`)
          if (outdatedCount > 0) console.log(`   - 过时: ${outdatedCount}个`)
          if (vulnerableCount > 0) console.log(`   - 安全漏洞: ${vulnerableCount}个`)
        }
      }
    } catch (error: any) {
      console.log('   ⚠️  无法检查依赖关系')
      console.log(`   错误详情: ${error.message}`)
    }
  }

  /**
   * 检查配置一致性
   */
  private async checkConfiguration(): Promise<void> {
    console.log('⚙️  检查配置一致性...')
    
    const configIssues: string[] = []
    
    // 检查共享配置是否被正确使用
    const backendConfigPath = path.join(this.projectRoot, 'backend/src/config/config.ts')
    const frontendConfigPath = path.join(this.projectRoot, 'frontend/src/config/index.ts')
    
    if (fs.existsSync(backendConfigPath)) {
      const backendConfig = fs.readFileSync(backendConfigPath, 'utf8')
      if (!backendConfig.includes('shared/utils/configManager') && !backendConfig.includes('createConfigManager')) {
        configIssues.push('后端配置未使用共享配置管理器')
      }
    }

    if (fs.existsSync(frontendConfigPath)) {
      const frontendConfig = fs.readFileSync(frontendConfigPath, 'utf8')
      if (!frontendConfig.includes('shared/utils/configManager') && !frontendConfig.includes('createConfigManager')) {
        configIssues.push('前端配置未使用共享配置管理器')
      }
    }
    
    this.report.metrics.configurationIssues = configIssues
    
    if (configIssues.length === 0) {
      this.report.passedChecks.push('配置管理一致性良好')
    } else {
      this.report.failedChecks.push('配置管理存在不一致')
      this.report.recommendations.push('统一使用共享配置管理器')
    }
    
    console.log(`   配置问题: ${configIssues.length}`)
  }

  /**
   * 验证重构效果
   */
  private async validateRefactoring(): Promise<void> {
    console.log('🔄 验证重构效果...')
    
    const sharedUtilsPath = path.join(this.projectRoot, 'shared/utils')
    const sharedTypesPath = path.join(this.projectRoot, 'shared/types')
    
    let refactoringScore = 0
    
    // 检查共享工具是否存在
    if (fs.existsSync(sharedUtilsPath)) {
      refactoringScore += 20
      this.report.passedChecks.push('共享工具库已建立')
    } else {
      this.report.failedChecks.push('缺少共享工具库')
    }
    
    // 检查共享类型是否存在
    if (fs.existsSync(sharedTypesPath)) {
      refactoringScore += 20
      this.report.passedChecks.push('共享类型定义已建立')
    } else {
      this.report.failedChecks.push('缺少共享类型定义')
    }
    
    // 检查重复代码减少情况
    if (this.report.metrics.duplicateCodePercentage < 10) {
      refactoringScore += 30
    }
    
    // 检查模块化程度
    if (this.report.metrics.moduleComplexity < 200) {
      refactoringScore += 30
    }
    
    console.log(`   重构效果评分: ${refactoringScore}/100`)
    
    if (refactoringScore >= 80) {
      this.report.passedChecks.push('重构效果优秀')
    } else if (refactoringScore >= 60) {
      this.report.recommendations.push('继续优化重构效果')
    } else {
      this.report.failedChecks.push('重构效果需要改进')
    }
  }

  /**
   * 计算总体评分
   */
  private calculateOverallScore(): void {
    let score = 100
    
    // 扣分项
    score -= this.report.metrics.duplicateCodePercentage * 2 // 重复代码
    score -= this.report.metrics.typeScriptErrors * 5 // TS错误
    score -= this.report.metrics.eslintErrors * 2 // ESLint错误
    score -= this.report.metrics.eslintWarnings * 0.5 // ESLint警告
    score -= Math.max(0, 80 - this.report.metrics.testCoverage) // 测试覆盖率
    score -= this.report.metrics.dependencyIssues.length * 2 // 依赖问题
    score -= this.report.metrics.configurationIssues.length * 5 // 配置问题
    
    this.report.overallScore = Math.max(0, Math.min(100, score))
  }

  /**
   * 生成改进建议
   */
  private generateRecommendations(): void {
    if (this.report.overallScore >= 90) {
      this.report.recommendations.unshift('代码质量优秀，继续保持！')
    } else if (this.report.overallScore >= 70) {
      this.report.recommendations.unshift('代码质量良好，可以进一步优化')
    } else if (this.report.overallScore >= 50) {
      this.report.recommendations.unshift('代码质量需要改进，建议优先处理关键问题')
    } else {
      this.report.recommendations.unshift('代码质量较差，需要大幅改进')
    }
  }

  /**
   * 获取源代码文件列表
   */
  private getSourceFiles(): string[] {
    const files: string[] = []
    const extensions = ['.ts', '.js', '.vue']
    
    const scanDirectory = (dir: string) => {
      if (!fs.existsSync(dir)) return
      
      const items = fs.readdirSync(dir)
      items.forEach(item => {
        const fullPath = path.join(dir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath)
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath)
        }
      })
    }
    
    scanDirectory(this.projectRoot)
    return files
  }

  /**
   * 查找未使用的依赖
   */
  private async findUnusedDependencies(dependencies: Record<string, string>): Promise<string[]> {
    // 简化实现：检查依赖是否在代码中被引用
    const sourceFiles = this.getSourceFiles()
    const usedDeps = new Set<string>()

    sourceFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8')
      Object.keys(dependencies).forEach(dep => {
        if (content.includes(`'${dep}'`) || content.includes(`"${dep}"`)) {
          usedDeps.add(dep)
        }
      })
    })

    return Object.keys(dependencies).filter(dep => !usedDeps.has(dep))
  }

  /**
   * 查找过时的依赖
   */
  private async findOutdatedDependencies(dependencies: Record<string, string>): Promise<Array<{name: string, current: string, latest: string}>> {
    const outdated: Array<{name: string, current: string, latest: string}> = []

    try {
      // 尝试使用npm outdated命令
      const result = execSync('npm outdated --json', {
        cwd: this.projectRoot,
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe'] // 抑制stderr输出
      })

      const outdatedData = JSON.parse(result)
      Object.keys(outdatedData).forEach(name => {
        if (dependencies[name]) {
          outdated.push({
            name,
            current: outdatedData[name].current,
            latest: outdatedData[name].latest
          })
        }
      })

      if (outdated.length === 0) {
        console.log('   ✅ 所有依赖都是最新版本')
      }

    } catch (error: any) {
      // npm outdated命令失败时的处理
      if (error.status === 1 && error.stdout) {
        // npm outdated在有过时依赖时返回状态码1，但仍有有效输出
        try {
          const outdatedData = JSON.parse(error.stdout)
          Object.keys(outdatedData).forEach(name => {
            if (dependencies[name]) {
              outdated.push({
                name,
                current: outdatedData[name].current,
                latest: outdatedData[name].latest
              })
            }
          })
        } catch {
          console.log('   ℹ️  无法检查依赖版本更新')
        }
      } else {
        console.log('   ℹ️  无法检查依赖版本更新')
      }
    }

    return outdated
  }

  /**
   * 查找存在安全漏洞的依赖
   */
  private async findVulnerableDependencies(): Promise<Array<{name: string, severity: string, description: string}>> {
    const vulnerable: Array<{name: string, severity: string, description: string}> = []

    try {
      // 首先检查当前npm配置的registry
      const registryResult = execSync('npm config get registry', {
        cwd: this.projectRoot,
        encoding: 'utf8'
      }).trim()

      // 如果使用的是国内镜像源，跳过audit检查
      const chineseMirrors = [
        'https://registry.npmmirror.com',
        'https://registry.npm.taobao.org',
        'https://r.cnpmjs.org'
      ]

      const isChineseMirror = chineseMirrors.some(mirror =>
        registryResult.includes(mirror.replace('https://', '').replace('http://', ''))
      )

      if (isChineseMirror) {
        console.log('   ℹ️  检测到使用国内镜像源，跳过安全漏洞检查')
        return vulnerable
      }

      // 尝试使用npm audit命令
      const result = execSync('npm audit --json', {
        cwd: this.projectRoot,
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe'] // 抑制stderr输出
      })

      const auditData = JSON.parse(result)
      if (auditData.vulnerabilities) {
        Object.keys(auditData.vulnerabilities).forEach(name => {
          const vuln = auditData.vulnerabilities[name]
          vulnerable.push({
            name,
            severity: vuln.severity || 'unknown',
            description: vuln.title || '未知安全漏洞'
          })
        })
      }

      if (vulnerable.length === 0) {
        console.log('   ✅ 未发现安全漏洞')
      }

    } catch (error: any) {
      // 检查是否是audit endpoint错误
      if (error.message && error.message.includes('audit endpoint')) {
        console.log('   ℹ️  当前npm镜像源不支持安全审计功能，跳过安全漏洞检查')
      } else if (error.message && error.message.includes('404')) {
        console.log('   ℹ️  npm audit服务不可用，跳过安全漏洞检查')
      } else {
        console.log('   ⚠️  无法执行安全漏洞检查')
        console.log(`   详情: ${error.message?.split('\n')[0] || '未知错误'}`)
      }
    }

    return vulnerable
  }

  /**
   * 生成质量报告
   */
  generateReport(): string {
    const eslintErrorsSection = this.generateESLintErrorsSection()
    const eslintWarningsSection = this.generateESLintWarningsSection()
    const dependencyIssuesSection = this.generateDependencyIssuesSection()

    const report = `
# 代码质量检查报告

**生成时间**: ${this.report.timestamp}
**总体评分**: ${this.report.overallScore}/100

## 质量指标

- **代码重复度**: ${this.report.metrics.duplicateCodePercentage}%
- **TypeScript错误**: ${this.report.metrics.typeScriptErrors}
- **ESLint错误**: ${this.report.metrics.eslintErrors}
- **ESLint警告**: ${this.report.metrics.eslintWarnings}
- **测试覆盖率**: ${this.report.metrics.testCoverage}%
- **平均模块复杂度**: ${Math.round(this.report.metrics.moduleComplexity)}行
- **依赖问题**: ${this.report.metrics.dependencyIssues.length}
- **配置问题**: ${this.report.metrics.configurationIssues.length}

## 通过的检查 ✅

${this.report.passedChecks.map(check => `- ${check}`).join('\n')}

## 失败的检查 ❌

${this.report.failedChecks.map(check => `- ${check}`).join('\n')}

${eslintErrorsSection}

${eslintWarningsSection}

${dependencyIssuesSection}

## 改进建议 💡

${this.report.recommendations.map(rec => `- ${rec}`).join('\n')}

---
*此报告由代码质量检查工具自动生成*
`
    return report
  }

  /**
   * 生成ESLint错误详情部分
   */
  private generateESLintErrorsSection(): string {
    const errors = this.report.metrics.eslintIssues.filter(issue => issue.severity === 'error')

    if (errors.length === 0) {
      return ''
    }

    let section = `## ESLint错误详情 🔴\n\n`
    section += `发现 ${errors.length} 个ESLint错误：\n\n`

    // 按文件分组
    const errorsByFile = errors.reduce((acc, error) => {
      if (!acc[error.file]) {
        acc[error.file] = []
      }
      acc[error.file]!.push(error)
      return acc
    }, {} as Record<string, ESLintIssue[]>)

    Object.keys(errorsByFile).forEach(file => {
      section += `### ${file}\n\n`
      errorsByFile[file]!.forEach(error => {
        section += `- **第${error.line}行:${error.column}列** - ${error.message} \`(${error.ruleId})\`\n`
      })
      section += '\n'
    })

    return section
  }

  /**
   * 生成ESLint警告详情部分
   */
  private generateESLintWarningsSection(): string {
    const warnings = this.report.metrics.eslintIssues.filter(issue => issue.severity === 'warning')

    if (warnings.length === 0) {
      return ''
    }

    let section = `## ESLint警告详情 🟡\n\n`
    section += `发现 ${warnings.length} 个ESLint警告：\n\n`

    // 按规则分组显示前10个最常见的警告
    const warningsByRule = warnings.reduce((acc, warning) => {
      if (!acc[warning.ruleId]) {
        acc[warning.ruleId] = []
      }
      acc[warning.ruleId]!.push(warning)
      return acc
    }, {} as Record<string, ESLintIssue[]>)

    const sortedRules = Object.keys(warningsByRule).sort((a, b) =>
      warningsByRule[b]!.length - warningsByRule[a]!.length
    ).slice(0, 10)

    sortedRules.forEach(ruleId => {
      const ruleWarnings = warningsByRule[ruleId]!
      section += `### ${ruleId} (${ruleWarnings.length}个)\n\n`

      // 显示前3个示例
      ruleWarnings.slice(0, 3).forEach(warning => {
        section += `- **${warning.file}:${warning.line}:${warning.column}** - ${warning.message}\n`
      })

      if (ruleWarnings.length > 3) {
        section += `- ... 还有${ruleWarnings.length - 3}个相同类型的警告\n`
      }
      section += '\n'
    })

    return section
  }

  /**
   * 生成依赖问题详情部分
   */
  private generateDependencyIssuesSection(): string {
    const issues = this.report.metrics.dependencyIssues

    if (issues.length === 0) {
      return ''
    }

    let section = `## 依赖问题详情 📦\n\n`
    section += `发现 ${issues.length} 个依赖问题：\n\n`

    // 按类型分组
    const unusedDeps = issues.filter(issue => issue.type === 'unused')
    const outdatedDeps = issues.filter(issue => issue.type === 'outdated')
    const vulnerableDeps = issues.filter(issue => issue.type === 'vulnerable')

    if (unusedDeps.length > 0) {
      section += `### 未使用的依赖 (${unusedDeps.length}个)\n\n`
      unusedDeps.forEach(dep => {
        section += `- **${dep.name}@${dep.currentVersion}** - ${dep.description}\n`
      })
      section += '\n'
    }

    if (outdatedDeps.length > 0) {
      section += `### 过时的依赖 (${outdatedDeps.length}个)\n\n`
      outdatedDeps.forEach(dep => {
        section += `- **${dep.name}** - 当前版本: ${dep.currentVersion}, 最新版本: ${dep.recommendedVersion}\n`
      })
      section += '\n'
    }

    if (vulnerableDeps.length > 0) {
      section += `### 存在安全漏洞的依赖 (${vulnerableDeps.length}个)\n\n`
      vulnerableDeps.forEach(dep => {
        section += `- **${dep.name}@${dep.currentVersion}** - ${dep.description}\n`
      })
      section += '\n'
    }

    return section
  }
}

// 主函数
async function main() {
  const projectRoot = process.cwd()
  const checker = new CodeQualityChecker(projectRoot)
  
  try {
    const report = await checker.runQualityCheck()
    
    // 生成报告
    const reportContent = checker.generateReport()
    const reportPath = path.join(projectRoot, 'quality-report.md')
    fs.writeFileSync(reportPath, reportContent)
    
    console.log('\n📊 质量检查报告:')
    console.log(`总体评分: ${report.overallScore}/100`)
    console.log(`通过检查: ${report.passedChecks.length}`)
    console.log(`失败检查: ${report.failedChecks.length}`)
    console.log(`改进建议: ${report.recommendations.length}`)
    console.log(`\n📄 详细报告已保存至: ${reportPath}`)
    
    // 根据评分决定退出码
    process.exit(report.overallScore >= 70 ? 0 : 1)
  } catch (error) {
    console.error('质量检查失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

export { CodeQualityChecker, QualityReport, QualityMetrics }

{"extends": "./tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./", "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "allowUnusedLabels": true, "allowUnreachableCode": true}, "include": ["code-quality-check/**/*"], "exclude": ["node_modules", "dist", "backend", "frontend", "shared"], "ts-node": {"transpileOnly": true, "compilerOptions": {"module": "commonjs"}}}
2025-07-08 09:22:12.975 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.976 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.726 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.727 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:59:27.279 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:00:05.584 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:06:23.230 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:06:54.133 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 10:07:51.225 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:81:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:11:49.750 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:11:49.752 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:11:49.843 [ERROR]: Error during graceful shutdown: SQLITE_MISUSE: Database handle is closed {"errno":21,"code":"SQLITE_MISUSE"}
Error: SQLITE_MISUSE: Database handle is closed
2025-07-08 10:13:19.660 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:13:19.661 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:15:41.923 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:15:41.925 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)

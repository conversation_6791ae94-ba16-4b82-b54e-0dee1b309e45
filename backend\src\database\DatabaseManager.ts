/**
 * 数据库管理器
 * 
 * 功能说明：
 * 1. SQLite数据库连接管理
 * 2. 数据库初始化和表创建
 * 3. 数据库迁移管理
 * 4. 连接池管理
 * 5. 事务处理
 */

import sqlite3 from 'sqlite3'
import { open, Database } from 'sqlite'
import path from 'path'
import fs from 'fs/promises'
import { logger } from '@/utils/logger'
import { config } from '@/config/config'

export class DatabaseManager {
  private static instance: DatabaseManager | null = null
  private db: Database<sqlite3.Database, sqlite3.Statement> | null = null
  private dbPath: string
  private isInitialized: boolean = false

  private constructor() {
    // 数据库文件路径
    this.dbPath = path.join(process.cwd(), 'data', config.database.filename)
  }

  /**
   * 获取DatabaseManager单例实例
   */
  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  /**
   * 初始化数据库
   */
  public async initialize(): Promise<void> {
    try {
      // 确保数据目录存在
      await this.ensureDataDirectory()

      // 打开数据库连接
      await this.openConnection()

      // 启用外键约束
      await this.enableForeignKeys()

      // 创建数据表
      await this.createTables()

      // 运行数据迁移
      await this.runMigrations()

      this.isInitialized = true
      logger.info('Database initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize database:', error)
      throw error
    }
  }

  /**
   * 确保数据目录存在
   */
  private async ensureDataDirectory(): Promise<void> {
    const dataDir = path.dirname(this.dbPath)
    try {
      await fs.access(dataDir)
    } catch {
      await fs.mkdir(dataDir, { recursive: true })
      logger.info(`Created data directory: ${dataDir}`)
    }
  }

  /**
   * 打开数据库连接
   */
  private async openConnection(): Promise<void> {
    this.db = await open({
      filename: this.dbPath,
      driver: sqlite3.Database
    })

    // 设置数据库配置
    await this.db.exec(`
      PRAGMA journal_mode = WAL;
      PRAGMA synchronous = NORMAL;
      PRAGMA cache_size = 1000;
      PRAGMA temp_store = MEMORY;
      PRAGMA mmap_size = 268435456;
    `)

    logger.info(`Database connection opened: ${this.dbPath}`)
  }

  /**
   * 启用外键约束
   */
  private async enableForeignKeys(): Promise<void> {
    if (!this.db) throw new Error('Database not connected')
    await this.db.exec('PRAGMA foreign_keys = ON')
  }

  /**
   * 创建数据表
   */
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not connected')

    // 用户表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) NOT NULL UNIQUE,
        display_name VARCHAR(100),
        avatar_url VARCHAR(255),
        status VARCHAR(20) DEFAULT 'offline',
        last_active_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 用户表索引
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_users_last_active ON users(last_active_time)`)

    // 会话表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id VARCHAR(100) NOT NULL UNIQUE,
        user1_id INTEGER NOT NULL,
        user2_id INTEGER NOT NULL,
        status VARCHAR(20) DEFAULT 'active',
        start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        end_time DATETIME,
        duration INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (user1_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (user2_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `)

    // 会话表索引
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_sessions_session_id ON sessions(session_id)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_sessions_users ON sessions(user1_id, user2_id)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time)`)

    // 消息表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id VARCHAR(100) NOT NULL UNIQUE,
        type VARCHAR(20) NOT NULL,
        from_user_id INTEGER,
        to_user_id INTEGER,
        session_id INTEGER,
        content TEXT,
        metadata TEXT,
        priority VARCHAR(20) DEFAULT 'normal',
        status VARCHAR(20) DEFAULT 'sent',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (to_user_id) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
      )
    `)

    // 消息表索引
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_messages_message_id ON messages(message_id)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_messages_users ON messages(from_user_id, to_user_id)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_messages_session ON messages(session_id)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at)`)

    // 系统配置表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS system_configs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_key VARCHAR(100) NOT NULL UNIQUE,
        config_value TEXT,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 系统配置表索引
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_configs_key ON system_configs(config_key)`)

    // 音频文件表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS audio_files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_id VARCHAR(100) NOT NULL UNIQUE,
        message_id INTEGER,
        file_path VARCHAR(255) NOT NULL,
        file_size INTEGER DEFAULT 0,
        duration INTEGER DEFAULT 0,
        format VARCHAR(10) DEFAULT 'aac',
        sample_rate INTEGER DEFAULT 16000,
        bit_rate INTEGER DEFAULT 64000,
        channels INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
      )
    `)

    // 音频文件表索引
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_audio_files_file_id ON audio_files(file_id)`)
    await this.db.exec(`CREATE INDEX IF NOT EXISTS idx_audio_files_message ON audio_files(message_id)`)

    logger.info('Database tables created successfully')
  }

  /**
   * 运行数据迁移
   */
  private async runMigrations(): Promise<void> {
    if (!this.db) throw new Error('Database not connected')

    // 创建迁移记录表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 检查并运行迁移
    const migrations = [
      {
        version: '1.0.0',
        description: 'Initial database schema',
        sql: '' // 已在createTables中执行
      }
    ]

    for (const migration of migrations) {
      const existing = await this.db.get(
        'SELECT version FROM migrations WHERE version = ?',
        [migration.version]
      )

      if (!existing) {
        if (migration.sql) {
          await this.db.exec(migration.sql)
        }
        
        await this.db.run(
          'INSERT INTO migrations (version, description) VALUES (?, ?)',
          [migration.version, migration.description]
        )
        
        logger.info(`Migration ${migration.version} executed: ${migration.description}`)
      }
    }
  }

  /**
   * 获取数据库连接
   */
  public getConnection(): Database<sqlite3.Database, sqlite3.Statement> {
    if (!this.db || !this.isInitialized) {
      throw new Error('Database not initialized')
    }
    return this.db
  }

  /**
   * 执行事务
   */
  public async transaction<T>(callback: (db: Database<sqlite3.Database, sqlite3.Statement>) => Promise<T>): Promise<T> {
    if (!this.db) throw new Error('Database not connected')

    await this.db.exec('BEGIN TRANSACTION')
    try {
      const result = await callback(this.db)
      await this.db.exec('COMMIT')
      return result
    } catch (error) {
      await this.db.exec('ROLLBACK')
      throw error
    }
  }

  /**
   * 关闭数据库连接
   */
  public async close(): Promise<void> {
    if (this.db) {
      await this.db.close()
      this.db = null
      this.isInitialized = false
      logger.info('Database connection closed')
    }
  }

  /**
   * 检查数据库健康状态
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.db) return false
      await this.db.get('SELECT 1')
      return true
    } catch {
      return false
    }
  }

  // ==================== 用户管理方法 ====================

  /**
   * 根据用户名获取用户
   */
  public async getUserByUsername(username: string): Promise<any> {
    if (!this.db) throw new Error('Database not connected')
    return await this.db.get(
      'SELECT * FROM users WHERE username = ?',
      [username]
    )
  }

  /**
   * 根据ID获取用户
   */
  public async getUserById(userId: number): Promise<any> {
    if (!this.db) throw new Error('Database not connected')
    return await this.db.get(
      'SELECT * FROM users WHERE id = ?',
      [userId]
    )
  }

  /**
   * 创建用户
   */
  public async createUser(userData: {
    username: string
    displayName?: string
    status?: string
    createdAt: Date
    lastLoginAt: Date
  }): Promise<number> {
    if (!this.db) throw new Error('Database not connected')

    const result = await this.db.run(
      `INSERT INTO users (username, display_name, status, created_at, last_active_time, updated_at)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        userData.username,
        userData.displayName || userData.username,
        userData.status || 'online',
        userData.createdAt.toISOString(),
        userData.lastLoginAt.toISOString(),
        new Date().toISOString()
      ]
    )

    return result.lastID!
  }

  /**
   * 更新用户信息
   */
  public async updateUser(userId: number, updateData: any): Promise<void> {
    if (!this.db) throw new Error('Database not connected')

    const fields = []
    const values = []

    if (updateData.displayName !== undefined) {
      fields.push('display_name = ?')
      values.push(updateData.displayName)
    }

    if (updateData.status !== undefined) {
      fields.push('status = ?')
      values.push(updateData.status)
    }

    if (updateData.lastLoginAt !== undefined) {
      fields.push('last_active_time = ?')
      values.push(updateData.lastLoginAt.toISOString())
    }

    if (updateData.lastLogoutAt !== undefined) {
      fields.push('updated_at = ?')
      values.push(updateData.lastLogoutAt.toISOString())
    }

    fields.push('updated_at = ?')
    values.push(new Date().toISOString())
    values.push(userId)

    if (fields.length > 1) { // 除了updated_at还有其他字段
      await this.db.run(
        `UPDATE users SET ${fields.join(', ')} WHERE id = ?`,
        values
      )
    }
  }

  /**
   * 获取在线用户列表
   */
  public async getOnlineUsers(): Promise<any[]> {
    if (!this.db) throw new Error('Database not connected')
    return await this.db.all(
      "SELECT * FROM users WHERE status IN ('online', 'talking', 'busy') ORDER BY last_active_time DESC"
    )
  }

  /**
   * 搜索用户
   */
  public async searchUsers(options: {
    keyword: string
    status: string
    page: number
    limit: number
    excludeUserId?: number
  }): Promise<{ users: any[]; total: number; page: number; limit: number }> {
    if (!this.db) throw new Error('Database not connected')

    let whereClause = '1=1'
    const params: any[] = []

    if (options.keyword) {
      whereClause += ' AND (username LIKE ? OR display_name LIKE ?)'
      params.push(`%${options.keyword}%`, `%${options.keyword}%`)
    }

    if (options.status && options.status !== 'all') {
      whereClause += ' AND status = ?'
      params.push(options.status)
    }

    if (options.excludeUserId) {
      whereClause += ' AND id != ?'
      params.push(options.excludeUserId)
    }

    // 获取总数
    const countResult = await this.db.get(
      `SELECT COUNT(*) as total FROM users WHERE ${whereClause}`,
      params
    )

    // 获取分页数据
    const offset = (options.page - 1) * options.limit
    const users = await this.db.all(
      `SELECT * FROM users WHERE ${whereClause} ORDER BY last_active_time DESC LIMIT ? OFFSET ?`,
      [...params, options.limit, offset]
    )

    return {
      users,
      total: countResult.total,
      page: options.page,
      limit: options.limit
    }
  }

  // ==================== 消息管理方法 ====================

  /**
   * 创建消息
   */
  public async createMessage(messageData: {
    senderId: number
    receiverId?: number | null
    content: string
    type: string
    priority: string
    status: string
    createdAt: Date
  }): Promise<number> {
    if (!this.db) throw new Error('Database not connected')

    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const result = await this.db.run(
      `INSERT INTO messages (message_id, type, from_user_id, to_user_id, content, priority, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        messageId,
        messageData.type,
        messageData.senderId,
        messageData.receiverId,
        messageData.content,
        messageData.priority,
        messageData.status,
        messageData.createdAt.toISOString(),
        new Date().toISOString()
      ]
    )

    return result.lastID!
  }

  /**
   * 根据ID获取消息
   */
  public async getMessageById(messageId: number): Promise<any> {
    if (!this.db) throw new Error('Database not connected')

    const message = await this.db.get(
      `SELECT m.*,
              s.username as sender_username, s.display_name as sender_display_name,
              r.username as receiver_username, r.display_name as receiver_display_name
       FROM messages m
       LEFT JOIN users s ON m.from_user_id = s.id
       LEFT JOIN users r ON m.to_user_id = r.id
       WHERE m.id = ?`,
      [messageId]
    )

    if (message) {
      return {
        id: message.id,
        messageId: message.message_id,
        content: message.content,
        type: message.type,
        priority: message.priority,
        status: message.status,
        createdAt: message.created_at,
        updatedAt: message.updated_at,
        senderId: message.from_user_id,
        receiverId: message.to_user_id,
        sender: message.sender_username ? {
          id: message.from_user_id,
          username: message.sender_username,
          displayName: message.sender_display_name
        } : null,
        receiver: message.receiver_username ? {
          id: message.to_user_id,
          username: message.receiver_username,
          displayName: message.receiver_display_name
        } : null
      }
    }

    return null
  }

  /**
   * 更新消息
   */
  public async updateMessage(messageId: number, updateData: any): Promise<void> {
    if (!this.db) throw new Error('Database not connected')

    const fields = []
    const values = []

    if (updateData.status !== undefined) {
      fields.push('status = ?')
      values.push(updateData.status)
    }

    if (updateData.readAt !== undefined) {
      fields.push('updated_at = ?')
      values.push(updateData.readAt.toISOString())
    }

    fields.push('updated_at = ?')
    values.push(new Date().toISOString())
    values.push(messageId)

    if (fields.length > 1) {
      await this.db.run(
        `UPDATE messages SET ${fields.join(', ')} WHERE id = ?`,
        values
      )
    }
  }

  /**
   * 获取消息历史
   */
  public async getMessageHistory(options: {
    userId: number
    page: number
    limit: number
    type?: string
    startDate?: Date
    endDate?: Date
    keyword?: string
  }): Promise<{ messages: any[]; total: number; page: number; limit: number }> {
    if (!this.db) throw new Error('Database not connected')

    let whereClause = '(m.from_user_id = ? OR m.to_user_id = ? OR m.to_user_id IS NULL)'
    const params: any[] = [options.userId, options.userId]

    if (options.type && options.type !== 'all') {
      whereClause += ' AND m.type = ?'
      params.push(options.type)
    }

    if (options.startDate) {
      whereClause += ' AND m.created_at >= ?'
      params.push(options.startDate.toISOString())
    }

    if (options.endDate) {
      whereClause += ' AND m.created_at <= ?'
      params.push(options.endDate.toISOString())
    }

    if (options.keyword) {
      whereClause += ' AND m.content LIKE ?'
      params.push(`%${options.keyword}%`)
    }

    // 获取总数
    const countResult = await this.db.get(
      `SELECT COUNT(*) as total FROM messages m WHERE ${whereClause}`,
      params
    )

    // 获取分页数据
    const offset = (options.page - 1) * options.limit
    const messages = await this.db.all(
      `SELECT m.*,
              s.username as sender_username, s.display_name as sender_display_name,
              r.username as receiver_username, r.display_name as receiver_display_name
       FROM messages m
       LEFT JOIN users s ON m.from_user_id = s.id
       LEFT JOIN users r ON m.to_user_id = r.id
       WHERE ${whereClause}
       ORDER BY m.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, options.limit, offset]
    )

    const formattedMessages = messages.map(msg => ({
      id: msg.id,
      messageId: msg.message_id,
      content: msg.content,
      type: msg.type,
      priority: msg.priority,
      status: msg.status,
      createdAt: msg.created_at,
      senderId: msg.from_user_id,
      receiverId: msg.to_user_id,
      sender: msg.sender_username ? {
        id: msg.from_user_id,
        username: msg.sender_username,
        displayName: msg.sender_display_name
      } : null,
      receiver: msg.receiver_username ? {
        id: msg.to_user_id,
        username: msg.receiver_username,
        displayName: msg.receiver_display_name
      } : null
    }))

    return {
      messages: formattedMessages,
      total: countResult.total,
      page: options.page,
      limit: options.limit
    }
  }

  /**
   * 获取对话历史
   */
  public async getConversationHistory(options: {
    userId: number
    targetUserId: number
    page: number
    limit: number
  }): Promise<{ messages: any[]; total: number; page: number; limit: number }> {
    if (!this.db) throw new Error('Database not connected')

    const whereClause = '((m.from_user_id = ? AND m.to_user_id = ?) OR (m.from_user_id = ? AND m.to_user_id = ?))'
    const params = [options.userId, options.targetUserId, options.targetUserId, options.userId]

    // 获取总数
    const countResult = await this.db.get(
      `SELECT COUNT(*) as total FROM messages m WHERE ${whereClause}`,
      params
    )

    // 获取分页数据
    const offset = (options.page - 1) * options.limit
    const messages = await this.db.all(
      `SELECT m.*,
              s.username as sender_username, s.display_name as sender_display_name
       FROM messages m
       LEFT JOIN users s ON m.from_user_id = s.id
       WHERE ${whereClause}
       ORDER BY m.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, options.limit, offset]
    )

    const formattedMessages = messages.map(msg => ({
      id: msg.id,
      messageId: msg.message_id,
      content: msg.content,
      type: msg.type,
      priority: msg.priority,
      status: msg.status,
      createdAt: msg.created_at,
      senderId: msg.from_user_id,
      receiverId: msg.to_user_id,
      sender: msg.sender_username ? {
        id: msg.from_user_id,
        username: msg.sender_username,
        displayName: msg.sender_display_name
      } : null
    }))

    return {
      messages: formattedMessages,
      total: countResult.total,
      page: options.page,
      limit: options.limit
    }
  }

  /**
   * 获取未读消息数量
   */
  public async getUnreadMessageCount(userId: number): Promise<number> {
    if (!this.db) throw new Error('Database not connected')

    const result = await this.db.get(
      "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND status != 'read'",
      [userId]
    )

    return result.count || 0
  }

  // ==================== 系统管理方法 ====================

  /**
   * 获取系统配置
   */
  public async getSystemConfigs(): Promise<any[]> {
    if (!this.db) throw new Error('Database not connected')

    const configs = await this.db.all(
      'SELECT config_key as key, config_value as value, description, updated_at FROM system_configs ORDER BY config_key'
    )

    return configs.map(config => ({
      key: config.key,
      value: config.value,
      description: config.description,
      updatedAt: config.updated_at
    }))
  }

  /**
   * 设置系统配置
   */
  public async setSystemConfig(key: string, value: string, description?: string, _updatedBy?: number): Promise<void> {
    if (!this.db) throw new Error('Database not connected')

    await this.db.run(
      `INSERT OR REPLACE INTO system_configs (config_key, config_value, description, updated_at)
       VALUES (?, ?, ?, ?)`,
      [key, value, description || '', new Date().toISOString()]
    )
  }

  /**
   * 获取用户统计信息
   */
  public async getUserStats(dateRange: { startDate: Date; endDate: Date }): Promise<any> {
    if (!this.db) throw new Error('Database not connected')

    const totalUsers = await this.db.get('SELECT COUNT(*) as count FROM users')
    const onlineUsers = await this.db.get("SELECT COUNT(*) as count FROM users WHERE status IN ('online', 'talking', 'busy')")
    const newUsers = await this.db.get(
      'SELECT COUNT(*) as count FROM users WHERE created_at >= ? AND created_at <= ?',
      [dateRange.startDate.toISOString(), dateRange.endDate.toISOString()]
    )

    return {
      total: totalUsers.count,
      online: onlineUsers.count,
      newInPeriod: newUsers.count
    }
  }

  /**
   * 获取消息统计信息
   */
  public async getMessageStats(dateRange: { startDate: Date; endDate: Date }): Promise<any> {
    if (!this.db) throw new Error('Database not connected')

    const totalMessages = await this.db.get(
      'SELECT COUNT(*) as count FROM messages WHERE created_at >= ? AND created_at <= ?',
      [dateRange.startDate.toISOString(), dateRange.endDate.toISOString()]
    )

    const messagesByType = await this.db.all(
      'SELECT type, COUNT(*) as count FROM messages WHERE created_at >= ? AND created_at <= ? GROUP BY type',
      [dateRange.startDate.toISOString(), dateRange.endDate.toISOString()]
    )

    return {
      total: totalMessages.count,
      byType: messagesByType.reduce((acc, item) => {
        acc[item.type] = item.count
        return acc
      }, {})
    }
  }

  /**
   * 获取通话统计信息
   */
  public async getCallStats(dateRange: { startDate: Date; endDate: Date }): Promise<any> {
    if (!this.db) throw new Error('Database not connected')

    const totalCalls = await this.db.get(
      'SELECT COUNT(*) as count FROM sessions WHERE start_time >= ? AND start_time <= ?',
      [dateRange.startDate.toISOString(), dateRange.endDate.toISOString()]
    )

    const avgDuration = await this.db.get(
      'SELECT AVG(duration) as avg FROM sessions WHERE start_time >= ? AND start_time <= ? AND duration > 0',
      [dateRange.startDate.toISOString(), dateRange.endDate.toISOString()]
    )

    return {
      total: totalCalls.count,
      averageDuration: avgDuration.avg || 0
    }
  }

  /**
   * 获取系统日志（简化实现）
   */
  public async getSystemLogs(options: {
    level: string
    limit: number
    page: number
  }): Promise<{ logs: any[]; total: number; page: number; limit: number }> {
    // 简化实现，实际应该从日志文件或专门的日志表中读取
    const logs = [
      {
        id: 1,
        level: 'info',
        message: 'System started successfully',
        timestamp: new Date().toISOString(),
        source: 'system'
      }
    ]

    return {
      logs,
      total: logs.length,
      page: options.page,
      limit: options.limit
    }
  }

  /**
   * 清理旧日志
   */
  public async cleanupOldLogs(_cutoffDate: Date): Promise<{ deletedCount: number }> {
    // 简化实现
    return { deletedCount: 0 }
  }

  /**
   * 清理旧消息
   */
  public async cleanupOldMessages(cutoffDate: Date): Promise<{ deletedCount: number }> {
    if (!this.db) throw new Error('Database not connected')

    const result = await this.db.run(
      'DELETE FROM messages WHERE created_at < ? AND type != ?',
      [cutoffDate.toISOString(), 'backend-call']
    )

    return { deletedCount: result.changes || 0 }
  }

  /**
   * 清理旧会话
   */
  public async cleanupOldSessions(cutoffDate: Date): Promise<{ deletedCount: number }> {
    if (!this.db) throw new Error('Database not connected')

    const result = await this.db.run(
      "DELETE FROM sessions WHERE end_time < ? AND status = 'ended'",
      [cutoffDate.toISOString()]
    )

    return { deletedCount: result.changes || 0 }
  }

  // ==================== 会话管理方法 ====================

  /**
   * 创建会话
   */
  public async createSession(sessionData: {
    sessionId: string
    user1Id: number
    user2Id: number
    startTime: Date
  }): Promise<number> {
    if (!this.db) throw new Error('Database not connected')

    const result = await this.db.run(
      `INSERT INTO sessions (session_id, user1_id, user2_id, status, start_time, created_at, updated_at)
       VALUES (?, ?, ?, 'active', ?, ?, ?)`,
      [
        sessionData.sessionId,
        sessionData.user1Id,
        sessionData.user2Id,
        sessionData.startTime.toISOString(),
        new Date().toISOString(),
        new Date().toISOString()
      ]
    )

    return result.lastID!
  }

  /**
   * 结束会话
   */
  public async endSession(sessionId: string, endTime: Date, duration: number): Promise<void> {
    if (!this.db) throw new Error('Database not connected')

    await this.db.run(
      `UPDATE sessions SET status = 'ended', end_time = ?, duration = ?, updated_at = ?
       WHERE session_id = ?`,
      [endTime.toISOString(), duration, new Date().toISOString(), sessionId]
    )
  }

  /**
   * 获取活跃会话
   */
  public async getActiveSession(sessionId: string): Promise<any> {
    if (!this.db) throw new Error('Database not connected')

    return await this.db.get(
      "SELECT * FROM sessions WHERE session_id = ? AND status = 'active'",
      [sessionId]
    )
  }
}

/**
 * 环境检测工具
 * 
 * 功能说明：
 * 1. 检测当前运行环境
 * 2. 确保开发工具只在合适的环境中运行
 * 3. 提供环境相关的配置
 */

/**
 * 检测是否为开发环境
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检测是否为生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}

/**
 * 检测是否在浏览器环境中
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined' && typeof document !== 'undefined'
}

/**
 * 检测是否在服务器端渲染环境中
 */
export function isSSR(): boolean {
  return typeof window === 'undefined'
}

/**
 * 检测是否为uni-app H5环境
 */
export function isUniH5(): boolean {
  // #ifdef H5
  return true
  // #endif
  // #ifndef H5
  return false
  // #endif
}

/**
 * 检测是否为uni-app小程序环境
 */
export function isMiniProgram(): boolean {
  // #ifdef MP
  return true
  // #endif
  // #ifndef MP
  return false
  // #endif
}

/**
 * 检测是否为uni-app App环境
 */
export function isApp(): boolean {
  // #ifdef APP
  return true
  // #endif
  // #ifndef APP
  return false
  // #endif
}

/**
 * 检测是否应该启用开发工具
 */
export function shouldEnableDevTools(): boolean {
  return (
    isDevelopment() && 
    isBrowser() && 
    isUniH5() && 
    !isSSR()
  )
}

/**
 * 获取当前平台信息
 */
export function getPlatformInfo(): {
  platform: string
  environment: string
  isBrowser: boolean
  isSSR: boolean
  shouldEnableDevTools: boolean
} {
  let platform = 'unknown'
  
  if (isUniH5()) {
    platform = 'h5'
  } else if (isMiniProgram()) {
    platform = 'miniprogram'
  } else if (isApp()) {
    platform = 'app'
  }

  return {
    platform,
    environment: process.env.NODE_ENV || 'unknown',
    isBrowser: isBrowser(),
    isSSR: isSSR(),
    shouldEnableDevTools: shouldEnableDevTools(),
  }
}

/**
 * 安全地执行只在浏览器环境中运行的代码
 */
export function runInBrowser<T>(fn: () => T): T | null {
  if (isBrowser()) {
    try {
      return fn()
    } catch (error) {
      console.warn('浏览器环境代码执行失败:', error)
      return null
    }
  }
  return null
}

/**
 * 安全地执行只在开发环境中运行的代码
 */
export function runInDevelopment<T>(fn: () => T): T | null {
  if (isDevelopment()) {
    try {
      return fn()
    } catch (error) {
      console.warn('开发环境代码执行失败:', error)
      return null
    }
  }
  return null
}

/**
 * 安全地执行开发工具相关代码
 */
export function runDevTools<T>(fn: () => T): T | null {
  if (shouldEnableDevTools()) {
    try {
      return fn()
    } catch (error) {
      console.warn('开发工具代码执行失败:', error)
      return null
    }
  }
  return null
}

/**
 * 延迟执行，等待DOM准备就绪
 */
export function waitForDOM(): Promise<void> {
  return new Promise((resolve) => {
    if (!isBrowser()) {
      resolve()
      return
    }

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => resolve(), { once: true })
    } else {
      resolve()
    }
  })
}

/**
 * 检测是否支持现代浏览器特性
 */
export function checkBrowserSupport(): {
  supportsES6: boolean
  supportsModules: boolean
  supportsWebComponents: boolean
  supportsIntersectionObserver: boolean
} {
  if (!isBrowser()) {
    return {
      supportsES6: false,
      supportsModules: false,
      supportsWebComponents: false,
      supportsIntersectionObserver: false,
    }
  }

  return {
    supportsES6: typeof Symbol !== 'undefined',
    supportsModules: 'noModule' in document.createElement('script'),
    supportsWebComponents: 'customElements' in window,
    supportsIntersectionObserver: 'IntersectionObserver' in window,
  }
}

// 导出平台信息（用于调试）
if (isDevelopment() && isBrowser()) {
  const platformInfo = getPlatformInfo()
  console.log('🔍 平台信息:', platformInfo)
  
  const browserSupport = checkBrowserSupport()
  console.log('🌐 浏览器支持:', browserSupport)
}

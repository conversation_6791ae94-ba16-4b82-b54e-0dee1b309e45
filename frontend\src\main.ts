import { createSSRApp } from 'vue'
import App from './App.vue'
import { pinia } from './stores'

// 导入全局样式
import './styles/global.scss'

// Stagewise集成（仅在开发环境和浏览器环境中）
import('./utils/environment').then(({ shouldEnableDevTools }) => {
  if (shouldEnableDevTools()) {
    import('./utils/stagewise').then(({ initStagewise }) => {
      initStagewise()
    }).catch(error => {
      console.warn('Stagewise初始化失败:', error)
    })
  }
}).catch(() => {
  // 静默忽略环境检测失败
})

export function createApp() {
  const app = createSSRApp(App)

  // 使用Pinia状态管理
  app.use(pinia)

  return {
    app
  }
}

/**
 * Stagewise配置文件
 * 
 * 功能说明：
 * 1. 配置Stagewise工具栏的行为
 * 2. 设置开发环境专用功能
 * 3. 配置AI代理集成
 */

export default {
  // 基本配置
  enabled: process.env.NODE_ENV === 'development', // 仅在开发环境启用
  
  // 工具栏配置
  toolbar: {
    position: 'bottom-right', // 工具栏位置
    theme: 'dark', // 主题：dark | light | auto
    size: 'medium', // 大小：small | medium | large
    autoHide: false, // 是否自动隐藏
    zIndex: 9999, // z-index层级
  },

  // 项目配置
  project: {
    name: '营业厅语音对讲系统',
    framework: 'vue',
    version: '1.0.0',
    description: '基于uni-app的语音对讲系统前端',
  },

  // AI代理配置
  ai: {
    enabled: true,
    provider: 'default', // AI提供商
    model: 'gpt-4', // 使用的模型
    contextWindow: 4000, // 上下文窗口大小
  },

  // 元素选择器配置
  selector: {
    highlightColor: '#667eea', // 高亮颜色
    borderWidth: 2, // 边框宽度
    showTooltip: true, // 显示工具提示
    excludeSelectors: [
      '.stagewise-toolbar',
      '.stagewise-overlay',
      'script',
      'style',
      'meta',
      'link'
    ], // 排除的选择器
  },

  // 注释系统配置
  comments: {
    enabled: true,
    maxLength: 1000, // 最大注释长度
    autoSave: true, // 自动保存
    showTimestamp: true, // 显示时间戳
    allowMarkdown: true, // 允许Markdown格式
  },

  // 代码生成配置
  codeGeneration: {
    enabled: true,
    outputPath: './src/components', // 生成代码的输出路径
    templatePath: './templates', // 模板路径
    backupOriginal: true, // 备份原始文件
  },

  // 集成配置
  integrations: {
    vscode: {
      enabled: true,
      extensionId: 'stagewise.stagewise-vscode-extension',
    },
    git: {
      enabled: true,
      autoCommit: false, // 是否自动提交更改
      commitPrefix: '[stagewise]', // 提交信息前缀
    },
  },

  // 调试配置
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    logLevel: 'info', // 日志级别：error | warn | info | debug
    showPerformance: true, // 显示性能信息
  },

  // 安全配置
  security: {
    allowedOrigins: [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001',
    ], // 允许的源
    requireAuth: false, // 是否需要认证
  },

  // 自定义插件
  plugins: [
    '@stagewise-plugins/vue', // Vue特定功能插件
  ],

  // 快捷键配置
  shortcuts: {
    toggleToolbar: 'Ctrl+Shift+S', // 切换工具栏显示
    selectElement: 'Ctrl+Click', // 选择元素
    addComment: 'Ctrl+Shift+C', // 添加注释
    generateCode: 'Ctrl+Shift+G', // 生成代码
  },

  // 实验性功能
  experimental: {
    aiCodeReview: true, // AI代码审查
    smartSuggestions: true, // 智能建议
    realTimeCollaboration: false, // 实时协作（实验性）
  },
}

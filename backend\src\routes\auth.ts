/**
 * 认证相关路由 - 重构版本
 *
 * 功能说明：
 * 1. 用户登录（简单用户名登录，无需注册）
 * 2. 用户登出
 * 3. Token验证和刷新
 * 4. 用户状态检查
 */

import { Router, Request, Response } from 'express'
import { logger } from '@/utils/logger'
import { DatabaseManager } from '@/database/DatabaseManager'
import { validateRequest } from '@/middleware/validation'
import { config } from '@/config/config'
import { authUtils } from '@/middleware/auth'
import jwt from 'jsonwebtoken'
// 本地类型定义
interface UserInfo {
  id: number
  username: string
  displayName?: string
  status: string
}

interface LoginRequest {
  username: string
  displayName?: string
}

interface LoginResponse {
  success: boolean
  message?: string
  data?: {
    user: any
    token: string
    refreshToken: string
    expiresIn: string
  }
}

enum UserStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  TALKING = 'talking',
  BUSY = 'busy'
}

const router = Router()
const dbManager = DatabaseManager.getInstance()

// 登录验证规则（使用共享验证）
const loginValidation = {
  username: {
    required: true,
    type: 'string',
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/ // 支持中英文、数字、下划线
  }
}

/**
 * 用户登录
 * POST /api/auth/login
 */
router.post('/login', validateRequest(loginValidation), async (req: Request, res: Response) => {
  try {
    const { username }: LoginRequest = req.body

    // 简单的用户名验证
    if (!username || username.length < 2 || username.length > 50) {
      return res.status(400).json({
        success: false,
        message: '用户名长度必须在2-50个字符之间',
        code: 'INVALID_USERNAME'
      })
    }

    // 检查用户是否存在，不存在则创建
    let user = await dbManager.getUserByUsername(username)

    if (!user) {
      // 创建新用户
      const userId = await dbManager.createUser({
        username,
        displayName: username, // 默认显示名为用户名
        status: UserStatus.ONLINE,
        createdAt: new Date(),
        lastLoginAt: new Date()
      })

      user = await dbManager.getUserById(userId)
      logger.info(`New user created: ${username} (ID: ${userId})`)
    } else {
      // 更新最后登录时间
      await dbManager.updateUser(user.id, {
        lastLoginAt: new Date(),
        status: UserStatus.ONLINE
      })
      logger.info(`User login: ${username} (ID: ${user.id})`)
    }

    // 构建用户信息对象
    const userInfo: any = {
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      status: UserStatus.ONLINE,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt,
      permissions: authUtils.getDefaultPermissions()
    }

    // 使用共享工具生成Token
    const token = authUtils.generateToken(userInfo)
    const refreshToken = authUtils.generateRefreshToken(user.id)

    const response: LoginResponse = {
      success: true,
      message: 'Login successful',
      data: {
        user: userInfo,
        token,
        refreshToken,
        expiresIn: config.security.jwtExpiresIn
      }
    }

    res.json(response)

  } catch (error) {
    logger.error('Login error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'LOGIN_ERROR'
    })
  }
})

/**
 * 用户登出
 * POST /api/auth/logout
 */
router.post('/logout', async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided',
        code: 'NO_TOKEN'
      })
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, config.security.jwtSecret) as any

    // 更新用户状态为离线
    await dbManager.updateUser(decoded.userId, {
      status: 'offline',
      lastLogoutAt: new Date()
    })

    // 获取用户信息用于日志
    const user = await dbManager.getUserById(decoded.userId)

    logger.info(`User logout: ${user.username} (ID: ${decoded.userId})`)

    res.json({
      success: true,
      message: 'Logout successful',
      data: {
        userId: decoded.userId,
        username: user.username,
        logoutTime: new Date().toISOString()
      }
    })

  } catch (error) {
    logger.error('Logout error:', error)

    // 即使出错也返回成功，避免前端无法完成登出
    res.json({
      success: true,
      message: 'Logout completed with warnings',
      warning: 'Some cleanup operations may have failed'
    })
  }
})

/**
 * Token刷新
 * POST /api/auth/refresh
 */
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token is required',
        code: 'NO_REFRESH_TOKEN'
      })
    }

    // 验证刷新Token
    const decoded = jwt.verify(refreshToken, config.security.jwtRefreshSecret) as any
    const user = await dbManager.getUserById(decoded.userId)

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      })
    }

    // 生成新的访问Token
    const newToken = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        displayName: user.displayName
      } as any,
      config.security.jwtSecret,
      {
        expiresIn: config.security.jwtExpiresIn
      }
    )

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token: newToken,
        expiresIn: config.security.jwtExpiresIn
      }
    })

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token',
        code: 'INVALID_REFRESH_TOKEN'
      })
    }

    logger.error('Token refresh error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'REFRESH_ERROR'
    })
  }
})

/**
 * 验证Token状态
 * GET /api/auth/verify
 */
router.get('/verify', async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided',
        code: 'NO_TOKEN'
      })
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, config.security.jwtSecret) as any
    const user = await dbManager.getUserById(decoded.userId)

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      })
    }

    res.json({
      success: true,
      message: 'Token is valid',
      data: {
        user: {
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          status: user.status,
          lastLoginAt: user.lastLoginAt
        },
        tokenInfo: {
          userId: decoded.userId,
          username: decoded.username,
          iat: decoded.iat,
          exp: decoded.exp
        }
      }
    })

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      })
    }

    logger.error('Token verification error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'VERIFY_ERROR'
    })
  }
})

export default router

/**
 * 查询数据库中的用户信息脚本
 */

const sqlite3 = require('sqlite3');
const { open } = require('sqlite');
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, 'data/talking.db');

console.log('正在查询数据库中的用户信息...');
console.log('数据库路径:', dbPath);

async function checkUsers() {
  let db;
  try {
    // 打开数据库连接
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    
    console.log('✅ 已连接到SQLite数据库');

    // 查询用户信息
    const query = `
      SELECT 
        id,
        username,
        display_name,
        status,
        last_active_time,
        created_at,
        updated_at
      FROM users 
      ORDER BY last_active_time DESC
    `;

    const rows = await db.all(query);

    console.log('\n📊 数据库中的用户信息:');
    console.log('='.repeat(80));
    
    if (rows.length === 0) {
      console.log('❌ 数据库中没有用户数据');
    } else {
      console.log(`📈 总共找到 ${rows.length} 个用户\n`);
      
      // 按状态分组统计
      const statusCount = {};
      rows.forEach(user => {
        statusCount[user.status] = (statusCount[user.status] || 0) + 1;
      });
      
      console.log('📊 用户状态统计:');
      Object.entries(statusCount).forEach(([status, count]) => {
        const emoji = getStatusEmoji(status);
        console.log(`  ${emoji} ${status}: ${count} 个用户`);
      });
      
      console.log('\n👥 详细用户列表:');
      console.log('-'.repeat(80));
      
      rows.forEach((user, index) => {
        const statusEmoji = getStatusEmoji(user.status);
        const lastActive = user.last_active_time ? 
          new Date(user.last_active_time).toLocaleString('zh-CN') : '未知';
        const created = user.created_at ? 
          new Date(user.created_at).toLocaleString('zh-CN') : '未知';
        
        console.log(`${index + 1}. 用户ID: ${user.id}`);
        console.log(`   用户名: ${user.username}`);
        console.log(`   显示名: ${user.display_name || '未设置'}`);
        console.log(`   状态: ${statusEmoji} ${user.status}`);
        console.log(`   最后活跃: ${lastActive}`);
        console.log(`   创建时间: ${created}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('查询失败:', error.message);
  } finally {
    // 关闭数据库连接
    if (db) {
      await db.close();
      console.log('✅ 数据库连接已关闭');
    }
  }
}

// 获取状态对应的emoji
function getStatusEmoji(status) {
  const emojiMap = {
    'online': '🟢',
    'offline': '⚫',
    'talking': '🔵',
    'busy': '🟡'
  };
  return emojiMap[status] || '❓';
}

// 运行查询
checkUsers();

/**
 * 用户管理相关路由
 * 
 * 功能说明：
 * 1. 获取用户信息
 * 2. 更新用户信息
 * 3. 获取在线用户列表
 * 4. 用户状态管理
 * 5. 用户搜索和查询
 */

import { Router, Request, Response } from 'express'
import { logger } from '@/utils/logger'
import { DatabaseManager } from '@/database/DatabaseManager'
import { validateRequest } from '@/middleware/validation'

const router = Router()
const dbManager = DatabaseManager.getInstance()

// 用户信息更新验证规则
const updateUserValidation = {
  displayName: {
    required: false,
    type: 'string',
    minLength: 1,
    maxLength: 100
  },
  status: {
    required: false,
    type: 'string',
    enum: ['online', 'offline', 'talking', 'busy']
  }
}

// 用户搜索验证规则
const searchValidation = {
  keyword: {
    required: false,
    type: 'string',
    minLength: 1,
    maxLength: 50
  },
  status: {
    required: false,
    type: 'string',
    enum: ['online', 'offline', 'talking', 'busy', 'all']
  },
  page: {
    required: false,
    type: 'number',
    min: 1
  },
  limit: {
    required: false,
    type: 'number',
    min: 1,
    max: 100
  }
}

/**
 * 获取当前用户信息
 * GET /api/users/profile
 */
router.get('/profile', async (req: Request, res: Response) => {
  try {
    const userId = (req as any).userId
    const user = await dbManager.getUserById(userId)

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      })
    }

    res.json({
      success: true,
      message: 'User profile retrieved successfully',
      data: {
        user: {
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          status: user.status,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt,
          lastLogoutAt: user.lastLogoutAt
        }
      }
    })

  } catch (error) {
    logger.error('Get user profile error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_PROFILE_ERROR'
    })
  }
})

/**
 * 更新当前用户信息
 * PUT /api/users/profile
 */
router.put('/profile', validateRequest(updateUserValidation), async (req: Request, res: Response) => {
  try {
    const userId = (req as any).userId
    const { displayName, status } = req.body

    const updateData: any = {}
    if (displayName !== undefined) updateData.displayName = displayName
    if (status !== undefined) updateData.status = status

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields to update',
        code: 'NO_UPDATE_FIELDS'
      })
    }

    await dbManager.updateUser(userId, updateData)
    const updatedUser = await dbManager.getUserById(userId)

    logger.info(`User profile updated: ${updatedUser.username} (ID: ${userId})`, updateData)

    res.json({
      success: true,
      message: 'User profile updated successfully',
      data: {
        user: {
          id: updatedUser.id,
          username: updatedUser.username,
          displayName: updatedUser.displayName,
          status: updatedUser.status,
          createdAt: updatedUser.createdAt,
          lastLoginAt: updatedUser.lastLoginAt,
          lastLogoutAt: updatedUser.lastLogoutAt
        }
      }
    })

  } catch (error) {
    logger.error('Update user profile error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'UPDATE_PROFILE_ERROR'
    })
  }
})

/**
 * 获取在线用户列表
 * GET /api/users/online
 */
router.get('/online', async (req: Request, res: Response) => {
  try {
    const currentUserId = (req as any).userId
    const onlineUsers = await dbManager.getOnlineUsers()

    // 过滤掉当前用户
    const filteredUsers = onlineUsers.filter(user => user.id !== currentUserId)

    res.json({
      success: true,
      message: 'Online users retrieved successfully',
      data: {
        users: filteredUsers.map(user => ({
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          status: user.status,
          lastLoginAt: user.lastLoginAt
        })),
        total: filteredUsers.length
      }
    })

  } catch (error) {
    logger.error('Get online users error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_ONLINE_USERS_ERROR'
    })
  }
})

/**
 * 搜索用户
 * GET /api/users/search
 */
router.get('/search', validateRequest(searchValidation, 'query'), async (req: Request, res: Response) => {
  try {
    const { keyword = '', status = 'all', page = 1, limit = 20 } = req.query
    const currentUserId = (req as any).userId

    const searchOptions = {
      keyword: keyword as string,
      status: status as string,
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      excludeUserId: currentUserId
    }

    const result = await dbManager.searchUsers(searchOptions)

    res.json({
      success: true,
      message: 'Users search completed successfully',
      data: {
        users: result.users.map(user => ({
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          status: user.status,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt
        })),
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / result.limit)
        }
      }
    })

  } catch (error) {
    logger.error('Search users error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'SEARCH_USERS_ERROR'
    })
  }
})

/**
 * 获取用户详情
 * GET /api/users/:userId
 */
router.get('/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params
    const targetUserId = parseInt(userId)

    if (isNaN(targetUserId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID',
        code: 'INVALID_USER_ID'
      })
    }

    const user = await dbManager.getUserById(targetUserId)

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      })
    }

    res.json({
      success: true,
      message: 'User details retrieved successfully',
      data: {
        user: {
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          status: user.status,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        }
      }
    })

  } catch (error) {
    logger.error('Get user details error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_USER_DETAILS_ERROR'
    })
  }
})

/**
 * 更新用户状态
 * PUT /api/users/status
 */
router.put('/status', async (req: Request, res: Response) => {
  try {
    const userId = (req as any).userId
    const { status } = req.body

    if (!status || !['online', 'offline', 'talking', 'busy'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status value',
        code: 'INVALID_STATUS'
      })
    }

    await dbManager.updateUser(userId, { status })
    const user = await dbManager.getUserById(userId)

    logger.info(`User status updated: ${user.username} (ID: ${userId}) -> ${status}`)

    res.json({
      success: true,
      message: 'User status updated successfully',
      data: {
        userId: user.id,
        username: user.username,
        status: user.status
      }
    })

  } catch (error) {
    logger.error('Update user status error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'UPDATE_STATUS_ERROR'
    })
  }
})

export default router

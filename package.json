{"name": "talking-system", "version": "1.0.0", "description": "营业厅语音对讲系统 - 前后端一体化启动", "private": true, "scripts": {"install:all": "npm install && npm install --prefix frontend && npm install --prefix backend", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" --names \"BACKEND,FRONTEND\" --prefix-colors \"blue,green\"", "dev:frontend": "cd frontend && npm run dev:h5", "dev:backend": "cd backend && npm run dev", "dev:frontend-only": "cd frontend && npm run dev:h5", "dev:backend-only": "cd backend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build:h5", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\" --names \"BACKEND,FRONTEND\" --prefix-colors \"blue,green\"", "start:frontend": "cd frontend && npm run serve", "start:backend": "cd backend && npm run start", "test": "npm run test:backend", "test:backend": "cd backend && npm run test", "test:frontend": "echo 'Frontend tests not configured yet'", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf dist node_modules", "clean:frontend": "cd frontend && rm -rf dist unpackage node_modules", "quality-check": "ts-node --transpile-only scripts/quality-check.ts", "quality": "npm run quality-check", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:backend": "eslint backend/", "lint:frontend": "eslint frontend/", "lint:shared": "eslint shared/"}, "devDependencies": {"@dcloudio/types": "3.4.15", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@eslint/js": "^9.30.1", "@jest/globals": "^30.0.4", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vue/runtime-core": "3.5.17", "concurrently": "^9.2.0", "eslint": "^9.30.1", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "jscpd": "^4.0.5", "sass": "^1.79.6", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3", "typescript-eslint": "^8.35.1", "vite": "^5.4.0", "vue-tsc": "^3.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["uni-app", "express", "websocket", "voice-intercom", "real-time-communication"], "author": "AI Assistant", "license": "MIT", "dependencies": {"@dcloudio/uni-components": "^3.0.0-4060620250520001", "@types/jsonwebtoken": "^9.0.10", "jsonwebtoken": "^9.0.2", "socket.io-client": "^4.8.1"}}
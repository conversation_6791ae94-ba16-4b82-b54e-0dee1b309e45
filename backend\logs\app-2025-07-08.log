2025-07-08 08:32:09.488 [INFO]: WebSocket server initialized
2025-07-08 08:32:09.489 [INFO]: WebSocket server initialized
2025-07-08 08:32:09.506 [INFO]: 🚀 启动开发服务器...
2025-07-08 08:32:09.507 [INFO]: 📦 初始化数据库...
2025-07-08 08:32:09.514 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:32:09.517 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:32:09.518 [INFO]: Database tables created successfully
2025-07-08 08:32:09.519 [INFO]: Database initialized successfully
2025-07-08 08:32:09.519 [INFO]: ✅ 数据库初始化完成
2025-07-08 08:32:09.545 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 08:32:09.545 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 08:32:09.546 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 08:32:09.552 [INFO]: Server is running on http://localhost:3001
2025-07-08 08:32:09.553 [INFO]: Environment: development
2025-07-08 08:32:09.553 [INFO]: WebSocket server is ready
2025-07-08 08:32:09.554 [INFO]: Database tables created successfully
2025-07-08 08:32:09.555 [INFO]: Database initialized successfully
2025-07-08 08:32:09.555 [INFO]: Database initialized successfully
2025-07-08 08:32:12.463 [INFO]: 🛑 正在关闭服务器...
2025-07-08 08:32:12.464 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 08:32:12.465 [INFO]: HTTP server closed
2025-07-08 08:32:12.467 [INFO]: WebSocket server shutdown completed
2025-07-08 08:32:12.467 [INFO]: WebSocket connections closed
2025-07-08 08:53:40.443 [INFO]: WebSocket server initialized
2025-07-08 08:53:40.444 [INFO]: WebSocket server initialized
2025-07-08 08:53:40.459 [INFO]: 🚀 启动开发服务器...
2025-07-08 08:53:40.460 [INFO]: 📦 初始化数据库...
2025-07-08 08:53:40.467 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:53:40.470 [INFO]: Database tables created successfully
2025-07-08 08:53:40.471 [INFO]: Database initialized successfully
2025-07-08 08:53:40.472 [INFO]: ✅ 数据库初始化完成
2025-07-08 08:53:40.502 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 08:53:40.503 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 08:53:40.504 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 08:53:40.508 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:53:40.513 [INFO]: Server is running on http://localhost:3001
2025-07-08 08:53:40.514 [INFO]: Environment: development
2025-07-08 08:53:40.514 [INFO]: WebSocket server is ready
2025-07-08 08:53:40.516 [INFO]: Database tables created successfully
2025-07-08 08:53:40.517 [INFO]: Database initialized successfully
2025-07-08 08:53:40.517 [INFO]: Database initialized successfully
2025-07-08 08:53:56.072 [INFO]: ::1 - - [08/Jul/2025:00:53:56 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:53:56.073 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 08:53:56.108 [WARN]: WebSocket authentication failed {"socketId":"9ghcKK1xBx9Fp4ozAAAB","error":"Token verification failed"}
2025-07-08 08:55:53.547 [INFO]: ::1 - - [08/Jul/2025:00:55:53 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:55:53.547 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 08:55:53.573 [WARN]: WebSocket authentication failed {"socketId":"R6Dsz8QbhQV9ZahCAAAD","error":"Token verification failed"}
2025-07-08 08:56:52.216 [INFO]: ::1 - - [08/Jul/2025:00:56:52 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:56:52.217 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:56:52.233 [WARN]: WebSocket authentication failed {"socketId":"5-Teq65wIksdQzAdAAAF","error":"Token verification failed"}
2025-07-08 08:57:13.492 [INFO]: ::1 - - [08/Jul/2025:00:57:13 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:13.493 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:13.510 [WARN]: WebSocket authentication failed {"socketId":"qiPJXLo5fUDWNRnDAAAH","error":"Token verification failed"}
2025-07-08 08:57:31.501 [INFO]: ::1 - - [08/Jul/2025:00:57:31 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:31.501 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:31.522 [WARN]: WebSocket authentication failed {"socketId":"H4bGPPGYkq-DD0O9AAAJ","error":"Token verification failed"}
2025-07-08 08:57:48.727 [INFO]: ::1 - - [08/Jul/2025:00:57:48 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:48.728 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:48.754 [WARN]: WebSocket authentication failed {"socketId":"alSIMBS1hwc_PTuWAAAL","error":"Token verification failed"}
2025-07-08 08:58:09.435 [INFO]: ::1 - - [08/Jul/2025:00:58:09 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:58:09.436 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 08:58:09.451 [WARN]: WebSocket authentication failed {"socketId":"3yt-dOe1iwPEd3drAAAN","error":"Token verification failed"}
2025-07-08 08:58:25.601 [INFO]: ::1 - - [08/Jul/2025:00:58:25 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:58:25.601 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:58:25.629 [WARN]: WebSocket authentication failed {"socketId":"hYU9glokUcEwl9SqAAAP","error":"Token verification failed"}
2025-07-08 08:59:13.753 [INFO]: ::1 - - [08/Jul/2025:00:59:13 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:59:13.754 [INFO]: GET /health - 200 - 4ms - ::1
2025-07-08 08:59:13.771 [WARN]: WebSocket authentication failed {"socketId":"TfcGikiNQit0BhkpAAAR","error":"Token verification failed"}
2025-07-08 08:59:29.249 [INFO]: ::1 - - [08/Jul/2025:00:59:29 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:59:29.249 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:59:29.262 [WARN]: WebSocket authentication failed {"socketId":"FdA9-3DoUmPjPVI7AAAT","error":"Token verification failed"}
2025-07-08 09:19:09.864 [INFO]: ::1 - - [08/Jul/2025:01:19:09 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:19:09.864 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:19:22.557 [INFO]: ::1 - - [08/Jul/2025:01:19:22 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:19:22.558 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:20:04.570 [INFO]: ::1 - - [08/Jul/2025:01:20:04 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:20:04.570 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:20:44.787 [INFO]: ::1 - - [08/Jul/2025:01:20:44 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:20:44.788 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:21:28.252 [INFO]: ::1 - - [08/Jul/2025:01:21:28 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:21:28.252 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:21:48.114 [INFO]: ::1 - - [08/Jul/2025:01:21:48 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:21:48.114 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:22:12.918 [INFO]: WebSocket server initialized
2025-07-08 09:22:12.919 [INFO]: WebSocket server initialized
2025-07-08 09:22:12.932 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:12.932 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:12.936 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:12.938 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:12.939 [INFO]: Database tables created successfully
2025-07-08 09:22:12.941 [INFO]: Database initialized successfully
2025-07-08 09:22:12.942 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:12.966 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:12.966 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:12.966 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:12.975 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.975 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:22:12.976 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.976 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 09:22:12.978 [INFO]: HTTP server closed
2025-07-08 09:22:12.979 [INFO]: WebSocket server shutdown completed
2025-07-08 09:22:12.979 [INFO]: WebSocket connections closed
2025-07-08 09:22:47.753 [INFO]: WebSocket server initialized
2025-07-08 09:22:47.754 [INFO]: WebSocket server initialized
2025-07-08 09:22:47.770 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:47.770 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:47.778 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:47.781 [INFO]: Database tables created successfully
2025-07-08 09:22:47.783 [INFO]: Database initialized successfully
2025-07-08 09:22:47.784 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:47.814 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:47.815 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:47.815 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:47.818 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:47.821 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:22:47.821 [INFO]: Environment: development
2025-07-08 09:22:47.822 [INFO]: WebSocket server is ready
2025-07-08 09:22:47.825 [INFO]: Database tables created successfully
2025-07-08 09:22:47.826 [INFO]: Database initialized successfully
2025-07-08 09:22:47.827 [INFO]: Database initialized successfully
2025-07-08 09:22:51.389 [INFO]: ::1 - - [08/Jul/2025:01:22:51 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:22:51.390 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 09:22:54.663 [INFO]: WebSocket server initialized
2025-07-08 09:22:54.664 [INFO]: WebSocket server initialized
2025-07-08 09:22:54.676 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:54.677 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:54.682 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:54.684 [INFO]: Database tables created successfully
2025-07-08 09:22:54.685 [INFO]: Database initialized successfully
2025-07-08 09:22:54.686 [INFO]: Database initialized successfully
2025-07-08 09:22:54.688 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:54.690 [INFO]: Database tables created successfully
2025-07-08 09:22:54.690 [INFO]: Database initialized successfully
2025-07-08 09:22:54.691 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:54.714 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:54.714 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:54.715 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:54.726 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.726 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:22:54.727 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.727 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 09:22:54.728 [INFO]: HTTP server closed
2025-07-08 09:22:54.729 [INFO]: WebSocket server shutdown completed
2025-07-08 09:22:54.729 [INFO]: WebSocket connections closed
2025-07-08 09:23:18.057 [INFO]: WebSocket server initialized
2025-07-08 09:23:18.059 [INFO]: WebSocket server initialized
2025-07-08 09:23:18.068 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:23:18.069 [INFO]: 📦 初始化数据库...
2025-07-08 09:23:18.075 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:18.076 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:18.078 [INFO]: Database tables created successfully
2025-07-08 09:23:18.079 [INFO]: Database tables created successfully
2025-07-08 09:23:18.081 [INFO]: Database initialized successfully
2025-07-08 09:23:18.082 [INFO]: Database initialized successfully
2025-07-08 09:23:18.083 [INFO]: Database initialized successfully
2025-07-08 09:23:18.083 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:23:18.106 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:23:18.106 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:23:18.107 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:23:18.115 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:23:18.116 [INFO]: Environment: development
2025-07-08 09:23:18.116 [INFO]: WebSocket server is ready
2025-07-08 09:23:24.053 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:23:24.054 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:23:24.055 [INFO]: HTTP server closed
2025-07-08 09:23:24.057 [INFO]: WebSocket server shutdown completed
2025-07-08 09:23:24.057 [INFO]: WebSocket connections closed
2025-07-08 09:23:39.169 [INFO]: WebSocket server initialized
2025-07-08 09:23:39.170 [INFO]: WebSocket server initialized
2025-07-08 09:23:39.180 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:23:39.181 [INFO]: 📦 初始化数据库...
2025-07-08 09:23:39.187 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:39.191 [INFO]: Database tables created successfully
2025-07-08 09:23:39.191 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:39.192 [INFO]: Database initialized successfully
2025-07-08 09:23:39.193 [INFO]: Database initialized successfully
2025-07-08 09:23:39.194 [INFO]: Database tables created successfully
2025-07-08 09:23:39.195 [INFO]: Database initialized successfully
2025-07-08 09:23:39.195 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:23:39.220 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:23:39.220 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:23:39.220 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:23:39.227 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:23:39.227 [INFO]: Environment: development
2025-07-08 09:23:39.227 [INFO]: WebSocket server is ready
2025-07-08 09:23:42.939 [INFO]: ::1 - - [08/Jul/2025:01:23:42 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:23:42.940 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 09:24:01.492 [INFO]: ::1 - - [08/Jul/2025:01:24:01 +0000] "GET /health HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202"
2025-07-08 09:24:01.492 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:28:14.412 [INFO]: ::1 - - [08/Jul/2025:01:28:14 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:28:14.412 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 09:28:14.432 [WARN]: WebSocket authentication failed {"socketId":"8_XR2tmFBQIAJr-_AAAD","error":"Token verification failed"}
2025-07-08 09:28:20.991 [WARN]: WebSocket authentication failed {"socketId":"B9KAdXKwhGr8srKgAAAF","error":"Token verification failed"}
2025-07-08 09:28:26.928 [WARN]: WebSocket authentication failed {"socketId":"RRbcR3xhhPzvzn-mAAAH","error":"Token verification failed"}
2025-07-08 09:28:34.481 [INFO]: ::1 - - [08/Jul/2025:01:28:34 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:28:34.482 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:28:34.495 [WARN]: WebSocket authentication failed {"socketId":"2vrEqFrfIpWTafzBAAAJ","error":"Token verification failed"}
2025-07-08 09:29:55.530 [INFO]: ::1 - - [08/Jul/2025:01:29:55 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:29:55.531 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:29:55.547 [WARN]: WebSocket authentication failed {"socketId":"T7-m2x7K-21syW-FAAAL","error":"Token verification failed"}
2025-07-08 09:30:16.363 [INFO]: ::1 - - [08/Jul/2025:01:30:16 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:16.364 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:16.381 [WARN]: WebSocket authentication failed {"socketId":"THfATFZzJTzCXEoRAAAN","error":"Token verification failed"}
2025-07-08 09:30:32.400 [INFO]: ::1 - - [08/Jul/2025:01:30:32 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:32.401 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:32.468 [WARN]: WebSocket authentication failed {"socketId":"gdzD1gJT6i1OPOUDAAAP","error":"Token verification failed"}
2025-07-08 09:30:53.539 [INFO]: ::1 - - [08/Jul/2025:01:30:53 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:53.539 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:53.556 [WARN]: WebSocket authentication failed {"socketId":"bXWDHLGbqs39TzcIAAAR","error":"Token verification failed"}
2025-07-08 09:31:11.960 [INFO]: ::1 - - [08/Jul/2025:01:31:11 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:31:11.961 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:31:11.992 [WARN]: WebSocket authentication failed {"socketId":"bqmskIyz9cb4ebTlAAAT","error":"Token verification failed"}
2025-07-08 09:37:42.691 [INFO]: ::1 - - [08/Jul/2025:01:37:42 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:37:42.691 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:37:42.702 [WARN]: WebSocket authentication failed {"socketId":"H_7_D1wQKPd-zii1AAAV","error":"Token verification failed"}
2025-07-08 09:43:19.567 [INFO]: ::1 - - [08/Jul/2025:01:43:19 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:43:19.567 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:43:19.583 [WARN]: WebSocket authentication failed {"socketId":"Q2xGX0OnllMx9R0FAAAX","error":"Token verification failed"}
2025-07-08 09:43:35.334 [INFO]: ::1 - - [08/Jul/2025:01:43:35 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:43:35.335 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:43:35.344 [WARN]: WebSocket authentication failed {"socketId":"bEV0haHfau6HVq6JAAAZ","error":"Token verification failed"}
2025-07-08 09:47:10.876 [INFO]: ::1 - - [08/Jul/2025:01:47:10 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:10.877 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 09:47:10.884 [WARN]: WebSocket authentication failed {"socketId":"PcjOsZYItFO8HYelAAAb","error":"Token verification failed"}
2025-07-08 09:47:13.833 [INFO]: ::1 - - [08/Jul/2025:01:47:13 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:13.833 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:47:13.841 [WARN]: WebSocket authentication failed {"socketId":"CJcxVzKEGo4jnMhkAAAd","error":"Token verification failed"}
2025-07-08 09:47:27.316 [INFO]: ::1 - - [08/Jul/2025:01:47:27 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:27.317 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:47:27.332 [WARN]: WebSocket authentication failed {"socketId":"Qa3-CHQRuArmflK1AAAf","error":"Token verification failed"}
2025-07-08 09:48:45.452 [INFO]: ::1 - - [08/Jul/2025:01:48:45 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:48:45.453 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:48:45.471 [WARN]: WebSocket authentication failed {"socketId":"WlOgxJqjh_aC37_XAAAh","error":"Token verification failed"}
2025-07-08 09:49:01.621 [INFO]: ::1 - - [08/Jul/2025:01:49:01 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:01.621 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:49:01.648 [WARN]: WebSocket authentication failed {"socketId":"N_SpfSZ0r7BkIRIUAAAj","error":"Token verification failed"}
2025-07-08 09:49:19.552 [INFO]: ::1 - - [08/Jul/2025:01:49:19 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:19.553 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:19.567 [WARN]: WebSocket authentication failed {"socketId":"LHea6jtb9ITji3nmAAAl","error":"Token verification failed"}
2025-07-08 09:49:34.533 [INFO]: ::1 - - [08/Jul/2025:01:49:34 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:34.533 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:34.549 [WARN]: WebSocket authentication failed {"socketId":"JWFQXlQ8th4a6BD8AAAn","error":"Token verification failed"}
2025-07-08 09:49:49.962 [INFO]: ::1 - - [08/Jul/2025:01:49:49 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:49.962 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:49.998 [WARN]: WebSocket authentication failed {"socketId":"tPbA1pp8-c09eswWAAAp","error":"Token verification failed"}
2025-07-08 09:50:05.536 [INFO]: ::1 - - [08/Jul/2025:01:50:05 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:05.537 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:05.551 [WARN]: WebSocket authentication failed {"socketId":"euq5g-orUDj6tGxOAAAr","error":"Token verification failed"}
2025-07-08 09:50:20.607 [INFO]: ::1 - - [08/Jul/2025:01:50:20 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:20.608 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:20.625 [WARN]: WebSocket authentication failed {"socketId":"KNMcroly7YTLhfJbAAAt","error":"Token verification failed"}
2025-07-08 09:50:54.476 [INFO]: ::1 - - [08/Jul/2025:01:50:54 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:54.477 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:54.494 [WARN]: WebSocket authentication failed {"socketId":"JLMcU9AwvAysLA2TAAAv","error":"Token verification failed"}
2025-07-08 09:52:06.695 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:52:06.696 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:52:06.697 [INFO]: HTTP server closed
2025-07-08 09:52:06.698 [INFO]: WebSocket server shutdown completed
2025-07-08 09:52:06.698 [INFO]: WebSocket connections closed
2025-07-08 09:52:17.764 [INFO]: WebSocket server initialized
2025-07-08 09:52:17.765 [INFO]: WebSocket server initialized
2025-07-08 09:52:17.779 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:52:17.780 [INFO]: 📦 初始化数据库...
2025-07-08 09:52:17.786 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:52:17.789 [INFO]: Database tables created successfully
2025-07-08 09:52:17.790 [INFO]: Database initialized successfully
2025-07-08 09:52:17.791 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:52:17.817 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:52:17.818 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:52:17.818 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:52:17.820 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:52:17.824 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:52:17.825 [INFO]: Environment: development
2025-07-08 09:52:17.825 [INFO]: WebSocket server is ready
2025-07-08 09:52:17.827 [INFO]: Database tables created successfully
2025-07-08 09:52:17.827 [INFO]: Database initialized successfully
2025-07-08 09:52:17.828 [INFO]: Database initialized successfully
2025-07-08 09:52:20.775 [INFO]: ::1 - - [08/Jul/2025:01:52:20 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:52:20.775 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 09:52:20.817 [WARN]: WebSocket authentication failed {"socketId":"PwsfNpGbLCUvAulvAAAB","error":"Token verification failed"}
2025-07-08 09:52:34.296 [INFO]: ::1 - - [08/Jul/2025:01:52:34 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:52:34.296 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:52:34.312 [WARN]: WebSocket authentication failed {"socketId":"5lg0Lxyw2PNjB1rZAAAE","error":"Token verification failed"}
2025-07-08 09:54:09.206 [INFO]: ::1 - - [08/Jul/2025:01:54:09 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:54:09.207 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:54:09.222 [WARN]: WebSocket authentication failed {"socketId":"67-dAonOzMRpe4DqAAAH","error":"Token verification failed"}
2025-07-08 09:54:38.840 [INFO]: ::1 - - [08/Jul/2025:01:54:38 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:54:38.840 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:54:38.856 [WARN]: WebSocket authentication failed {"socketId":"b2yYqu22Y-m9gROqAAAK","error":"Token verification failed"}
2025-07-08 09:55:00.003 [INFO]: ::1 - - [08/Jul/2025:01:55:00 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:55:00.003 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:55:00.071 [WARN]: WebSocket authentication failed {"socketId":"C3FnjnJWsxVZrtu2AAAN","error":"Token verification failed"}
2025-07-08 09:55:33.284 [INFO]: ::1 - - [08/Jul/2025:01:55:33 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:55:33.284 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:55:33.306 [WARN]: WebSocket authentication failed {"socketId":"jUxMFkZ3o8mkL74rAAAQ","error":"Token verification failed"}
2025-07-08 09:59:27.279 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 09:59:27.280 [INFO]: ::1 - - [08/Jul/2025:01:59:27 +0000] "POST /api/auth/login HTTP/1.1" 500 72 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:59:27.281 [INFO]: POST /api/auth/login - 500 - 12ms - ::1
2025-07-08 09:59:40.835 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:59:40.835 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:59:40.836 [INFO]: HTTP server closed
2025-07-08 09:59:40.837 [INFO]: WebSocket server shutdown completed
2025-07-08 09:59:40.837 [INFO]: WebSocket connections closed
2025-07-08 09:59:53.315 [INFO]: WebSocket server initialized
2025-07-08 09:59:53.316 [INFO]: WebSocket server initialized
2025-07-08 09:59:53.331 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:59:53.332 [INFO]: 📦 初始化数据库...
2025-07-08 09:59:53.341 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:59:53.344 [INFO]: Database tables created successfully
2025-07-08 09:59:53.345 [INFO]: Database initialized successfully
2025-07-08 09:59:53.345 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:59:53.371 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:59:53.372 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:59:53.373 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:59:53.376 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:59:53.381 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:59:53.382 [INFO]: Environment: development
2025-07-08 09:59:53.382 [INFO]: WebSocket server is ready
2025-07-08 09:59:53.384 [INFO]: Database tables created successfully
2025-07-08 09:59:53.384 [INFO]: Database initialized successfully
2025-07-08 09:59:53.385 [INFO]: Database initialized successfully
2025-07-08 10:00:05.584 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:00:05.587 [INFO]: ::1 - - [08/Jul/2025:02:00:05 +0000] "POST /api/auth/login HTTP/1.1" 500 72 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:00:05.588 [INFO]: POST /api/auth/login - 500 - 12ms - ::1
2025-07-08 10:06:23.222 [INFO]: ::1 - - [08/Jul/2025:02:06:23 +0000] "GET /health HTTP/1.1" 200 120 "-" "-"
2025-07-08 10:06:23.222 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:06:23.230 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:06:23.232 [INFO]: ::1 - - [08/Jul/2025:02:06:23 +0000] "POST /api/auth/login HTTP/1.1" 500 72 "-" "-"
2025-07-08 10:06:23.232 [INFO]: POST /api/auth/login - 500 - 4ms - ::1
2025-07-08 10:06:54.075 [INFO]: WebSocket server initialized
2025-07-08 10:06:54.076 [INFO]: WebSocket server initialized
2025-07-08 10:06:54.087 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:06:54.093 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:06:54.095 [INFO]: Database tables created successfully
2025-07-08 10:06:54.096 [INFO]: Database initialized successfully
2025-07-08 10:06:54.097 [INFO]: Database initialized successfully
2025-07-08 10:06:54.120 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:06:54.121 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:06:54.122 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:07:25.307 [INFO]: WebSocket server initialized
2025-07-08 10:07:25.308 [INFO]: WebSocket server initialized
2025-07-08 10:07:25.319 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:07:25.324 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:07:25.326 [INFO]: Database tables created successfully
2025-07-08 10:07:25.327 [INFO]: Database initialized successfully
2025-07-08 10:07:25.328 [INFO]: Database initialized successfully
2025-07-08 10:07:25.351 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:07:25.352 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:07:25.352 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:07:25.360 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:07:25.361 [INFO]: Environment: development
2025-07-08 10:07:25.361 [INFO]: WebSocket server is ready
2025-07-08 10:07:25.361 [INFO]: Database initialized and ready
2025-07-08 10:07:51.196 [INFO]: ::1 - - [08/Jul/2025:02:07:51 +0000] "GET /health HTTP/1.1" 200 120 "-" "-"
2025-07-08 10:07:51.197 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 10:07:51.224 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:81:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:07:51.226 [INFO]: ::1 - - [08/Jul/2025:02:07:51 +0000] "POST /api/auth/login HTTP/1.1" 500 72 "-" "-"
2025-07-08 10:07:51.227 [INFO]: POST /api/auth/login - 500 - 12ms - ::1
2025-07-08 10:11:24.919 [INFO]: WebSocket server initialized
2025-07-08 10:11:24.920 [INFO]: WebSocket server initialized
2025-07-08 10:11:24.930 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:11:24.937 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:11:24.939 [INFO]: Database tables created successfully
2025-07-08 10:11:24.941 [INFO]: Database initialized successfully
2025-07-08 10:11:24.941 [INFO]: Database initialized successfully
2025-07-08 10:11:24.965 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:11:24.966 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:11:24.967 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:11:24.975 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:11:24.976 [INFO]: Environment: development
2025-07-08 10:11:24.976 [INFO]: WebSocket server is ready
2025-07-08 10:11:24.976 [INFO]: Database initialized and ready
2025-07-08 10:11:49.663 [INFO]: ::1 - - [08/Jul/2025:02:11:49 +0000] "GET /health HTTP/1.1" 200 120 "-" "-"
2025-07-08 10:11:49.664 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 10:11:49.718 [INFO]: New user created: testuser (ID: 1)
2025-07-08 10:11:49.724 [INFO]: ::1 - - [08/Jul/2025:02:11:49 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "-"
2025-07-08 10:11:49.725 [INFO]: POST /api/auth/login - 200 - 43ms - ::1
2025-07-08 10:11:49.742 [INFO]: WebSocket user authenticated {"socketId":"1J8BKsXOxo3Eq4igAAAB","userId":1,"username":"testuser"}
2025-07-08 10:11:49.750 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:11:49.751 [INFO]: 🛑 正在关闭服务器...
2025-07-08 10:11:49.751 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:11:49.752 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 10:11:49.756 [INFO]: HTTP server closed
2025-07-08 10:11:49.757 [INFO]: WebSocket server shutdown completed
2025-07-08 10:11:49.758 [INFO]: WebSocket connections closed
2025-07-08 10:11:49.842 [INFO]: Database connection closed
2025-07-08 10:12:42.541 [INFO]: WebSocket server initialized
2025-07-08 10:12:42.542 [INFO]: WebSocket server initialized
2025-07-08 10:12:42.556 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:12:42.562 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:12:42.564 [INFO]: Database tables created successfully
2025-07-08 10:12:42.565 [INFO]: Database initialized successfully
2025-07-08 10:12:42.566 [INFO]: Database initialized successfully
2025-07-08 10:12:42.589 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:12:42.590 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:12:42.590 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:12:42.596 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:12:42.596 [INFO]: Environment: development
2025-07-08 10:12:42.597 [INFO]: WebSocket server is ready
2025-07-08 10:12:42.597 [INFO]: Database initialized and ready
2025-07-08 10:13:18.113 [INFO]: New user created: 544 (ID: 2)
2025-07-08 10:13:18.120 [INFO]: ::1 - - [08/Jul/2025:02:13:18 +0000] "POST /api/auth/login HTTP/1.1" 200 612 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:13:18.121 [INFO]: POST /api/auth/login - 200 - 49ms - ::1
2025-07-08 10:13:19.630 [INFO]: ::1 - - [08/Jul/2025:02:13:19 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:13:19.630 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:13:19.650 [INFO]: WebSocket user authenticated {"socketId":"3LOfiTBwRWwTFkWGAAAB","userId":2,"username":"544"}
2025-07-08 10:13:19.660 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:13:19.661 [INFO]: 🛑 正在关闭服务器...
2025-07-08 10:13:19.661 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:13:19.661 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 10:13:19.737 [INFO]: Database connection closed
2025-07-08 10:15:38.717 [INFO]: WebSocket server initialized
2025-07-08 10:15:38.718 [INFO]: WebSocket server initialized
2025-07-08 10:15:38.728 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:15:38.735 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:15:38.738 [INFO]: Database tables created successfully
2025-07-08 10:15:38.739 [INFO]: Database initialized successfully
2025-07-08 10:15:38.739 [INFO]: Database initialized successfully
2025-07-08 10:15:38.766 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:15:38.767 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:15:38.768 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:15:38.774 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:15:38.774 [INFO]: Environment: development
2025-07-08 10:15:38.774 [INFO]: WebSocket server is ready
2025-07-08 10:15:38.775 [INFO]: Database initialized and ready
2025-07-08 10:15:41.873 [INFO]: ::1 - - [08/Jul/2025:02:15:41 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:15:41.874 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 10:15:41.910 [INFO]: WebSocket user authenticated {"socketId":"1TP4wyTP4gNuYtFJAAAB","userId":2,"username":"544"}
2025-07-08 10:15:41.923 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:15:41.924 [INFO]: 🛑 正在关闭服务器...
2025-07-08 10:15:41.925 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:15:41.926 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 10:19:05.575 [INFO]: WebSocket server initialized
2025-07-08 10:19:05.576 [INFO]: WebSocket server initialized
2025-07-08 10:19:05.591 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:19:05.598 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:19:05.601 [INFO]: Database tables created successfully
2025-07-08 10:19:05.602 [INFO]: Database initialized successfully
2025-07-08 10:19:05.603 [INFO]: Database initialized successfully
2025-07-08 10:19:05.629 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:19:05.630 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:19:05.630 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:19:05.639 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:19:05.640 [INFO]: Environment: development
2025-07-08 10:19:05.641 [INFO]: WebSocket server is ready
2025-07-08 10:19:05.641 [INFO]: Database initialized and ready
2025-07-08 10:19:32.717 [INFO]: ::1 - - [08/Jul/2025:02:19:32 +0000] "GET /health HTTP/1.1" 200 119 "-" "-"
2025-07-08 10:19:32.718 [INFO]: GET /health - 200 - 5ms - ::1
2025-07-08 10:19:32.774 [INFO]: User login: testuser (ID: 1)
2025-07-08 10:19:32.779 [INFO]: ::1 - - [08/Jul/2025:02:19:32 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "-"
2025-07-08 10:19:32.779 [INFO]: POST /api/auth/login - 200 - 46ms - ::1
2025-07-08 10:19:32.796 [INFO]: WebSocket user authenticated {"socketId":"rzfLvMQzQoaqhMfRAAAB","userId":1,"username":"testuser"}
2025-07-08 10:19:32.799 [INFO]: WebSocket connected {"socketId":"rzfLvMQzQoaqhMfRAAAB","userId":"1","type":"websocket","username":"testuser","displayName":"testuser"}
2025-07-08 10:19:32.803 [INFO]: Client initiated disconnect
2025-07-08 10:19:32.803 [INFO]: WebSocket disconnected {"socketId":"rzfLvMQzQoaqhMfRAAAB","userId":"1","type":"websocket","username":"testuser","reason":"client namespace disconnect","details":{},"duration":4,"recovered":false}
2025-07-08 10:39:01.433 [INFO]: ::1 - - [08/Jul/2025:02:39:01 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:01.434 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 10:39:01.455 [INFO]: WebSocket user authenticated {"socketId":"YudJiF9sPqzdysEbAAAE","userId":2,"username":"544"}
2025-07-08 10:39:01.456 [INFO]: WebSocket connected {"socketId":"YudJiF9sPqzdysEbAAAE","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:39:01.472 [INFO]: User authenticated {"userId":2,"username":"544","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:39:01.476 [INFO]: ::1 - - [08/Jul/2025:02:39:01 +0000] "GET /api/users/online HTTP/1.1" 200 142 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:01.479 [INFO]: GET /api/users/online - 200 - 7ms - ::1
2025-07-08 10:39:04.173 [INFO]: ::1 - - [08/Jul/2025:02:39:04 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:04.174 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:39:04.184 [INFO]: WebSocket user authenticated {"socketId":"awKePKyM97A0HFvNAAAH","userId":2,"username":"544"}
2025-07-08 10:39:04.185 [INFO]: WebSocket connected {"socketId":"awKePKyM97A0HFvNAAAH","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:39:04.192 [INFO]: User authenticated {"userId":2,"username":"544","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:39:04.195 [INFO]: ::1 - - [08/Jul/2025:02:39:04 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:04.196 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 10:39:07.823 [INFO]: ::1 - - [08/Jul/2025:02:39:07 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:07.824 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 10:39:07.834 [INFO]: WebSocket user authenticated {"socketId":"Vr243j3hBeVM2vzDAAAK","userId":2,"username":"544"}
2025-07-08 10:39:07.835 [INFO]: WebSocket connected {"socketId":"Vr243j3hBeVM2vzDAAAK","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:39:07.846 [INFO]: User authenticated {"userId":2,"username":"544","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:39:07.848 [INFO]: ::1 - - [08/Jul/2025:02:39:07 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:07.848 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 10:39:11.186 [INFO]: ::1 - - [08/Jul/2025:02:39:11 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:11.187 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:39:11.195 [INFO]: WebSocket user authenticated {"socketId":"hbBSZvfJ_mp3uAObAAAN","userId":2,"username":"544"}
2025-07-08 10:39:11.196 [INFO]: WebSocket connected {"socketId":"hbBSZvfJ_mp3uAObAAAN","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:39:11.208 [INFO]: User authenticated {"userId":2,"username":"544","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:39:11.210 [INFO]: ::1 - - [08/Jul/2025:02:39:11 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:11.210 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 10:39:15.268 [INFO]: Client initiated disconnect
2025-07-08 10:39:15.269 [INFO]: WebSocket disconnected {"socketId":"hbBSZvfJ_mp3uAObAAAN","userId":"2","type":"websocket","username":"544","reason":"client namespace disconnect","details":{},"duration":4071,"recovered":false}
2025-07-08 10:39:16.874 [INFO]: WebSocket user authenticated {"socketId":"W2yAub7av2mYOpPuAAAQ","userId":2,"username":"544"}
2025-07-08 10:39:16.876 [INFO]: WebSocket connected {"socketId":"W2yAub7av2mYOpPuAAAQ","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:05.957 [WARN]: Connection timeout {"socketId":"YudJiF9sPqzdysEbAAAE","userId":2,"username":"544","timeSinceLastActivity":304500}
2025-07-08 10:44:05.958 [INFO]: Server initiated disconnect
2025-07-08 10:44:05.959 [INFO]: WebSocket disconnected {"socketId":"YudJiF9sPqzdysEbAAAE","userId":"2","type":"websocket","username":"544","reason":"server namespace disconnect","details":{},"duration":304501,"recovered":false}
2025-07-08 10:44:05.960 [WARN]: Connection timeout {"socketId":"awKePKyM97A0HFvNAAAH","userId":2,"username":"544","timeSinceLastActivity":301771}
2025-07-08 10:44:05.960 [INFO]: Server initiated disconnect
2025-07-08 10:44:05.960 [INFO]: WebSocket disconnected {"socketId":"awKePKyM97A0HFvNAAAH","userId":"2","type":"websocket","username":"544","reason":"server namespace disconnect","details":{},"duration":301775,"recovered":false}
2025-07-08 10:44:08.976 [INFO]: WebSocket user authenticated {"socketId":"jESxRp4QJ3qjxaGTAAAT","userId":2,"username":"544"}
2025-07-08 10:44:08.977 [INFO]: WebSocket connected {"socketId":"jESxRp4QJ3qjxaGTAAAT","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:11.972 [INFO]: WebSocket user authenticated {"socketId":"FqXDp-6FdXE76EOLAAAW","userId":2,"username":"544"}
2025-07-08 10:44:11.973 [INFO]: WebSocket connected {"socketId":"FqXDp-6FdXE76EOLAAAW","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:35.970 [WARN]: Connection timeout {"socketId":"Vr243j3hBeVM2vzDAAAK","userId":2,"username":"544","timeSinceLastActivity":328135}
2025-07-08 10:44:35.971 [INFO]: Server initiated disconnect
2025-07-08 10:44:35.972 [INFO]: WebSocket disconnected {"socketId":"Vr243j3hBeVM2vzDAAAK","userId":"2","type":"websocket","username":"544","reason":"server namespace disconnect","details":{},"duration":328136,"recovered":false}
2025-07-08 10:44:35.972 [WARN]: Connection timeout {"socketId":"W2yAub7av2mYOpPuAAAQ","userId":2,"username":"544","timeSinceLastActivity":319094}
2025-07-08 10:44:35.973 [INFO]: Server initiated disconnect
2025-07-08 10:44:35.973 [INFO]: WebSocket disconnected {"socketId":"W2yAub7av2mYOpPuAAAQ","userId":"2","type":"websocket","username":"544","reason":"server namespace disconnect","details":{},"duration":319097,"recovered":false}
2025-07-08 10:44:38.990 [INFO]: WebSocket user authenticated {"socketId":"8SNxuAdruSAiBQGmAAAZ","userId":2,"username":"544"}
2025-07-08 10:44:38.991 [INFO]: WebSocket connected {"socketId":"8SNxuAdruSAiBQGmAAAZ","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:41.991 [INFO]: WebSocket user authenticated {"socketId":"dtO5CURnmG4UM5MAAAAc","userId":2,"username":"544"}
2025-07-08 10:44:41.991 [INFO]: WebSocket connected {"socketId":"dtO5CURnmG4UM5MAAAAc","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:49.697 [INFO]: User login: testuser (ID: 1)
2025-07-08 10:44:49.699 [INFO]: ::1 - - [08/Jul/2025:02:44:49 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
2025-07-08 10:44:49.699 [INFO]: POST /api/auth/login - 200 - 3ms - ::1
2025-07-08 10:44:49.716 [INFO]: WebSocket user authenticated {"socketId":"1IoDniu8aMZE04HNAAAf","userId":1,"username":"testuser"}
2025-07-08 10:44:49.717 [INFO]: WebSocket connected {"socketId":"1IoDniu8aMZE04HNAAAf","userId":"1","type":"websocket","username":"testuser","displayName":"testuser"}
2025-07-08 10:44:51.732 [INFO]: Client initiated disconnect
2025-07-08 10:44:51.733 [INFO]: WebSocket disconnected {"socketId":"1IoDniu8aMZE04HNAAAf","userId":"1","type":"websocket","username":"testuser","reason":"client namespace disconnect","details":{},"duration":2016,"recovered":false}
2025-07-08 10:45:12.902 [INFO]: WebSocket disconnected {"socketId":"jESxRp4QJ3qjxaGTAAAT","userId":"2","type":"websocket","username":"544","reason":"transport close","details":{},"duration":63924,"recovered":false}
2025-07-08 10:45:12.904 [INFO]: WebSocket disconnected {"socketId":"dtO5CURnmG4UM5MAAAAc","userId":"2","type":"websocket","username":"544","reason":"transport close","details":{},"duration":30913,"recovered":false}
2025-07-08 10:45:12.906 [INFO]: WebSocket disconnected {"socketId":"8SNxuAdruSAiBQGmAAAZ","userId":"2","type":"websocket","username":"544","reason":"transport close","details":{},"duration":33915,"recovered":false}
2025-07-08 10:45:12.907 [INFO]: WebSocket disconnected {"socketId":"FqXDp-6FdXE76EOLAAAW","userId":"2","type":"websocket","username":"544","reason":"transport close","details":{},"duration":60935,"recovered":false}
2025-07-08 10:46:20.286 [INFO]: New user created: 45654 (ID: 3)
2025-07-08 10:46:20.289 [INFO]: ::1 - - [08/Jul/2025:02:46:20 +0000] "POST /api/auth/login HTTP/1.1" 200 617 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:46:20.290 [INFO]: POST /api/auth/login - 200 - 5ms - ::1
2025-07-08 10:50:11.192 [INFO]: ::1 - - [08/Jul/2025:02:50:11 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:50:11.193 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 10:50:11.248 [INFO]: WebSocket user authenticated {"socketId":"J9gNDTYh5tvvTqTHAAAi","userId":3,"username":"45654"}
2025-07-08 10:50:11.249 [INFO]: WebSocket connected {"socketId":"J9gNDTYh5tvvTqTHAAAi","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:50:11.283 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:50:11.285 [INFO]: ::1 - - [08/Jul/2025:02:50:11 +0000] "GET /api/users/online HTTP/1.1" 200 186 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:50:11.286 [INFO]: GET /api/users/online - 200 - 5ms - ::1
2025-07-08 10:50:11.997 [INFO]: ::1 - - [08/Jul/2025:02:50:11 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:50:11.998 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 10:50:12.097 [INFO]: WebSocket user authenticated {"socketId":"iPlP14OUpiGbru60AAAl","userId":3,"username":"45654"}
2025-07-08 10:50:12.098 [INFO]: WebSocket connected {"socketId":"iPlP14OUpiGbru60AAAl","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:50:12.106 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:50:12.109 [INFO]: ::1 - - [08/Jul/2025:02:50:12 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:50:12.109 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 10:51:47.894 [INFO]: WebSocket server initialized
2025-07-08 10:51:47.895 [INFO]: WebSocket server initialized
2025-07-08 10:51:47.910 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:51:47.916 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:51:47.918 [INFO]: Database tables created successfully
2025-07-08 10:51:47.919 [INFO]: Database initialized successfully
2025-07-08 10:51:47.920 [INFO]: Database initialized successfully
2025-07-08 10:51:47.944 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:51:47.945 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:51:47.946 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:51:47.951 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:51:47.952 [INFO]: Environment: development
2025-07-08 10:51:47.952 [INFO]: WebSocket server is ready
2025-07-08 10:51:47.952 [INFO]: Database initialized and ready
2025-07-08 10:51:48.992 [INFO]: WebSocket user authenticated {"socketId":"CE029-CFpiwZ4rWfAAAB","userId":3,"username":"45654"}
2025-07-08 10:51:48.994 [INFO]: WebSocket connected {"socketId":"CE029-CFpiwZ4rWfAAAB","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:51:50.984 [INFO]: WebSocket user authenticated {"socketId":"q9_MkvTBIsLc7Jv9AAAE","userId":3,"username":"45654"}
2025-07-08 10:51:50.985 [INFO]: WebSocket connected {"socketId":"q9_MkvTBIsLc7Jv9AAAE","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:52:27.603 [INFO]: WebSocket disconnected {"socketId":"CE029-CFpiwZ4rWfAAAB","userId":"3","type":"websocket","username":"45654","reason":"transport close","details":{},"duration":38608,"recovered":false}
2025-07-08 10:52:29.838 [INFO]: WebSocket disconnected {"socketId":"q9_MkvTBIsLc7Jv9AAAE","userId":"3","type":"websocket","username":"45654","reason":"transport close","details":{},"duration":38853,"recovered":false}
2025-07-08 10:52:30.569 [INFO]: ::1 - - [08/Jul/2025:02:52:30 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:52:30.570 [INFO]: GET /health - 200 - 5ms - ::1
2025-07-08 10:52:30.695 [INFO]: WebSocket user authenticated {"socketId":"CiTl7BdkibVJersNAAAH","userId":3,"username":"45654"}
2025-07-08 10:52:30.696 [INFO]: WebSocket connected {"socketId":"CiTl7BdkibVJersNAAAH","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:52:30.764 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:52:30.768 [INFO]: ::1 - - [08/Jul/2025:02:52:30 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:52:30.768 [INFO]: GET /api/users/online - 304 - 6ms - ::1
2025-07-08 10:52:31.298 [INFO]: ::1 - - [08/Jul/2025:02:52:31 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:52:31.299 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:52:31.331 [INFO]: WebSocket user authenticated {"socketId":"XwmcOr_br6aIE6FEAAAK","userId":3,"username":"45654"}
2025-07-08 10:52:31.332 [INFO]: WebSocket connected {"socketId":"XwmcOr_br6aIE6FEAAAK","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:52:31.353 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:52:31.355 [INFO]: ::1 - - [08/Jul/2025:02:52:31 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:52:31.355 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 10:52:32.106 [INFO]: WebSocket disconnected {"socketId":"CiTl7BdkibVJersNAAAH","userId":"3","type":"websocket","username":"45654","reason":"transport close","details":{},"duration":1410,"recovered":false}
2025-07-08 10:52:41.268 [INFO]: ::1 - - [08/Jul/2025:02:52:41 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:52:41.268 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 10:52:41.278 [INFO]: WebSocket user authenticated {"socketId":"VF3UXVvLUCtLnSQ4AAAN","userId":3,"username":"45654"}
2025-07-08 10:52:41.278 [INFO]: WebSocket connected {"socketId":"VF3UXVvLUCtLnSQ4AAAN","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:52:41.295 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:52:41.297 [INFO]: ::1 - - [08/Jul/2025:02:52:41 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:52:41.297 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 10:52:44.689 [INFO]: ::1 - - [08/Jul/2025:02:52:44 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:52:44.690 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 10:52:44.698 [INFO]: WebSocket user authenticated {"socketId":"Ud3KJGGzhEyn7YD_AAAQ","userId":3,"username":"45654"}
2025-07-08 10:52:44.699 [INFO]: WebSocket connected {"socketId":"Ud3KJGGzhEyn7YD_AAAQ","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:52:44.710 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:52:44.711 [INFO]: ::1 - - [08/Jul/2025:02:52:44 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:52:44.712 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 10:53:12.879 [INFO]: Client initiated disconnect
2025-07-08 10:53:12.880 [INFO]: WebSocket disconnected {"socketId":"Ud3KJGGzhEyn7YD_AAAQ","userId":"3","type":"websocket","username":"45654","reason":"client namespace disconnect","details":{},"duration":28180,"recovered":false}
2025-07-08 10:53:14.252 [INFO]: WebSocket user authenticated {"socketId":"ohaK8SpaMYtz2fxNAAAT","userId":3,"username":"45654"}
2025-07-08 10:53:14.252 [INFO]: WebSocket connected {"socketId":"ohaK8SpaMYtz2fxNAAAT","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:54:05.106 [INFO]: ::1 - - [08/Jul/2025:02:54:05 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:54:05.107 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:54:05.118 [INFO]: WebSocket user authenticated {"socketId":"go_1GI4gZFV3-wqjAAAW","userId":3,"username":"45654"}
2025-07-08 10:54:05.118 [INFO]: WebSocket connected {"socketId":"go_1GI4gZFV3-wqjAAAW","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:54:05.133 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:54:05.134 [INFO]: ::1 - - [08/Jul/2025:02:54:05 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:54:05.135 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 10:56:55.079 [INFO]: WebSocket server initialized
2025-07-08 10:56:55.082 [INFO]: WebSocket server initialized
2025-07-08 10:56:55.096 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:56:55.103 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:56:55.105 [INFO]: Database tables created successfully
2025-07-08 10:56:55.107 [INFO]: Database initialized successfully
2025-07-08 10:56:55.107 [INFO]: Database initialized successfully
2025-07-08 10:56:55.130 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:56:55.130 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:56:55.131 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:56:55.137 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:56:55.137 [INFO]: Environment: development
2025-07-08 10:56:55.137 [INFO]: WebSocket server is ready
2025-07-08 10:56:55.137 [INFO]: Database initialized and ready
2025-07-08 10:56:57.729 [INFO]: ::1 - - [08/Jul/2025:02:56:57 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:56:57.729 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 10:56:57.749 [INFO]: WebSocket user authenticated {"socketId":"NxpvvLgjniiAhrafAAAB","userId":3,"username":"45654"}
2025-07-08 10:56:57.752 [INFO]: WebSocket connected {"socketId":"NxpvvLgjniiAhrafAAAB","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:56:57.776 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:56:57.781 [INFO]: ::1 - - [08/Jul/2025:02:56:57 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:56:57.782 [INFO]: GET /api/users/online - 304 - 9ms - ::1
2025-07-08 10:57:00.745 [INFO]: ::1 - - [08/Jul/2025:02:57:00 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:57:00.745 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:57:00.757 [INFO]: WebSocket user authenticated {"socketId":"zYQMIvycSUoqxu-OAAAE","userId":3,"username":"45654"}
2025-07-08 10:57:00.757 [INFO]: WebSocket connected {"socketId":"zYQMIvycSUoqxu-OAAAE","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:57:00.766 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:57:00.768 [INFO]: ::1 - - [08/Jul/2025:02:57:00 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:57:00.769 [INFO]: GET /api/users/online - 304 - 6ms - ::1
2025-07-08 10:57:16.705 [INFO]: Client initiated disconnect
2025-07-08 10:57:16.706 [INFO]: WebSocket disconnected {"socketId":"zYQMIvycSUoqxu-OAAAE","userId":"3","type":"websocket","username":"45654","reason":"client namespace disconnect","details":{},"duration":15948,"recovered":false}
2025-07-08 10:57:21.156 [INFO]: WebSocket user authenticated {"socketId":"lEVMIAJtIvroLntRAAAH","userId":3,"username":"45654"}
2025-07-08 10:57:21.157 [INFO]: WebSocket connected {"socketId":"lEVMIAJtIvroLntRAAAH","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:57:44.313 [INFO]: Client initiated disconnect
2025-07-08 10:57:44.313 [INFO]: WebSocket disconnected {"socketId":"lEVMIAJtIvroLntRAAAH","userId":"3","type":"websocket","username":"45654","reason":"client namespace disconnect","details":{},"duration":23157,"recovered":false}
2025-07-08 10:57:45.743 [INFO]: WebSocket user authenticated {"socketId":"KtD-qZ0HC9WZvm6MAAAK","userId":3,"username":"45654"}
2025-07-08 10:57:45.744 [INFO]: WebSocket connected {"socketId":"KtD-qZ0HC9WZvm6MAAAK","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:57:48.625 [INFO]: Client initiated disconnect
2025-07-08 10:57:48.626 [INFO]: WebSocket disconnected {"socketId":"KtD-qZ0HC9WZvm6MAAAK","userId":"3","type":"websocket","username":"45654","reason":"client namespace disconnect","details":{},"duration":2882,"recovered":false}
2025-07-08 10:57:52.866 [INFO]: WebSocket user authenticated {"socketId":"UciYSU6ufgkb9LQqAAAN","userId":3,"username":"45654"}
2025-07-08 10:57:52.867 [INFO]: WebSocket connected {"socketId":"UciYSU6ufgkb9LQqAAAN","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 11:00:59.137 [INFO]: WebSocket server initialized
2025-07-08 11:00:59.138 [INFO]: WebSocket server initialized
2025-07-08 11:00:59.149 [INFO]: 🚀 启动开发服务器...
2025-07-08 11:00:59.154 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 11:00:59.156 [INFO]: Database tables created successfully
2025-07-08 11:00:59.157 [INFO]: Database initialized successfully
2025-07-08 11:00:59.158 [INFO]: Database initialized successfully
2025-07-08 11:00:59.179 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 11:00:59.180 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 11:00:59.180 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 11:00:59.191 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 11:00:59.191 [INFO]: 🛑 正在关闭服务器...
2025-07-08 11:00:59.192 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 11:00:59.192 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 11:00:59.193 [INFO]: HTTP server closed
2025-07-08 11:00:59.194 [INFO]: WebSocket server shutdown completed
2025-07-08 11:00:59.195 [INFO]: WebSocket connections closed
2025-07-08 11:01:28.469 [INFO]: WebSocket server initialized
2025-07-08 11:01:28.470 [INFO]: WebSocket server initialized
2025-07-08 11:01:28.484 [INFO]: 🚀 启动开发服务器...
2025-07-08 11:01:28.490 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 11:01:28.492 [INFO]: Database tables created successfully
2025-07-08 11:01:28.494 [INFO]: Database initialized successfully
2025-07-08 11:01:28.495 [INFO]: Database initialized successfully
2025-07-08 11:01:28.520 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 11:01:28.521 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 11:01:28.522 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 11:01:28.533 [INFO]: Server is running on http://localhost:3001
2025-07-08 11:01:28.533 [INFO]: Environment: development
2025-07-08 11:01:28.534 [INFO]: WebSocket server is ready
2025-07-08 11:01:28.534 [INFO]: Database initialized and ready
2025-07-08 11:01:29.017 [INFO]: WebSocket user authenticated {"socketId":"77HUYKMNpfTLxLfeAAAB","userId":3,"username":"45654"}
2025-07-08 11:01:29.020 [INFO]: WebSocket connected {"socketId":"77HUYKMNpfTLxLfeAAAB","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 11:01:29.543 [INFO]: WebSocket user authenticated {"socketId":"xSwzUzzmRkMyLkz9AAAE","userId":3,"username":"45654"}
2025-07-08 11:01:29.544 [INFO]: WebSocket connected {"socketId":"xSwzUzzmRkMyLkz9AAAE","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 11:03:11.298 [INFO]: WebSocket server initialized
2025-07-08 11:03:11.299 [INFO]: WebSocket server initialized
2025-07-08 11:03:11.310 [INFO]: 🚀 启动开发服务器...
2025-07-08 11:03:11.318 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 11:03:11.321 [INFO]: Database tables created successfully
2025-07-08 11:03:11.322 [INFO]: Database initialized successfully
2025-07-08 11:03:11.322 [INFO]: Database initialized successfully
2025-07-08 11:03:11.346 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 11:03:11.346 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 11:03:11.347 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 11:03:11.353 [INFO]: Server is running on http://localhost:3001
2025-07-08 11:03:11.353 [INFO]: Environment: development
2025-07-08 11:03:11.353 [INFO]: WebSocket server is ready
2025-07-08 11:03:11.353 [INFO]: Database initialized and ready
2025-07-08 11:03:11.901 [INFO]: WebSocket user authenticated {"socketId":"nyCBaGBbNzzPcKeLAAAB","userId":3,"username":"45654"}
2025-07-08 11:03:11.904 [INFO]: WebSocket connected {"socketId":"nyCBaGBbNzzPcKeLAAAB","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 11:03:11.990 [INFO]: WebSocket disconnected {"socketId":"nyCBaGBbNzzPcKeLAAAB","userId":"3","type":"websocket","username":"45654","reason":"transport close","details":{},"duration":87,"recovered":false}
2025-07-08 11:03:16.433 [INFO]: WebSocket user authenticated {"socketId":"-5xBqyge246HiEseAAAE","userId":3,"username":"45654"}
2025-07-08 11:03:16.436 [INFO]: WebSocket connected {"socketId":"-5xBqyge246HiEseAAAE","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 11:03:36.455 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:03:36.457 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":14324}
2025-07-08 11:03:46.467 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:03:46.468 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":24336}
2025-07-08 11:03:56.476 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:03:56.477 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":34345}
2025-07-08 11:04:06.488 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:04:06.488 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":44356}
2025-07-08 11:04:55.760 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:04:55.761 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":2}
2025-07-08 11:05:05.770 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:05:05.771 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":10011}
2025-07-08 11:05:15.771 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:05:15.771 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":20012}
2025-07-08 11:05:25.780 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:05:25.780 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":30021}
2025-07-08 11:05:29.607 [INFO]: ::1 - - [08/Jul/2025:03:05:29 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:05:29.608 [INFO]: GET /health - 200 - 4ms - ::1
2025-07-08 11:05:29.619 [INFO]: WebSocket user authenticated {"socketId":"tHzJFg-cp01AvCpKAAAH","userId":3,"username":"45654"}
2025-07-08 11:05:29.620 [INFO]: WebSocket connected {"socketId":"tHzJFg-cp01AvCpKAAAH","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 11:05:29.634 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:05:29.637 [INFO]: ::1 - - [08/Jul/2025:03:05:29 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:05:29.637 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:05:32.660 [INFO]: ::1 - - [08/Jul/2025:03:05:32 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:05:32.660 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:05:32.672 [INFO]: WebSocket user authenticated {"socketId":"Ae8BEiBMocXNnrp2AAAK","userId":3,"username":"45654"}
2025-07-08 11:05:32.672 [INFO]: WebSocket connected {"socketId":"Ae8BEiBMocXNnrp2AAAK","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 11:05:32.679 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:05:32.681 [INFO]: ::1 - - [08/Jul/2025:03:05:32 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:05:32.681 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:05:35.794 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:05:35.794 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":33630}
2025-07-08 11:05:36.714 [INFO]: ::1 - - [08/Jul/2025:03:05:36 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:05:36.714 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:05:36.727 [INFO]: WebSocket user authenticated {"socketId":"MWli7gSy5HOUIf12AAAN","userId":3,"username":"45654"}
2025-07-08 11:05:36.728 [INFO]: WebSocket connected {"socketId":"MWli7gSy5HOUIf12AAAN","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 11:05:36.740 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:05:36.742 [INFO]: ::1 - - [08/Jul/2025:03:05:36 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:05:36.742 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:05:45.807 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:05:45.807 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":43643}
2025-07-08 11:05:55.807 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:05:55.808 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":53643}
2025-07-08 11:06:05.810 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:06:05.810 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":63646}
2025-07-08 11:06:15.813 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:06:15.813 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":67611}
2025-07-08 11:06:25.820 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:06:25.820 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":77618}
2025-07-08 11:06:35.821 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:06:35.822 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":87620}
2025-07-08 11:06:45.831 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:06:45.831 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":97629}
2025-07-08 11:06:55.845 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:06:55.846 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":95884}
2025-07-08 11:07:05.857 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:07:05.857 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":105895}
2025-07-08 11:07:15.859 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:07:15.860 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":115898}
2025-07-08 11:07:25.861 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:07:25.862 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":125899}
2025-07-08 11:07:35.874 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:07:35.875 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":131975}
2025-07-08 11:07:45.887 [INFO]: Test message received Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","username":"45654"}
2025-07-08 11:07:45.888 [INFO]: WebSocket test-message Hello from test page! {"socketId":"-5xBqyge246HiEseAAAE","userId":"45654","type":"websocket","responseTime":141988}
2025-07-08 11:07:45.960 [INFO]: 🛑 正在关闭服务器...
2025-07-08 11:07:45.961 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 11:07:46.024 [INFO]: Database connection closed
2025-07-08 11:17:27.056 [INFO]: WebSocket server initialized
2025-07-08 11:17:27.057 [INFO]: WebSocket server initialized
2025-07-08 11:17:27.069 [INFO]: 🚀 启动开发服务器...
2025-07-08 11:17:27.075 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 11:17:27.078 [INFO]: Database tables created successfully
2025-07-08 11:17:27.079 [INFO]: Database initialized successfully
2025-07-08 11:17:27.079 [INFO]: Database initialized successfully
2025-07-08 11:17:27.105 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 11:17:27.106 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 11:17:27.107 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 11:17:27.114 [INFO]: Server is running on http://localhost:3001
2025-07-08 11:17:27.114 [INFO]: Environment: development
2025-07-08 11:17:27.115 [INFO]: WebSocket server is ready
2025-07-08 11:17:27.115 [INFO]: Database initialized and ready
2025-07-08 11:17:35.595 [INFO]: New user created: 6546 (ID: 4)
2025-07-08 11:17:35.602 [INFO]: ::1 - - [08/Jul/2025:03:17:35 +0000] "POST /api/auth/login HTTP/1.1" 200 615 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:17:35.603 [INFO]: POST /api/auth/login - 200 - 48ms - ::1
2025-07-08 11:17:36.662 [INFO]: ::1 - - [08/Jul/2025:03:17:36 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:17:36.662 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 11:17:36.685 [INFO]: WebSocket user authenticated {"socketId":"R5t66fCqqmkT-KwYAAAB","userId":4,"username":"6546"}
2025-07-08 11:17:36.688 [INFO]: WebSocket connected {"socketId":"R5t66fCqqmkT-KwYAAAB","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:17:36.714 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:17:36.716 [INFO]: ::1 - - [08/Jul/2025:03:17:36 +0000] "GET /api/users/online HTTP/1.1" 200 232 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:17:36.717 [INFO]: GET /api/users/online - 200 - 5ms - ::1
2025-07-08 11:19:14.772 [INFO]: WebSocket disconnected {"socketId":"R5t66fCqqmkT-KwYAAAB","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":98084,"recovered":false}
2025-07-08 11:19:16.069 [INFO]: ::1 - - [08/Jul/2025:03:19:16 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:19:16.069 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:19:16.309 [INFO]: WebSocket user authenticated {"socketId":"UpItjgG79NgPENVIAAAE","userId":4,"username":"6546"}
2025-07-08 11:19:16.310 [INFO]: WebSocket connected {"socketId":"UpItjgG79NgPENVIAAAE","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:19:16.338 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:19:16.341 [INFO]: ::1 - - [08/Jul/2025:03:19:16 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:19:16.341 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:19:26.536 [INFO]: WebSocket disconnected {"socketId":"UpItjgG79NgPENVIAAAE","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":10226,"recovered":false}
2025-07-08 11:19:27.927 [INFO]: ::1 - - [08/Jul/2025:03:19:27 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:19:27.927 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:19:28.045 [INFO]: WebSocket user authenticated {"socketId":"hiv2-TPNc0ccwj0qAAAH","userId":4,"username":"6546"}
2025-07-08 11:19:28.046 [INFO]: WebSocket connected {"socketId":"hiv2-TPNc0ccwj0qAAAH","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:19:28.090 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:19:28.093 [INFO]: ::1 - - [08/Jul/2025:03:19:28 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:19:28.093 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:20:30.376 [INFO]: WebSocket disconnected {"socketId":"hiv2-TPNc0ccwj0qAAAH","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":62329,"recovered":false}
2025-07-08 11:20:31.878 [INFO]: ::1 - - [08/Jul/2025:03:20:31 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:20:31.878 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 11:20:31.915 [INFO]: WebSocket user authenticated {"socketId":"C5qBdR7crnpBmDEXAAAK","userId":4,"username":"6546"}
2025-07-08 11:20:31.915 [INFO]: WebSocket connected {"socketId":"C5qBdR7crnpBmDEXAAAK","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:20:31.937 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:20:31.940 [INFO]: ::1 - - [08/Jul/2025:03:20:31 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:20:31.940 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:20:45.840 [INFO]: WebSocket disconnected {"socketId":"C5qBdR7crnpBmDEXAAAK","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":13925,"recovered":false}
2025-07-08 11:20:47.233 [INFO]: ::1 - - [08/Jul/2025:03:20:47 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:20:47.233 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:20:47.257 [INFO]: WebSocket user authenticated {"socketId":"sOcw5G4CLkziGoaQAAAN","userId":4,"username":"6546"}
2025-07-08 11:20:47.258 [INFO]: WebSocket connected {"socketId":"sOcw5G4CLkziGoaQAAAN","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:20:47.292 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:20:47.294 [INFO]: ::1 - - [08/Jul/2025:03:20:47 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:20:47.294 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:21:01.054 [INFO]: WebSocket disconnected {"socketId":"sOcw5G4CLkziGoaQAAAN","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":13796,"recovered":false}
2025-07-08 11:21:02.181 [INFO]: ::1 - - [08/Jul/2025:03:21:02 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:21:02.182 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:21:02.249 [INFO]: WebSocket user authenticated {"socketId":"rlnTYYSvhRaA_6mJAAAQ","userId":4,"username":"6546"}
2025-07-08 11:21:02.250 [INFO]: WebSocket connected {"socketId":"rlnTYYSvhRaA_6mJAAAQ","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:21:02.280 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:21:02.282 [INFO]: ::1 - - [08/Jul/2025:03:21:02 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:21:02.283 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:21:21.380 [INFO]: WebSocket disconnected {"socketId":"rlnTYYSvhRaA_6mJAAAQ","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":19130,"recovered":false}
2025-07-08 11:21:22.761 [INFO]: ::1 - - [08/Jul/2025:03:21:22 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:21:22.762 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:21:22.967 [INFO]: WebSocket user authenticated {"socketId":"t0x2SIPTKkviVY2ZAAAT","userId":4,"username":"6546"}
2025-07-08 11:21:22.968 [INFO]: WebSocket connected {"socketId":"t0x2SIPTKkviVY2ZAAAT","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:21:23.018 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:21:23.020 [INFO]: ::1 - - [08/Jul/2025:03:21:23 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:21:23.021 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:21:34.531 [INFO]: WebSocket disconnected {"socketId":"t0x2SIPTKkviVY2ZAAAT","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":11562,"recovered":false}
2025-07-08 11:21:35.877 [INFO]: ::1 - - [08/Jul/2025:03:21:35 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:21:35.877 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:21:36.035 [INFO]: WebSocket user authenticated {"socketId":"x_D4EyR-JK5MQiWmAAAW","userId":4,"username":"6546"}
2025-07-08 11:21:36.035 [INFO]: WebSocket connected {"socketId":"x_D4EyR-JK5MQiWmAAAW","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:21:36.092 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:21:36.094 [INFO]: ::1 - - [08/Jul/2025:03:21:36 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:21:36.094 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:21:47.567 [INFO]: WebSocket disconnected {"socketId":"x_D4EyR-JK5MQiWmAAAW","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":11532,"recovered":false}
2025-07-08 11:21:48.427 [INFO]: ::1 - - [08/Jul/2025:03:21:48 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-07-08 11:21:48.428 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:21:48.564 [INFO]: WebSocket user authenticated {"socketId":"Oi_q7FJj1xG_Gi3NAAAZ","userId":4,"username":"6546"}
2025-07-08 11:21:48.565 [INFO]: WebSocket connected {"socketId":"Oi_q7FJj1xG_Gi3NAAAZ","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:21:48.587 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
2025-07-08 11:21:48.589 [INFO]: ::1 - - [08/Jul/2025:03:21:48 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-07-08 11:21:48.589 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:22:03.958 [INFO]: WebSocket disconnected {"socketId":"Oi_q7FJj1xG_Gi3NAAAZ","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":15393,"recovered":false}
2025-07-08 11:22:04.675 [INFO]: ::1 - - [08/Jul/2025:03:22:04 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-07-08 11:22:04.675 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:22:04.711 [INFO]: WebSocket user authenticated {"socketId":"Vk7Xu87Wx72_C7h-AAAc","userId":4,"username":"6546"}
2025-07-08 11:22:04.711 [INFO]: WebSocket connected {"socketId":"Vk7Xu87Wx72_C7h-AAAc","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:22:04.744 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
2025-07-08 11:22:04.746 [INFO]: ::1 - - [08/Jul/2025:03:22:04 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-07-08 11:22:04.746 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:22:18.475 [INFO]: WebSocket disconnected {"socketId":"Vk7Xu87Wx72_C7h-AAAc","userId":"4","type":"websocket","username":"6546","reason":"transport close","details":{},"duration":13763,"recovered":false}
2025-07-08 11:22:19.893 [INFO]: ::1 - - [08/Jul/2025:03:22:19 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:22:19.894 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:22:20.012 [INFO]: WebSocket user authenticated {"socketId":"xhAIVh0MqtlT-UpmAAAf","userId":4,"username":"6546"}
2025-07-08 11:22:20.012 [INFO]: WebSocket connected {"socketId":"xhAIVh0MqtlT-UpmAAAf","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:22:20.033 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:22:20.036 [INFO]: ::1 - - [08/Jul/2025:03:22:20 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:22:20.036 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:23:27.726 [INFO]: ::1 - - [08/Jul/2025:03:23:27 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:23:27.726 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:23:27.736 [INFO]: WebSocket user authenticated {"socketId":"MKaDGaTHcq8F12NrAAAi","userId":4,"username":"6546"}
2025-07-08 11:23:27.736 [INFO]: WebSocket connected {"socketId":"MKaDGaTHcq8F12NrAAAi","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:23:27.750 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:23:27.752 [INFO]: ::1 - - [08/Jul/2025:03:23:27 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:23:27.752 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:23:29.391 [INFO]: ::1 - - [08/Jul/2025:03:23:29 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:23:29.392 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:23:29.403 [INFO]: WebSocket user authenticated {"socketId":"looSoXARDEeLXHRgAAAl","userId":4,"username":"6546"}
2025-07-08 11:23:29.404 [INFO]: WebSocket connected {"socketId":"looSoXARDEeLXHRgAAAl","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:23:29.416 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:23:29.417 [INFO]: ::1 - - [08/Jul/2025:03:23:29 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:23:29.418 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:23:54.980 [INFO]: Client initiated disconnect
2025-07-08 11:23:54.981 [INFO]: WebSocket disconnected {"socketId":"looSoXARDEeLXHRgAAAl","userId":"4","type":"websocket","username":"6546","reason":"client namespace disconnect","details":{},"duration":25577,"recovered":false}
2025-07-08 11:23:56.569 [INFO]: WebSocket user authenticated {"socketId":"TWX3GkMAXCO3GXW8AAAo","userId":4,"username":"6546"}
2025-07-08 11:23:56.570 [INFO]: WebSocket connected {"socketId":"TWX3GkMAXCO3GXW8AAAo","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:26:07.069 [INFO]: ::1 - - [08/Jul/2025:03:26:07 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:26:07.069 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:26:07.079 [INFO]: WebSocket user authenticated {"socketId":"pmLeMXqpewwKORsWAAAr","userId":4,"username":"6546"}
2025-07-08 11:26:07.080 [INFO]: WebSocket connected {"socketId":"pmLeMXqpewwKORsWAAAr","userId":"4","type":"websocket","username":"6546","displayName":"6546"}
2025-07-08 11:26:07.095 [INFO]: User authenticated {"userId":4,"username":"6546","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:26:07.097 [INFO]: ::1 - - [08/Jul/2025:03:26:07 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:26:07.097 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:26:16.829 [INFO]: New user created: 64645 (ID: 5)
2025-07-08 11:26:16.832 [INFO]: ::1 - - [08/Jul/2025:03:26:16 +0000] "POST /api/auth/login HTTP/1.1" 200 617 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:26:16.832 [INFO]: POST /api/auth/login - 200 - 4ms - ::1
2025-07-08 11:26:17.904 [INFO]: ::1 - - [08/Jul/2025:03:26:17 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:26:17.905 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:26:17.919 [INFO]: WebSocket user authenticated {"socketId":"HcZ9NiC9VuZ0KScRAAAu","userId":5,"username":"64645"}
2025-07-08 11:26:17.920 [INFO]: WebSocket connected {"socketId":"HcZ9NiC9VuZ0KScRAAAu","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:26:17.937 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:26:17.940 [INFO]: ::1 - - [08/Jul/2025:03:26:17 +0000] "GET /api/users/online HTTP/1.1" 200 277 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:26:17.940 [INFO]: GET /api/users/online - 200 - 5ms - ::1
2025-07-08 11:27:27.217 [WARN]: Connection timeout {"socketId":"xhAIVh0MqtlT-UpmAAAf","userId":4,"username":"6546","timeSinceLastActivity":307205}
2025-07-08 11:27:27.218 [INFO]: Server initiated disconnect
2025-07-08 11:27:27.219 [INFO]: WebSocket disconnected {"socketId":"xhAIVh0MqtlT-UpmAAAf","userId":"4","type":"websocket","username":"6546","reason":"server namespace disconnect","details":{},"duration":307206,"recovered":false}
2025-07-08 11:27:30.232 [INFO]: WebSocket user authenticated {"socketId":"CuhMBU7-3MR2Ze9LAAAx","userId":5,"username":"64645"}
2025-07-08 11:27:30.233 [INFO]: WebSocket connected {"socketId":"CuhMBU7-3MR2Ze9LAAAx","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:28:57.231 [WARN]: Connection timeout {"socketId":"MKaDGaTHcq8F12NrAAAi","userId":4,"username":"6546","timeSinceLastActivity":329494}
2025-07-08 11:28:57.231 [INFO]: Server initiated disconnect
2025-07-08 11:28:57.232 [INFO]: WebSocket disconnected {"socketId":"MKaDGaTHcq8F12NrAAAi","userId":"4","type":"websocket","username":"6546","reason":"server namespace disconnect","details":{},"duration":329495,"recovered":false}
2025-07-08 11:28:57.232 [WARN]: Connection timeout {"socketId":"TWX3GkMAXCO3GXW8AAAo","userId":4,"username":"6546","timeSinceLastActivity":300660}
2025-07-08 11:28:57.232 [INFO]: Server initiated disconnect
2025-07-08 11:28:57.233 [INFO]: WebSocket disconnected {"socketId":"TWX3GkMAXCO3GXW8AAAo","userId":"4","type":"websocket","username":"6546","reason":"server namespace disconnect","details":{},"duration":300662,"recovered":false}
2025-07-08 11:29:00.249 [INFO]: WebSocket user authenticated {"socketId":"YFAkFAtpE19GpA2yAAA0","userId":5,"username":"64645"}
2025-07-08 11:29:00.249 [INFO]: WebSocket connected {"socketId":"YFAkFAtpE19GpA2yAAA0","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:29:03.252 [INFO]: WebSocket user authenticated {"socketId":"f7AUKIaTxqbBpbNfAAA3","userId":5,"username":"64645"}
2025-07-08 11:29:03.253 [INFO]: WebSocket connected {"socketId":"f7AUKIaTxqbBpbNfAAA3","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:29:58.124 [INFO]: ::1 - - [08/Jul/2025:03:29:58 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:29:58.124 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:29:58.126 [INFO]: ::1 - - [08/Jul/2025:03:29:58 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:29:58.126 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:29:58.138 [INFO]: WebSocket user authenticated {"socketId":"E7FxEL1AT4esBY2OAAA6","userId":5,"username":"64645"}
2025-07-08 11:29:58.139 [INFO]: WebSocket connected {"socketId":"E7FxEL1AT4esBY2OAAA6","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:29:58.151 [INFO]: WebSocket user authenticated {"socketId":"GyljF2xe5OF179gSAAA9","userId":5,"username":"64645"}
2025-07-08 11:29:58.151 [INFO]: WebSocket connected {"socketId":"GyljF2xe5OF179gSAAA9","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:29:58.156 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:29:58.159 [INFO]: ::1 - - [08/Jul/2025:03:29:58 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:29:58.159 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:29:58.162 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:29:58.163 [INFO]: ::1 - - [08/Jul/2025:03:29:58 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:29:58.163 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:30:56.093 [INFO]: ::1 - - [08/Jul/2025:03:30:56 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:30:56.093 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:30:56.095 [INFO]: ::1 - - [08/Jul/2025:03:30:56 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:30:56.096 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:30:56.111 [INFO]: WebSocket user authenticated {"socketId":"W2WsdNRGDS93o9IAAABA","userId":5,"username":"64645"}
2025-07-08 11:30:56.112 [INFO]: WebSocket connected {"socketId":"W2WsdNRGDS93o9IAAABA","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:30:56.123 [INFO]: WebSocket user authenticated {"socketId":"SVt59bPueh_UwXBRAABD","userId":5,"username":"64645"}
2025-07-08 11:30:56.124 [INFO]: WebSocket connected {"socketId":"SVt59bPueh_UwXBRAABD","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:30:56.131 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:30:56.134 [INFO]: ::1 - - [08/Jul/2025:03:30:56 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:30:56.135 [INFO]: GET /api/users/online - 304 - 7ms - ::1
2025-07-08 11:30:56.143 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:30:56.163 [INFO]: ::1 - - [08/Jul/2025:03:30:56 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:30:56.164 [INFO]: GET /api/users/online - 304 - 24ms - ::1
2025-07-08 11:31:27.325 [WARN]: Connection timeout {"socketId":"pmLeMXqpewwKORsWAAAr","userId":4,"username":"6546","timeSinceLastActivity":320245}
2025-07-08 11:31:27.326 [INFO]: Server initiated disconnect
2025-07-08 11:31:27.327 [INFO]: WebSocket disconnected {"socketId":"pmLeMXqpewwKORsWAAAr","userId":"4","type":"websocket","username":"6546","reason":"server namespace disconnect","details":{},"duration":320246,"recovered":false}
2025-07-08 11:31:27.327 [WARN]: Connection timeout {"socketId":"HcZ9NiC9VuZ0KScRAAAu","userId":5,"username":"64645","timeSinceLastActivity":309405}
2025-07-08 11:31:27.328 [INFO]: Server initiated disconnect
2025-07-08 11:31:27.328 [INFO]: WebSocket disconnected {"socketId":"HcZ9NiC9VuZ0KScRAAAu","userId":"5","type":"websocket","username":"64645","reason":"server namespace disconnect","details":{},"duration":309407,"recovered":false}
2025-07-08 11:31:30.338 [INFO]: WebSocket user authenticated {"socketId":"oHZaCXwkDYgJT9naAABG","userId":5,"username":"64645"}
2025-07-08 11:31:30.338 [INFO]: WebSocket connected {"socketId":"oHZaCXwkDYgJT9naAABG","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:31:33.338 [INFO]: WebSocket user authenticated {"socketId":"CaUhN7XXBJZLhXRPAABJ","userId":5,"username":"64645"}
2025-07-08 11:31:33.339 [INFO]: WebSocket connected {"socketId":"CaUhN7XXBJZLhXRPAABJ","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:31:38.035 [INFO]: ::1 - - [08/Jul/2025:03:31:38 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:31:38.035 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:31:38.037 [INFO]: ::1 - - [08/Jul/2025:03:31:38 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:31:38.037 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:31:38.054 [INFO]: WebSocket user authenticated {"socketId":"Xo4hsdSQh41CuVv_AABM","userId":5,"username":"64645"}
2025-07-08 11:31:38.055 [INFO]: WebSocket connected {"socketId":"Xo4hsdSQh41CuVv_AABM","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:31:38.070 [INFO]: WebSocket user authenticated {"socketId":"jldmlAJ0NYCbzOZVAABP","userId":5,"username":"64645"}
2025-07-08 11:31:38.071 [INFO]: WebSocket connected {"socketId":"jldmlAJ0NYCbzOZVAABP","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:31:38.076 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:31:38.077 [INFO]: ::1 - - [08/Jul/2025:03:31:38 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:31:38.077 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:31:38.088 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:31:38.090 [INFO]: ::1 - - [08/Jul/2025:03:31:38 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:31:38.091 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:32:57.355 [WARN]: Connection timeout {"socketId":"CuhMBU7-3MR2Ze9LAAAx","userId":5,"username":"64645","timeSinceLastActivity":327122}
2025-07-08 11:32:57.356 [INFO]: Server initiated disconnect
2025-07-08 11:32:57.358 [INFO]: WebSocket disconnected {"socketId":"CuhMBU7-3MR2Ze9LAAAx","userId":"5","type":"websocket","username":"64645","reason":"server namespace disconnect","details":{},"duration":327123,"recovered":false}
2025-07-08 11:33:00.372 [INFO]: WebSocket user authenticated {"socketId":"9n0uGdw0HCxS3qzxAABS","userId":5,"username":"64645"}
2025-07-08 11:33:00.373 [INFO]: WebSocket connected {"socketId":"9n0uGdw0HCxS3qzxAABS","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:34:26.439 [INFO]: ::1 - - [08/Jul/2025:03:34:26 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:34:26.439 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:34:26.441 [INFO]: ::1 - - [08/Jul/2025:03:34:26 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:34:26.442 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:34:26.460 [INFO]: WebSocket user authenticated {"socketId":"oo0KuFcflP-FNlYmAABV","userId":5,"username":"64645"}
2025-07-08 11:34:26.461 [INFO]: WebSocket connected {"socketId":"oo0KuFcflP-FNlYmAABV","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:34:26.480 [INFO]: WebSocket user authenticated {"socketId":"B3KTV657GL4gdDFyAABY","userId":5,"username":"64645"}
2025-07-08 11:34:26.482 [INFO]: WebSocket connected {"socketId":"B3KTV657GL4gdDFyAABY","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:34:26.489 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:34:26.491 [INFO]: ::1 - - [08/Jul/2025:03:34:26 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:34:26.492 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:34:26.497 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:34:26.503 [INFO]: ::1 - - [08/Jul/2025:03:34:26 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:34:26.503 [INFO]: GET /api/users/online - 304 - 9ms - ::1
2025-07-08 11:34:27.371 [WARN]: Connection timeout {"socketId":"YFAkFAtpE19GpA2yAAA0","userId":5,"username":"64645","timeSinceLastActivity":327122}
2025-07-08 11:34:27.372 [INFO]: Server initiated disconnect
2025-07-08 11:34:27.375 [INFO]: WebSocket disconnected {"socketId":"YFAkFAtpE19GpA2yAAA0","userId":"5","type":"websocket","username":"64645","reason":"server namespace disconnect","details":{},"duration":327123,"recovered":false}
2025-07-08 11:34:27.375 [WARN]: Connection timeout {"socketId":"f7AUKIaTxqbBpbNfAAA3","userId":5,"username":"64645","timeSinceLastActivity":324119}
2025-07-08 11:34:27.376 [INFO]: Server initiated disconnect
2025-07-08 11:34:27.376 [INFO]: WebSocket disconnected {"socketId":"f7AUKIaTxqbBpbNfAAA3","userId":"5","type":"websocket","username":"64645","reason":"server namespace disconnect","details":{},"duration":324124,"recovered":false}
2025-07-08 11:34:30.389 [INFO]: WebSocket user authenticated {"socketId":"omki4gOtwlraybrAAABb","userId":5,"username":"64645"}
2025-07-08 11:34:30.389 [INFO]: WebSocket connected {"socketId":"omki4gOtwlraybrAAABb","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:34:33.399 [INFO]: WebSocket user authenticated {"socketId":"I_IkbLY5kTLKOALdAABe","userId":5,"username":"64645"}
2025-07-08 11:34:33.400 [INFO]: WebSocket connected {"socketId":"I_IkbLY5kTLKOALdAABe","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:34:55.676 [INFO]: WebSocket disconnected {"socketId":"E7FxEL1AT4esBY2OAAA6","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":297537,"recovered":false}
2025-07-08 11:34:55.683 [INFO]: WebSocket disconnected {"socketId":"I_IkbLY5kTLKOALdAABe","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":22283,"recovered":false}
2025-07-08 11:34:55.683 [INFO]: WebSocket disconnected {"socketId":"omki4gOtwlraybrAAABb","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":25294,"recovered":false}
2025-07-08 11:34:55.684 [INFO]: WebSocket disconnected {"socketId":"B3KTV657GL4gdDFyAABY","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":29203,"recovered":false}
2025-07-08 11:34:55.685 [INFO]: WebSocket disconnected {"socketId":"oo0KuFcflP-FNlYmAABV","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":29224,"recovered":false}
2025-07-08 11:34:55.685 [INFO]: WebSocket disconnected {"socketId":"9n0uGdw0HCxS3qzxAABS","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":115313,"recovered":false}
2025-07-08 11:34:55.686 [INFO]: WebSocket disconnected {"socketId":"jldmlAJ0NYCbzOZVAABP","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":197615,"recovered":false}
2025-07-08 11:34:55.686 [INFO]: WebSocket disconnected {"socketId":"Xo4hsdSQh41CuVv_AABM","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":197631,"recovered":false}
2025-07-08 11:34:55.687 [INFO]: WebSocket disconnected {"socketId":"CaUhN7XXBJZLhXRPAABJ","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":202348,"recovered":false}
2025-07-08 11:34:55.687 [INFO]: WebSocket disconnected {"socketId":"oHZaCXwkDYgJT9naAABG","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":205349,"recovered":false}
2025-07-08 11:34:55.688 [INFO]: WebSocket disconnected {"socketId":"SVt59bPueh_UwXBRAABD","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":239564,"recovered":false}
2025-07-08 11:34:55.688 [INFO]: WebSocket disconnected {"socketId":"W2WsdNRGDS93o9IAAABA","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":239577,"recovered":false}
2025-07-08 11:34:55.689 [INFO]: WebSocket disconnected {"socketId":"GyljF2xe5OF179gSAAA9","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":297538,"recovered":false}
2025-07-08 11:34:57.225 [INFO]: ::1 - - [08/Jul/2025:03:34:57 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:34:57.226 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:34:57.393 [INFO]: WebSocket user authenticated {"socketId":"JxtNqCWIPICBUdSqAABh","userId":5,"username":"64645"}
2025-07-08 11:34:57.393 [INFO]: WebSocket connected {"socketId":"JxtNqCWIPICBUdSqAABh","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:34:57.455 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:34:57.457 [INFO]: ::1 - - [08/Jul/2025:03:34:57 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:34:57.457 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:35:11.407 [INFO]: WebSocket disconnected {"socketId":"JxtNqCWIPICBUdSqAABh","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":14014,"recovered":false}
2025-07-08 11:35:12.786 [INFO]: ::1 - - [08/Jul/2025:03:35:12 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:35:12.786 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:35:12.880 [INFO]: WebSocket user authenticated {"socketId":"d05ZSx00yaj9_IA8AABk","userId":5,"username":"64645"}
2025-07-08 11:35:12.881 [INFO]: WebSocket connected {"socketId":"d05ZSx00yaj9_IA8AABk","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:35:55.150 [INFO]: WebSocket disconnected {"socketId":"d05ZSx00yaj9_IA8AABk","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":42269,"recovered":false}
2025-07-08 11:35:56.496 [INFO]: ::1 - - [08/Jul/2025:03:35:56 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:35:56.496 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:35:56.649 [INFO]: WebSocket user authenticated {"socketId":"RNl-0TEK8UJ-gwyCAABn","userId":5,"username":"64645"}
2025-07-08 11:35:56.650 [INFO]: WebSocket connected {"socketId":"RNl-0TEK8UJ-gwyCAABn","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:36:21.660 [INFO]: WebSocket disconnected {"socketId":"RNl-0TEK8UJ-gwyCAABn","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":25009,"recovered":false}
2025-07-08 11:36:23.109 [INFO]: ::1 - - [08/Jul/2025:03:36:23 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:36:23.109 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:36:23.261 [INFO]: WebSocket user authenticated {"socketId":"DeEcUYk4HHKZucwKAABq","userId":5,"username":"64645"}
2025-07-08 11:36:23.262 [INFO]: WebSocket connected {"socketId":"DeEcUYk4HHKZucwKAABq","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:36:23.321 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:36:23.324 [INFO]: ::1 - - [08/Jul/2025:03:36:23 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:36:23.324 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:36:35.275 [INFO]: Client initiated disconnect
2025-07-08 11:36:35.275 [INFO]: WebSocket disconnected {"socketId":"DeEcUYk4HHKZucwKAABq","userId":"5","type":"websocket","username":"64645","reason":"client namespace disconnect","details":{},"duration":12012,"recovered":false}
2025-07-08 11:36:35.943 [INFO]: WebSocket user authenticated {"socketId":"vTpjIcqKEZhP6KIQAABt","userId":5,"username":"64645"}
2025-07-08 11:36:35.943 [INFO]: WebSocket connected {"socketId":"vTpjIcqKEZhP6KIQAABt","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:36:55.969 [INFO]: Test message received Hello from test page! {"socketId":"vTpjIcqKEZhP6KIQAABt","username":"64645"}
2025-07-08 11:36:55.970 [INFO]: WebSocket test-message Hello from test page! {"socketId":"vTpjIcqKEZhP6KIQAABt","userId":"64645","type":"websocket","responseTime":15767}
2025-07-08 11:36:58.020 [INFO]: WebSocket disconnected {"socketId":"vTpjIcqKEZhP6KIQAABt","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":22077,"recovered":false}
2025-07-08 11:37:02.816 [INFO]: ::1 - - [08/Jul/2025:03:37:02 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:37:02.816 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:37:02.841 [INFO]: WebSocket user authenticated {"socketId":"NmUenH1M2DLnPS1JAABw","userId":5,"username":"64645"}
2025-07-08 11:37:02.842 [INFO]: WebSocket connected {"socketId":"NmUenH1M2DLnPS1JAABw","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:37:02.862 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:37:02.863 [INFO]: ::1 - - [08/Jul/2025:03:37:02 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:37:02.864 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:37:11.793 [INFO]: ::1 - - [08/Jul/2025:03:37:11 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:37:11.793 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:37:11.802 [INFO]: WebSocket user authenticated {"socketId":"3Zu3aVwpSuQu1gc_AABz","userId":5,"username":"64645"}
2025-07-08 11:37:11.803 [INFO]: WebSocket connected {"socketId":"3Zu3aVwpSuQu1gc_AABz","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:37:11.818 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:37:11.820 [INFO]: ::1 - - [08/Jul/2025:03:37:11 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:37:11.821 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:37:49.424 [INFO]: ::1 - - [08/Jul/2025:03:37:49 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:37:49.424 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:37:49.444 [INFO]: WebSocket user authenticated {"socketId":"XcSebkASzTVuUJxhAAB2","userId":5,"username":"64645"}
2025-07-08 11:37:49.444 [INFO]: WebSocket connected {"socketId":"XcSebkASzTVuUJxhAAB2","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:37:49.460 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:37:49.463 [INFO]: ::1 - - [08/Jul/2025:03:37:49 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:37:49.463 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:40:06.217 [INFO]: ::1 - - [08/Jul/2025:03:40:06 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:40:06.217 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:40:06.231 [INFO]: WebSocket user authenticated {"socketId":"RNlkgVTnfa6Ygq4cAAB5","userId":5,"username":"64645"}
2025-07-08 11:40:06.232 [INFO]: WebSocket connected {"socketId":"RNlkgVTnfa6Ygq4cAAB5","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:40:06.249 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:40:06.252 [INFO]: ::1 - - [08/Jul/2025:03:40:06 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:40:06.252 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:40:30.753 [INFO]: ::1 - - [08/Jul/2025:03:40:30 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:40:30.753 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:40:30.765 [INFO]: WebSocket user authenticated {"socketId":"SXeVNApropXyWjmdAAB8","userId":5,"username":"64645"}
2025-07-08 11:40:30.765 [INFO]: WebSocket connected {"socketId":"SXeVNApropXyWjmdAAB8","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:40:30.829 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:40:30.831 [INFO]: ::1 - - [08/Jul/2025:03:40:30 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:40:30.831 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:40:47.224 [INFO]: ::1 - - [08/Jul/2025:03:40:47 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:40:47.224 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:40:47.236 [INFO]: WebSocket user authenticated {"socketId":"iLizEuk6U28yAu75AAB_","userId":5,"username":"64645"}
2025-07-08 11:40:47.237 [INFO]: WebSocket connected {"socketId":"iLizEuk6U28yAu75AAB_","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:40:47.250 [INFO]: User authenticated {"userId":5,"username":"64645","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:40:47.252 [INFO]: ::1 - - [08/Jul/2025:03:40:47 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:40:47.252 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:42:27.491 [WARN]: Connection timeout {"socketId":"NmUenH1M2DLnPS1JAABw","userId":5,"username":"64645","timeSinceLastActivity":324648}
2025-07-08 11:42:27.491 [INFO]: Server initiated disconnect
2025-07-08 11:42:27.492 [INFO]: WebSocket disconnected {"socketId":"NmUenH1M2DLnPS1JAABw","userId":"5","type":"websocket","username":"64645","reason":"server namespace disconnect","details":{},"duration":324649,"recovered":false}
2025-07-08 11:42:27.492 [WARN]: Connection timeout {"socketId":"3Zu3aVwpSuQu1gc_AABz","userId":5,"username":"64645","timeSinceLastActivity":315688}
2025-07-08 11:42:27.492 [INFO]: Server initiated disconnect
2025-07-08 11:42:27.492 [INFO]: WebSocket disconnected {"socketId":"3Zu3aVwpSuQu1gc_AABz","userId":"5","type":"websocket","username":"64645","reason":"server namespace disconnect","details":{},"duration":315690,"recovered":false}
2025-07-08 11:42:30.503 [INFO]: WebSocket user authenticated {"socketId":"l4nJ233j87jCN1bSAACC","userId":5,"username":"64645"}
2025-07-08 11:42:30.503 [INFO]: WebSocket connected {"socketId":"l4nJ233j87jCN1bSAACC","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:42:33.508 [INFO]: WebSocket user authenticated {"socketId":"TQ3bHdmAew_GCmnWAACF","userId":5,"username":"64645"}
2025-07-08 11:42:33.509 [INFO]: WebSocket connected {"socketId":"TQ3bHdmAew_GCmnWAACF","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:42:57.492 [WARN]: Connection timeout {"socketId":"XcSebkASzTVuUJxhAAB2","userId":5,"username":"64645","timeSinceLastActivity":308047}
2025-07-08 11:42:57.492 [INFO]: Server initiated disconnect
2025-07-08 11:42:57.493 [INFO]: WebSocket disconnected {"socketId":"XcSebkASzTVuUJxhAAB2","userId":"5","type":"websocket","username":"64645","reason":"server namespace disconnect","details":{},"duration":308048,"recovered":false}
2025-07-08 11:43:00.516 [INFO]: WebSocket user authenticated {"socketId":"X1cquC8vTe3QvT8RAACI","userId":5,"username":"64645"}
2025-07-08 11:43:00.516 [INFO]: WebSocket connected {"socketId":"X1cquC8vTe3QvT8RAACI","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:45:00.807 [INFO]: 🛑 正在关闭服务器...
2025-07-08 11:45:00.808 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 11:45:00.885 [INFO]: Database connection closed
2025-07-08 11:45:49.848 [INFO]: WebSocket server initialized
2025-07-08 11:45:49.849 [INFO]: WebSocket server initialized
2025-07-08 11:45:49.861 [INFO]: 🚀 启动开发服务器...
2025-07-08 11:45:49.868 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 11:45:49.871 [INFO]: Database tables created successfully
2025-07-08 11:45:49.872 [INFO]: Database initialized successfully
2025-07-08 11:45:49.874 [INFO]: Database initialized successfully
2025-07-08 11:45:49.898 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 11:45:49.898 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 11:45:49.899 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 11:45:49.904 [INFO]: Server is running on http://localhost:3001
2025-07-08 11:45:49.904 [INFO]: Environment: development
2025-07-08 11:45:49.904 [INFO]: WebSocket server is ready
2025-07-08 11:45:49.904 [INFO]: Database initialized and ready
2025-07-08 11:45:50.250 [INFO]: WebSocket user authenticated {"socketId":"6A0ZwPo8pPeMqDNBAAAB","userId":5,"username":"64645"}
2025-07-08 11:45:50.252 [INFO]: WebSocket connected {"socketId":"6A0ZwPo8pPeMqDNBAAAB","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:45:50.262 [INFO]: WebSocket user authenticated {"socketId":"fMJ3niN1X3j_DWkJAAAE","userId":5,"username":"64645"}
2025-07-08 11:45:50.263 [INFO]: WebSocket connected {"socketId":"fMJ3niN1X3j_DWkJAAAE","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:45:50.287 [INFO]: WebSocket user authenticated {"socketId":"jDgh7nTSbp2oQLkLAAAH","userId":5,"username":"64645"}
2025-07-08 11:45:50.288 [INFO]: WebSocket connected {"socketId":"jDgh7nTSbp2oQLkLAAAH","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:45:50.303 [INFO]: WebSocket user authenticated {"socketId":"gGLXjjVgP0AaBJw_AAAK","userId":5,"username":"64645"}
2025-07-08 11:45:50.304 [INFO]: WebSocket connected {"socketId":"gGLXjjVgP0AaBJw_AAAK","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:45:51.121 [INFO]: WebSocket disconnected {"socketId":"6A0ZwPo8pPeMqDNBAAAB","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":869,"recovered":false}
2025-07-08 11:45:51.125 [INFO]: WebSocket disconnected {"socketId":"gGLXjjVgP0AaBJw_AAAK","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":821,"recovered":false}
2025-07-08 11:45:51.126 [INFO]: WebSocket disconnected {"socketId":"jDgh7nTSbp2oQLkLAAAH","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":838,"recovered":false}
2025-07-08 11:45:51.127 [INFO]: WebSocket disconnected {"socketId":"fMJ3niN1X3j_DWkJAAAE","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":865,"recovered":false}
2025-07-08 11:45:53.891 [INFO]: WebSocket user authenticated {"socketId":"NepSf_-wBJGROJEUAAAN","userId":5,"username":"64645"}
2025-07-08 11:45:53.892 [INFO]: WebSocket connected {"socketId":"NepSf_-wBJGROJEUAAAN","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:45:53.918 [WARN]: Client Error: Route /api/api/users/online not found {"error":{"code":"NOT_FOUND","message":"Route /api/api/users/online not found","stack":"Error: Route /api/api/users/online not found\n    at createError (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:29:27)\n    at notFoundHandler (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:38:28)\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at G:\\codingProject\\talking\\backend\\src\\app.ts:184:7\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)"},"request":{"method":"GET","url":"/api/api/users/online","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}}
2025-07-08 11:45:53.926 [INFO]: ::1 - - [08/Jul/2025:03:45:53 +0000] "GET /api/api/users/online HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:45:53.927 [INFO]: GET /api/api/users/online - 404 - 12ms - ::1
2025-07-08 11:46:01.622 [INFO]: WebSocket user authenticated {"socketId":"SxyJ6BUqbFGTC6d5AAAQ","userId":5,"username":"64645"}
2025-07-08 11:46:01.624 [INFO]: WebSocket connected {"socketId":"SxyJ6BUqbFGTC6d5AAAQ","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:46:01.651 [WARN]: Client Error: Route /api/api/users/online not found {"error":{"code":"NOT_FOUND","message":"Route /api/api/users/online not found","stack":"Error: Route /api/api/users/online not found\n    at createError (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:29:27)\n    at notFoundHandler (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:38:28)\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at G:\\codingProject\\talking\\backend\\src\\app.ts:184:7\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)"},"request":{"method":"GET","url":"/api/api/users/online","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}}
2025-07-08 11:46:01.654 [INFO]: ::1 - - [08/Jul/2025:03:46:01 +0000] "GET /api/api/users/online HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:01.655 [INFO]: GET /api/api/users/online - 404 - 6ms - ::1
2025-07-08 11:46:03.076 [INFO]: WebSocket user authenticated {"socketId":"cDNKJYmlR26Pa3NjAAAT","userId":5,"username":"64645"}
2025-07-08 11:46:03.076 [INFO]: WebSocket connected {"socketId":"cDNKJYmlR26Pa3NjAAAT","userId":"5","type":"websocket","username":"64645","displayName":"64645"}
2025-07-08 11:46:03.082 [WARN]: Client Error: Route /api/api/users/online not found {"error":{"code":"NOT_FOUND","message":"Route /api/api/users/online not found","stack":"Error: Route /api/api/users/online not found\n    at createError (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:29:27)\n    at notFoundHandler (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:38:28)\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at G:\\codingProject\\talking\\backend\\src\\app.ts:184:7\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)"},"request":{"method":"GET","url":"/api/api/users/online","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}}
2025-07-08 11:46:03.086 [INFO]: ::1 - - [08/Jul/2025:03:46:03 +0000] "GET /api/api/users/online HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:03.086 [INFO]: GET /api/api/users/online - 404 - 4ms - ::1
2025-07-08 11:46:12.883 [INFO]: WebSocket disconnected {"socketId":"NepSf_-wBJGROJEUAAAN","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":18992,"recovered":false}
2025-07-08 11:46:12.885 [INFO]: WebSocket disconnected {"socketId":"cDNKJYmlR26Pa3NjAAAT","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":9809,"recovered":false}
2025-07-08 11:46:12.885 [INFO]: WebSocket disconnected {"socketId":"SxyJ6BUqbFGTC6d5AAAQ","userId":"5","type":"websocket","username":"64645","reason":"transport close","details":{},"duration":11262,"recovered":false}
2025-07-08 11:46:20.150 [INFO]: New user created: 5456 (ID: 6)
2025-07-08 11:46:20.154 [INFO]: ::1 - - [08/Jul/2025:03:46:20 +0000] "POST /api/auth/login HTTP/1.1" 200 615 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:20.154 [INFO]: POST /api/auth/login - 200 - 41ms - ::1
2025-07-08 11:46:21.210 [INFO]: WebSocket user authenticated {"socketId":"ccoARZtNO_y1SCwZAAAW","userId":6,"username":"5456"}
2025-07-08 11:46:21.211 [INFO]: WebSocket connected {"socketId":"ccoARZtNO_y1SCwZAAAW","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:46:21.228 [WARN]: Client Error: Route /api/api/users/online not found {"error":{"code":"NOT_FOUND","message":"Route /api/api/users/online not found","stack":"Error: Route /api/api/users/online not found\n    at createError (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:29:27)\n    at notFoundHandler (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:38:28)\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at G:\\codingProject\\talking\\backend\\src\\app.ts:184:7\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)"},"request":{"method":"GET","url":"/api/api/users/online","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}}
2025-07-08 11:46:21.231 [INFO]: ::1 - - [08/Jul/2025:03:46:21 +0000] "GET /api/api/users/online HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:21.231 [INFO]: GET /api/api/users/online - 404 - 4ms - ::1
2025-07-08 11:46:25.692 [INFO]: WebSocket user authenticated {"socketId":"sOJFWSBufJxUDONEAAAZ","userId":6,"username":"5456"}
2025-07-08 11:46:25.692 [INFO]: WebSocket connected {"socketId":"sOJFWSBufJxUDONEAAAZ","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:46:25.698 [WARN]: Client Error: Route /api/api/users/online not found {"error":{"code":"NOT_FOUND","message":"Route /api/api/users/online not found","stack":"Error: Route /api/api/users/online not found\n    at createError (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:29:27)\n    at notFoundHandler (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:38:28)\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at G:\\codingProject\\talking\\backend\\src\\app.ts:184:7\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)"},"request":{"method":"GET","url":"/api/api/users/online","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}}
2025-07-08 11:46:25.700 [INFO]: ::1 - - [08/Jul/2025:03:46:25 +0000] "GET /api/api/users/online HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:25.701 [INFO]: GET /api/api/users/online - 404 - 3ms - ::1
2025-07-08 11:46:29.147 [INFO]: WebSocket user authenticated {"socketId":"x0GziBSTbAUV9ay_AAAc","userId":6,"username":"5456"}
2025-07-08 11:46:29.148 [INFO]: WebSocket connected {"socketId":"x0GziBSTbAUV9ay_AAAc","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:46:29.163 [WARN]: Client Error: Route /api/api/users/online not found {"error":{"code":"NOT_FOUND","message":"Route /api/api/users/online not found","stack":"Error: Route /api/api/users/online not found\n    at createError (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:29:27)\n    at notFoundHandler (G:\\codingProject\\talking\\backend\\src\\middleware\\errorHandler.ts:38:28)\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at G:\\codingProject\\talking\\backend\\src\\app.ts:184:7\n    at Layer.handle [as handle_request] (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (G:\\codingProject\\talking\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)"},"request":{"method":"GET","url":"/api/api/users/online","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}}
2025-07-08 11:46:29.165 [INFO]: ::1 - - [08/Jul/2025:03:46:29 +0000] "GET /api/api/users/online HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:29.165 [INFO]: GET /api/api/users/online - 404 - 3ms - ::1
2025-07-08 11:46:55.026 [INFO]: Client initiated disconnect
2025-07-08 11:46:55.027 [INFO]: WebSocket disconnected {"socketId":"x0GziBSTbAUV9ay_AAAc","userId":"6","type":"websocket","username":"5456","reason":"client namespace disconnect","details":{},"duration":25877,"recovered":false}
2025-07-08 11:46:55.042 [INFO]: ::1 - - [08/Jul/2025:03:46:55 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:55.043 [INFO]: GET /health - 200 - 4ms - ::1
2025-07-08 11:46:55.058 [INFO]: WebSocket user authenticated {"socketId":"bfA9vIEChWCaQsXnAAAf","userId":6,"username":"5456"}
2025-07-08 11:46:55.059 [INFO]: WebSocket connected {"socketId":"bfA9vIEChWCaQsXnAAAf","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:46:55.073 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:46:55.076 [INFO]: ::1 - - [08/Jul/2025:03:46:55 +0000] "GET /api/users/online HTTP/1.1" 200 323 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:55.076 [INFO]: GET /api/users/online - 200 - 5ms - ::1
2025-07-08 11:46:59.586 [INFO]: ::1 - - [08/Jul/2025:03:46:59 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:59.587 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 11:46:59.608 [INFO]: WebSocket user authenticated {"socketId":"63iql2R9MJUhceIMAAAi","userId":6,"username":"5456"}
2025-07-08 11:46:59.609 [INFO]: WebSocket connected {"socketId":"63iql2R9MJUhceIMAAAi","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:46:59.618 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:46:59.620 [INFO]: ::1 - - [08/Jul/2025:03:46:59 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:46:59.620 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:47:01.901 [INFO]: Client initiated disconnect
2025-07-08 11:47:01.902 [INFO]: WebSocket disconnected {"socketId":"63iql2R9MJUhceIMAAAi","userId":"6","type":"websocket","username":"5456","reason":"client namespace disconnect","details":{},"duration":2292,"recovered":false}
2025-07-08 11:47:07.948 [INFO]: WebSocket user authenticated {"socketId":"WBTltFwGzyV-Shu6AAAl","userId":6,"username":"5456"}
2025-07-08 11:47:07.949 [INFO]: WebSocket connected {"socketId":"WBTltFwGzyV-Shu6AAAl","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:47:13.120 [INFO]: ::1 - - [08/Jul/2025:03:47:13 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:47:13.120 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:47:13.132 [INFO]: WebSocket user authenticated {"socketId":"soypxgITd7nZ5Z8SAAAo","userId":6,"username":"5456"}
2025-07-08 11:47:13.133 [INFO]: WebSocket connected {"socketId":"soypxgITd7nZ5Z8SAAAo","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:47:13.144 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:47:13.147 [INFO]: ::1 - - [08/Jul/2025:03:47:13 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:47:13.147 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:48:33.746 [INFO]: WebSocket disconnected {"socketId":"ccoARZtNO_y1SCwZAAAW","userId":"6","type":"websocket","username":"5456","reason":"transport close","details":{},"duration":132535,"recovered":false}
2025-07-08 11:48:33.750 [INFO]: WebSocket disconnected {"socketId":"soypxgITd7nZ5Z8SAAAo","userId":"6","type":"websocket","username":"5456","reason":"transport close","details":{},"duration":80618,"recovered":false}
2025-07-08 11:48:33.751 [INFO]: WebSocket disconnected {"socketId":"WBTltFwGzyV-Shu6AAAl","userId":"6","type":"websocket","username":"5456","reason":"transport close","details":{},"duration":85802,"recovered":false}
2025-07-08 11:48:33.752 [INFO]: WebSocket disconnected {"socketId":"bfA9vIEChWCaQsXnAAAf","userId":"6","type":"websocket","username":"5456","reason":"transport close","details":{},"duration":98693,"recovered":false}
2025-07-08 11:48:33.752 [INFO]: WebSocket disconnected {"socketId":"sOJFWSBufJxUDONEAAAZ","userId":"6","type":"websocket","username":"5456","reason":"transport close","details":{},"duration":128060,"recovered":false}
2025-07-08 11:48:35.164 [INFO]: ::1 - - [08/Jul/2025:03:48:35 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:48:35.164 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:48:35.215 [INFO]: WebSocket user authenticated {"socketId":"xPZdzdNG1uANS7U6AAAr","userId":6,"username":"5456"}
2025-07-08 11:48:35.216 [INFO]: WebSocket connected {"socketId":"xPZdzdNG1uANS7U6AAAr","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:48:35.237 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:48:35.239 [INFO]: ::1 - - [08/Jul/2025:03:48:35 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:48:35.239 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:48:56.065 [INFO]: WebSocket disconnected {"socketId":"xPZdzdNG1uANS7U6AAAr","userId":"6","type":"websocket","username":"5456","reason":"transport close","details":{},"duration":20850,"recovered":false}
2025-07-08 11:48:57.378 [INFO]: ::1 - - [08/Jul/2025:03:48:57 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:48:57.378 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:48:57.457 [INFO]: WebSocket user authenticated {"socketId":"bGczreRXBfru7c4vAAAu","userId":6,"username":"5456"}
2025-07-08 11:48:57.458 [INFO]: WebSocket connected {"socketId":"bGczreRXBfru7c4vAAAu","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:48:57.511 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:48:57.517 [INFO]: ::1 - - [08/Jul/2025:03:48:57 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:48:57.518 [INFO]: GET /api/users/online - 304 - 10ms - ::1
2025-07-08 11:49:38.438 [INFO]: 🛑 正在关闭服务器...
2025-07-08 11:49:38.439 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 11:49:38.498 [INFO]: Database connection closed
2025-07-08 11:50:07.022 [INFO]: WebSocket server initialized
2025-07-08 11:50:07.023 [INFO]: WebSocket server initialized
2025-07-08 11:50:07.036 [INFO]: 🚀 启动开发服务器...
2025-07-08 11:50:07.044 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 11:50:07.046 [INFO]: Database tables created successfully
2025-07-08 11:50:07.047 [INFO]: Database initialized successfully
2025-07-08 11:50:07.048 [INFO]: Database initialized successfully
2025-07-08 11:50:07.077 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 11:50:07.078 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 11:50:07.079 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 11:50:07.087 [INFO]: Server is running on http://localhost:3001
2025-07-08 11:50:07.088 [INFO]: Environment: development
2025-07-08 11:50:07.088 [INFO]: WebSocket server is ready
2025-07-08 11:50:07.088 [INFO]: Database initialized and ready
2025-07-08 11:50:10.733 [INFO]: ::1 - - [08/Jul/2025:03:50:10 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:50:10.733 [INFO]: GET /health - 200 - 4ms - ::1
2025-07-08 11:50:10.764 [INFO]: WebSocket user authenticated {"socketId":"JaQCthwEBXFazQEzAAAB","userId":6,"username":"5456"}
2025-07-08 11:50:10.766 [INFO]: WebSocket connected {"socketId":"JaQCthwEBXFazQEzAAAB","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:50:10.831 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:50:10.835 [INFO]: ::1 - - [08/Jul/2025:03:50:10 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:50:10.835 [INFO]: GET /api/users/online - 304 - 7ms - ::1
2025-07-08 11:50:27.450 [INFO]: 🛑 正在关闭服务器...
2025-07-08 11:50:27.450 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 11:50:27.452 [INFO]: Database connection closed
2025-07-08 11:53:05.514 [INFO]: WebSocket server initialized
2025-07-08 11:53:05.515 [INFO]: WebSocket server initialized
2025-07-08 11:53:05.528 [INFO]: 🚀 启动开发服务器...
2025-07-08 11:53:05.535 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 11:53:05.537 [INFO]: Database tables created successfully
2025-07-08 11:53:05.538 [INFO]: Database initialized successfully
2025-07-08 11:53:05.539 [INFO]: Database initialized successfully
2025-07-08 11:53:05.562 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 11:53:05.562 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 11:53:05.563 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 11:53:05.568 [INFO]: Server is running on http://localhost:3001
2025-07-08 11:53:05.569 [INFO]: Environment: development
2025-07-08 11:53:05.569 [INFO]: WebSocket server is ready
2025-07-08 11:53:05.569 [INFO]: Database initialized and ready
2025-07-08 11:53:08.609 [INFO]: ::1 - - [08/Jul/2025:03:53:08 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:53:08.610 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 11:53:08.636 [INFO]: WebSocket user authenticated {"socketId":"-gEd573Ozn6xn8QsAAAB","userId":6,"username":"5456"}
2025-07-08 11:53:08.637 [INFO]: WebSocket connected {"socketId":"-gEd573Ozn6xn8QsAAAB","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:53:08.656 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:53:08.659 [INFO]: ::1 - - [08/Jul/2025:03:53:08 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:53:08.661 [INFO]: GET /api/users/online - 304 - 7ms - ::1
2025-07-08 11:53:22.735 [INFO]: ::1 - - [08/Jul/2025:03:53:22 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:53:22.736 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:53:22.752 [INFO]: WebSocket user authenticated {"socketId":"txhS-Y34qRCcY0eBAAAE","userId":6,"username":"5456"}
2025-07-08 11:53:22.753 [INFO]: WebSocket connected {"socketId":"txhS-Y34qRCcY0eBAAAE","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:53:22.765 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:53:22.767 [INFO]: ::1 - - [08/Jul/2025:03:53:22 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:53:22.768 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:55:40.147 [INFO]: ::1 - - [08/Jul/2025:03:55:40 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:55:40.147 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:55:40.167 [INFO]: WebSocket user authenticated {"socketId":"jBazuQA7DjDpaqwjAAAH","userId":6,"username":"5456"}
2025-07-08 11:55:40.168 [INFO]: WebSocket connected {"socketId":"jBazuQA7DjDpaqwjAAAH","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:55:40.182 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:55:40.185 [INFO]: ::1 - - [08/Jul/2025:03:55:40 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:55:40.185 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:55:59.680 [INFO]: ::1 - - [08/Jul/2025:03:55:59 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:55:59.681 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:56:12.573 [INFO]: ::1 - - [08/Jul/2025:03:56:12 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:56:12.573 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:56:12.578 [INFO]: Client initiated disconnect
2025-07-08 11:56:12.580 [INFO]: WebSocket disconnected {"socketId":"jBazuQA7DjDpaqwjAAAH","userId":"6","type":"websocket","username":"5456","reason":"client namespace disconnect","details":{},"duration":32410,"recovered":false}
2025-07-08 11:56:43.814 [INFO]: ::1 - - [08/Jul/2025:03:56:43 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:56:43.814 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:56:43.837 [INFO]: WebSocket user authenticated {"socketId":"D5zri4xtRT6Kap5mAAAK","userId":6,"username":"5456"}
2025-07-08 11:56:43.838 [INFO]: WebSocket connected {"socketId":"D5zri4xtRT6Kap5mAAAK","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:56:43.867 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:56:43.869 [INFO]: ::1 - - [08/Jul/2025:03:56:43 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:56:43.869 [INFO]: GET /api/users/online - 304 - 9ms - ::1
2025-07-08 11:57:20.061 [INFO]: ::1 - - [08/Jul/2025:03:57:20 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:57:20.062 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:57:20.076 [INFO]: WebSocket user authenticated {"socketId":"Ys3P_boegpVOBKGyAAAN","userId":6,"username":"5456"}
2025-07-08 11:57:20.077 [INFO]: WebSocket connected {"socketId":"Ys3P_boegpVOBKGyAAAN","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:57:20.091 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:57:20.093 [INFO]: ::1 - - [08/Jul/2025:03:57:20 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:57:20.094 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:57:33.293 [INFO]: ::1 - - [08/Jul/2025:03:57:33 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:57:33.294 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:57:33.311 [INFO]: WebSocket user authenticated {"socketId":"47Nv9FABWN3U57ApAAAQ","userId":6,"username":"5456"}
2025-07-08 11:57:33.312 [INFO]: WebSocket connected {"socketId":"47Nv9FABWN3U57ApAAAQ","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:57:33.327 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:57:33.329 [INFO]: ::1 - - [08/Jul/2025:03:57:33 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:57:33.330 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:57:44.571 [INFO]: ::1 - - [08/Jul/2025:03:57:44 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:57:44.572 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:57:44.641 [INFO]: WebSocket user authenticated {"socketId":"hu22hvaRvlnCGX-iAAAT","userId":6,"username":"5456"}
2025-07-08 11:57:44.642 [INFO]: WebSocket connected {"socketId":"hu22hvaRvlnCGX-iAAAT","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:57:44.670 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:57:44.672 [INFO]: ::1 - - [08/Jul/2025:03:57:44 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:57:44.672 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:57:54.450 [INFO]: ::1 - - [08/Jul/2025:03:57:54 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:57:54.450 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 11:57:54.461 [INFO]: WebSocket user authenticated {"socketId":"FQI1Mn_xmlhNmjgsAAAW","userId":6,"username":"5456"}
2025-07-08 11:57:54.461 [INFO]: WebSocket connected {"socketId":"FQI1Mn_xmlhNmjgsAAAW","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:57:54.473 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:57:54.475 [INFO]: ::1 - - [08/Jul/2025:03:57:54 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:57:54.475 [INFO]: GET /api/users/online - 304 - 4ms - ::1
2025-07-08 11:58:34.245 [INFO]: ::1 - - [08/Jul/2025:03:58:34 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:58:34.245 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:58:34.266 [INFO]: WebSocket user authenticated {"socketId":"gr2nRGQCALwCzer3AAAZ","userId":6,"username":"5456"}
2025-07-08 11:58:34.266 [INFO]: WebSocket connected {"socketId":"gr2nRGQCALwCzer3AAAZ","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:58:34.279 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:58:34.281 [INFO]: ::1 - - [08/Jul/2025:03:58:34 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:58:34.281 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 11:58:35.606 [WARN]: Connection timeout {"socketId":"-gEd573Ozn6xn8QsAAAB","userId":6,"username":"5456","timeSinceLastActivity":326968}
2025-07-08 11:58:35.606 [INFO]: Server initiated disconnect
2025-07-08 11:58:35.607 [INFO]: WebSocket disconnected {"socketId":"-gEd573Ozn6xn8QsAAAB","userId":"6","type":"websocket","username":"5456","reason":"server namespace disconnect","details":{},"duration":326969,"recovered":false}
2025-07-08 11:58:35.608 [WARN]: Connection timeout {"socketId":"txhS-Y34qRCcY0eBAAAE","userId":6,"username":"5456","timeSinceLastActivity":312853}
2025-07-08 11:58:35.608 [INFO]: Server initiated disconnect
2025-07-08 11:58:35.609 [INFO]: WebSocket disconnected {"socketId":"txhS-Y34qRCcY0eBAAAE","userId":"6","type":"websocket","username":"5456","reason":"server namespace disconnect","details":{},"duration":312856,"recovered":false}
2025-07-08 11:58:38.628 [INFO]: WebSocket user authenticated {"socketId":"VCHQjWt6Dz21rY_IAAAc","userId":6,"username":"5456"}
2025-07-08 11:58:38.629 [INFO]: WebSocket connected {"socketId":"VCHQjWt6Dz21rY_IAAAc","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:58:41.621 [INFO]: WebSocket user authenticated {"socketId":"CCtNzx0ryBA3Ma5rAAAf","userId":6,"username":"5456"}
2025-07-08 11:58:41.622 [INFO]: WebSocket connected {"socketId":"CCtNzx0ryBA3Ma5rAAAf","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:58:48.984 [INFO]: ::1 - - [08/Jul/2025:03:58:48 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:58:48.984 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 11:58:48.999 [INFO]: WebSocket user authenticated {"socketId":"rUASZyZiKNFl-ahDAAAi","userId":6,"username":"5456"}
2025-07-08 11:58:49.000 [INFO]: WebSocket connected {"socketId":"rUASZyZiKNFl-ahDAAAi","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:58:49.013 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:58:49.016 [INFO]: ::1 - - [08/Jul/2025:03:58:49 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:58:49.016 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:58:59.978 [INFO]: ::1 - - [08/Jul/2025:03:58:59 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:58:59.978 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 11:58:59.991 [INFO]: WebSocket user authenticated {"socketId":"sOFOqpUjW6pVFPtqAAAl","userId":6,"username":"5456"}
2025-07-08 11:58:59.992 [INFO]: WebSocket connected {"socketId":"sOFOqpUjW6pVFPtqAAAl","userId":"6","type":"websocket","username":"5456","displayName":"5456"}
2025-07-08 11:59:00.005 [INFO]: User authenticated {"userId":6,"username":"5456","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 11:59:00.009 [INFO]: ::1 - - [08/Jul/2025:03:59:00 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 11:59:00.010 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 11:59:07.984 [INFO]: 🛑 正在关闭服务器...
2025-07-08 11:59:07.985 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 11:59:07.986 [INFO]: Database connection closed

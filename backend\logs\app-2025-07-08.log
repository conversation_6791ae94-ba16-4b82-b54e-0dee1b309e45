2025-07-08 08:32:09.488 [INFO]: WebSocket server initialized
2025-07-08 08:32:09.489 [INFO]: WebSocket server initialized
2025-07-08 08:32:09.506 [INFO]: 🚀 启动开发服务器...
2025-07-08 08:32:09.507 [INFO]: 📦 初始化数据库...
2025-07-08 08:32:09.514 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:32:09.517 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:32:09.518 [INFO]: Database tables created successfully
2025-07-08 08:32:09.519 [INFO]: Database initialized successfully
2025-07-08 08:32:09.519 [INFO]: ✅ 数据库初始化完成
2025-07-08 08:32:09.545 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 08:32:09.545 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 08:32:09.546 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 08:32:09.552 [INFO]: Server is running on http://localhost:3001
2025-07-08 08:32:09.553 [INFO]: Environment: development
2025-07-08 08:32:09.553 [INFO]: WebSocket server is ready
2025-07-08 08:32:09.554 [INFO]: Database tables created successfully
2025-07-08 08:32:09.555 [INFO]: Database initialized successfully
2025-07-08 08:32:09.555 [INFO]: Database initialized successfully
2025-07-08 08:32:12.463 [INFO]: 🛑 正在关闭服务器...
2025-07-08 08:32:12.464 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 08:32:12.465 [INFO]: HTTP server closed
2025-07-08 08:32:12.467 [INFO]: WebSocket server shutdown completed
2025-07-08 08:32:12.467 [INFO]: WebSocket connections closed
2025-07-08 08:53:40.443 [INFO]: WebSocket server initialized
2025-07-08 08:53:40.444 [INFO]: WebSocket server initialized
2025-07-08 08:53:40.459 [INFO]: 🚀 启动开发服务器...
2025-07-08 08:53:40.460 [INFO]: 📦 初始化数据库...
2025-07-08 08:53:40.467 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:53:40.470 [INFO]: Database tables created successfully
2025-07-08 08:53:40.471 [INFO]: Database initialized successfully
2025-07-08 08:53:40.472 [INFO]: ✅ 数据库初始化完成
2025-07-08 08:53:40.502 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 08:53:40.503 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 08:53:40.504 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 08:53:40.508 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:53:40.513 [INFO]: Server is running on http://localhost:3001
2025-07-08 08:53:40.514 [INFO]: Environment: development
2025-07-08 08:53:40.514 [INFO]: WebSocket server is ready
2025-07-08 08:53:40.516 [INFO]: Database tables created successfully
2025-07-08 08:53:40.517 [INFO]: Database initialized successfully
2025-07-08 08:53:40.517 [INFO]: Database initialized successfully
2025-07-08 08:53:56.072 [INFO]: ::1 - - [08/Jul/2025:00:53:56 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:53:56.073 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 08:53:56.108 [WARN]: WebSocket authentication failed {"socketId":"9ghcKK1xBx9Fp4ozAAAB","error":"Token verification failed"}
2025-07-08 08:55:53.547 [INFO]: ::1 - - [08/Jul/2025:00:55:53 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:55:53.547 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 08:55:53.573 [WARN]: WebSocket authentication failed {"socketId":"R6Dsz8QbhQV9ZahCAAAD","error":"Token verification failed"}
2025-07-08 08:56:52.216 [INFO]: ::1 - - [08/Jul/2025:00:56:52 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:56:52.217 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:56:52.233 [WARN]: WebSocket authentication failed {"socketId":"5-Teq65wIksdQzAdAAAF","error":"Token verification failed"}
2025-07-08 08:57:13.492 [INFO]: ::1 - - [08/Jul/2025:00:57:13 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:13.493 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:13.510 [WARN]: WebSocket authentication failed {"socketId":"qiPJXLo5fUDWNRnDAAAH","error":"Token verification failed"}
2025-07-08 08:57:31.501 [INFO]: ::1 - - [08/Jul/2025:00:57:31 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:31.501 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:31.522 [WARN]: WebSocket authentication failed {"socketId":"H4bGPPGYkq-DD0O9AAAJ","error":"Token verification failed"}
2025-07-08 08:57:48.727 [INFO]: ::1 - - [08/Jul/2025:00:57:48 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:48.728 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:48.754 [WARN]: WebSocket authentication failed {"socketId":"alSIMBS1hwc_PTuWAAAL","error":"Token verification failed"}
2025-07-08 08:58:09.435 [INFO]: ::1 - - [08/Jul/2025:00:58:09 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:58:09.436 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 08:58:09.451 [WARN]: WebSocket authentication failed {"socketId":"3yt-dOe1iwPEd3drAAAN","error":"Token verification failed"}
2025-07-08 08:58:25.601 [INFO]: ::1 - - [08/Jul/2025:00:58:25 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:58:25.601 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:58:25.629 [WARN]: WebSocket authentication failed {"socketId":"hYU9glokUcEwl9SqAAAP","error":"Token verification failed"}
2025-07-08 08:59:13.753 [INFO]: ::1 - - [08/Jul/2025:00:59:13 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:59:13.754 [INFO]: GET /health - 200 - 4ms - ::1
2025-07-08 08:59:13.771 [WARN]: WebSocket authentication failed {"socketId":"TfcGikiNQit0BhkpAAAR","error":"Token verification failed"}
2025-07-08 08:59:29.249 [INFO]: ::1 - - [08/Jul/2025:00:59:29 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:59:29.249 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:59:29.262 [WARN]: WebSocket authentication failed {"socketId":"FdA9-3DoUmPjPVI7AAAT","error":"Token verification failed"}
2025-07-08 09:19:09.864 [INFO]: ::1 - - [08/Jul/2025:01:19:09 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:19:09.864 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:19:22.557 [INFO]: ::1 - - [08/Jul/2025:01:19:22 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:19:22.558 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:20:04.570 [INFO]: ::1 - - [08/Jul/2025:01:20:04 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:20:04.570 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:20:44.787 [INFO]: ::1 - - [08/Jul/2025:01:20:44 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:20:44.788 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:21:28.252 [INFO]: ::1 - - [08/Jul/2025:01:21:28 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:21:28.252 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:21:48.114 [INFO]: ::1 - - [08/Jul/2025:01:21:48 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:21:48.114 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:22:12.918 [INFO]: WebSocket server initialized
2025-07-08 09:22:12.919 [INFO]: WebSocket server initialized
2025-07-08 09:22:12.932 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:12.932 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:12.936 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:12.938 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:12.939 [INFO]: Database tables created successfully
2025-07-08 09:22:12.941 [INFO]: Database initialized successfully
2025-07-08 09:22:12.942 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:12.966 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:12.966 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:12.966 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:12.975 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.975 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:22:12.976 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.976 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 09:22:12.978 [INFO]: HTTP server closed
2025-07-08 09:22:12.979 [INFO]: WebSocket server shutdown completed
2025-07-08 09:22:12.979 [INFO]: WebSocket connections closed
2025-07-08 09:22:47.753 [INFO]: WebSocket server initialized
2025-07-08 09:22:47.754 [INFO]: WebSocket server initialized
2025-07-08 09:22:47.770 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:47.770 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:47.778 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:47.781 [INFO]: Database tables created successfully
2025-07-08 09:22:47.783 [INFO]: Database initialized successfully
2025-07-08 09:22:47.784 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:47.814 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:47.815 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:47.815 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:47.818 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:47.821 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:22:47.821 [INFO]: Environment: development
2025-07-08 09:22:47.822 [INFO]: WebSocket server is ready
2025-07-08 09:22:47.825 [INFO]: Database tables created successfully
2025-07-08 09:22:47.826 [INFO]: Database initialized successfully
2025-07-08 09:22:47.827 [INFO]: Database initialized successfully
2025-07-08 09:22:51.389 [INFO]: ::1 - - [08/Jul/2025:01:22:51 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:22:51.390 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 09:22:54.663 [INFO]: WebSocket server initialized
2025-07-08 09:22:54.664 [INFO]: WebSocket server initialized
2025-07-08 09:22:54.676 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:54.677 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:54.682 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:54.684 [INFO]: Database tables created successfully
2025-07-08 09:22:54.685 [INFO]: Database initialized successfully
2025-07-08 09:22:54.686 [INFO]: Database initialized successfully
2025-07-08 09:22:54.688 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:54.690 [INFO]: Database tables created successfully
2025-07-08 09:22:54.690 [INFO]: Database initialized successfully
2025-07-08 09:22:54.691 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:54.714 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:54.714 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:54.715 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:54.726 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.726 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:22:54.727 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.727 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 09:22:54.728 [INFO]: HTTP server closed
2025-07-08 09:22:54.729 [INFO]: WebSocket server shutdown completed
2025-07-08 09:22:54.729 [INFO]: WebSocket connections closed
2025-07-08 09:23:18.057 [INFO]: WebSocket server initialized
2025-07-08 09:23:18.059 [INFO]: WebSocket server initialized
2025-07-08 09:23:18.068 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:23:18.069 [INFO]: 📦 初始化数据库...
2025-07-08 09:23:18.075 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:18.076 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:18.078 [INFO]: Database tables created successfully
2025-07-08 09:23:18.079 [INFO]: Database tables created successfully
2025-07-08 09:23:18.081 [INFO]: Database initialized successfully
2025-07-08 09:23:18.082 [INFO]: Database initialized successfully
2025-07-08 09:23:18.083 [INFO]: Database initialized successfully
2025-07-08 09:23:18.083 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:23:18.106 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:23:18.106 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:23:18.107 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:23:18.115 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:23:18.116 [INFO]: Environment: development
2025-07-08 09:23:18.116 [INFO]: WebSocket server is ready
2025-07-08 09:23:24.053 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:23:24.054 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:23:24.055 [INFO]: HTTP server closed
2025-07-08 09:23:24.057 [INFO]: WebSocket server shutdown completed
2025-07-08 09:23:24.057 [INFO]: WebSocket connections closed
2025-07-08 09:23:39.169 [INFO]: WebSocket server initialized
2025-07-08 09:23:39.170 [INFO]: WebSocket server initialized
2025-07-08 09:23:39.180 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:23:39.181 [INFO]: 📦 初始化数据库...
2025-07-08 09:23:39.187 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:39.191 [INFO]: Database tables created successfully
2025-07-08 09:23:39.191 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:39.192 [INFO]: Database initialized successfully
2025-07-08 09:23:39.193 [INFO]: Database initialized successfully
2025-07-08 09:23:39.194 [INFO]: Database tables created successfully
2025-07-08 09:23:39.195 [INFO]: Database initialized successfully
2025-07-08 09:23:39.195 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:23:39.220 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:23:39.220 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:23:39.220 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:23:39.227 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:23:39.227 [INFO]: Environment: development
2025-07-08 09:23:39.227 [INFO]: WebSocket server is ready
2025-07-08 09:23:42.939 [INFO]: ::1 - - [08/Jul/2025:01:23:42 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:23:42.940 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 09:24:01.492 [INFO]: ::1 - - [08/Jul/2025:01:24:01 +0000] "GET /health HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202"
2025-07-08 09:24:01.492 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:28:14.412 [INFO]: ::1 - - [08/Jul/2025:01:28:14 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:28:14.412 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 09:28:14.432 [WARN]: WebSocket authentication failed {"socketId":"8_XR2tmFBQIAJr-_AAAD","error":"Token verification failed"}
2025-07-08 09:28:20.991 [WARN]: WebSocket authentication failed {"socketId":"B9KAdXKwhGr8srKgAAAF","error":"Token verification failed"}
2025-07-08 09:28:26.928 [WARN]: WebSocket authentication failed {"socketId":"RRbcR3xhhPzvzn-mAAAH","error":"Token verification failed"}
2025-07-08 09:28:34.481 [INFO]: ::1 - - [08/Jul/2025:01:28:34 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:28:34.482 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:28:34.495 [WARN]: WebSocket authentication failed {"socketId":"2vrEqFrfIpWTafzBAAAJ","error":"Token verification failed"}
2025-07-08 09:29:55.530 [INFO]: ::1 - - [08/Jul/2025:01:29:55 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:29:55.531 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:29:55.547 [WARN]: WebSocket authentication failed {"socketId":"T7-m2x7K-21syW-FAAAL","error":"Token verification failed"}
2025-07-08 09:30:16.363 [INFO]: ::1 - - [08/Jul/2025:01:30:16 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:16.364 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:16.381 [WARN]: WebSocket authentication failed {"socketId":"THfATFZzJTzCXEoRAAAN","error":"Token verification failed"}
2025-07-08 09:30:32.400 [INFO]: ::1 - - [08/Jul/2025:01:30:32 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:32.401 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:32.468 [WARN]: WebSocket authentication failed {"socketId":"gdzD1gJT6i1OPOUDAAAP","error":"Token verification failed"}
2025-07-08 09:30:53.539 [INFO]: ::1 - - [08/Jul/2025:01:30:53 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:53.539 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:53.556 [WARN]: WebSocket authentication failed {"socketId":"bXWDHLGbqs39TzcIAAAR","error":"Token verification failed"}
2025-07-08 09:31:11.960 [INFO]: ::1 - - [08/Jul/2025:01:31:11 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:31:11.961 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:31:11.992 [WARN]: WebSocket authentication failed {"socketId":"bqmskIyz9cb4ebTlAAAT","error":"Token verification failed"}
2025-07-08 09:37:42.691 [INFO]: ::1 - - [08/Jul/2025:01:37:42 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:37:42.691 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:37:42.702 [WARN]: WebSocket authentication failed {"socketId":"H_7_D1wQKPd-zii1AAAV","error":"Token verification failed"}
2025-07-08 09:43:19.567 [INFO]: ::1 - - [08/Jul/2025:01:43:19 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:43:19.567 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:43:19.583 [WARN]: WebSocket authentication failed {"socketId":"Q2xGX0OnllMx9R0FAAAX","error":"Token verification failed"}
2025-07-08 09:43:35.334 [INFO]: ::1 - - [08/Jul/2025:01:43:35 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:43:35.335 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:43:35.344 [WARN]: WebSocket authentication failed {"socketId":"bEV0haHfau6HVq6JAAAZ","error":"Token verification failed"}
2025-07-08 09:47:10.876 [INFO]: ::1 - - [08/Jul/2025:01:47:10 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:10.877 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 09:47:10.884 [WARN]: WebSocket authentication failed {"socketId":"PcjOsZYItFO8HYelAAAb","error":"Token verification failed"}
2025-07-08 09:47:13.833 [INFO]: ::1 - - [08/Jul/2025:01:47:13 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:13.833 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:47:13.841 [WARN]: WebSocket authentication failed {"socketId":"CJcxVzKEGo4jnMhkAAAd","error":"Token verification failed"}
2025-07-08 09:47:27.316 [INFO]: ::1 - - [08/Jul/2025:01:47:27 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:27.317 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:47:27.332 [WARN]: WebSocket authentication failed {"socketId":"Qa3-CHQRuArmflK1AAAf","error":"Token verification failed"}
2025-07-08 09:48:45.452 [INFO]: ::1 - - [08/Jul/2025:01:48:45 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:48:45.453 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:48:45.471 [WARN]: WebSocket authentication failed {"socketId":"WlOgxJqjh_aC37_XAAAh","error":"Token verification failed"}
2025-07-08 09:49:01.621 [INFO]: ::1 - - [08/Jul/2025:01:49:01 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:01.621 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:49:01.648 [WARN]: WebSocket authentication failed {"socketId":"N_SpfSZ0r7BkIRIUAAAj","error":"Token verification failed"}
2025-07-08 09:49:19.552 [INFO]: ::1 - - [08/Jul/2025:01:49:19 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:19.553 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:19.567 [WARN]: WebSocket authentication failed {"socketId":"LHea6jtb9ITji3nmAAAl","error":"Token verification failed"}
2025-07-08 09:49:34.533 [INFO]: ::1 - - [08/Jul/2025:01:49:34 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:34.533 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:34.549 [WARN]: WebSocket authentication failed {"socketId":"JWFQXlQ8th4a6BD8AAAn","error":"Token verification failed"}
2025-07-08 09:49:49.962 [INFO]: ::1 - - [08/Jul/2025:01:49:49 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:49.962 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:49.998 [WARN]: WebSocket authentication failed {"socketId":"tPbA1pp8-c09eswWAAAp","error":"Token verification failed"}
2025-07-08 09:50:05.536 [INFO]: ::1 - - [08/Jul/2025:01:50:05 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:05.537 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:05.551 [WARN]: WebSocket authentication failed {"socketId":"euq5g-orUDj6tGxOAAAr","error":"Token verification failed"}
2025-07-08 09:50:20.607 [INFO]: ::1 - - [08/Jul/2025:01:50:20 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:20.608 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:20.625 [WARN]: WebSocket authentication failed {"socketId":"KNMcroly7YTLhfJbAAAt","error":"Token verification failed"}
2025-07-08 09:50:54.476 [INFO]: ::1 - - [08/Jul/2025:01:50:54 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:54.477 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:54.494 [WARN]: WebSocket authentication failed {"socketId":"JLMcU9AwvAysLA2TAAAv","error":"Token verification failed"}
2025-07-08 09:52:06.695 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:52:06.696 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:52:06.697 [INFO]: HTTP server closed
2025-07-08 09:52:06.698 [INFO]: WebSocket server shutdown completed
2025-07-08 09:52:06.698 [INFO]: WebSocket connections closed
2025-07-08 09:52:17.764 [INFO]: WebSocket server initialized
2025-07-08 09:52:17.765 [INFO]: WebSocket server initialized
2025-07-08 09:52:17.779 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:52:17.780 [INFO]: 📦 初始化数据库...
2025-07-08 09:52:17.786 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:52:17.789 [INFO]: Database tables created successfully
2025-07-08 09:52:17.790 [INFO]: Database initialized successfully
2025-07-08 09:52:17.791 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:52:17.817 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:52:17.818 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:52:17.818 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:52:17.820 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:52:17.824 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:52:17.825 [INFO]: Environment: development
2025-07-08 09:52:17.825 [INFO]: WebSocket server is ready
2025-07-08 09:52:17.827 [INFO]: Database tables created successfully
2025-07-08 09:52:17.827 [INFO]: Database initialized successfully
2025-07-08 09:52:17.828 [INFO]: Database initialized successfully
2025-07-08 09:52:20.775 [INFO]: ::1 - - [08/Jul/2025:01:52:20 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:52:20.775 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 09:52:20.817 [WARN]: WebSocket authentication failed {"socketId":"PwsfNpGbLCUvAulvAAAB","error":"Token verification failed"}
2025-07-08 09:52:34.296 [INFO]: ::1 - - [08/Jul/2025:01:52:34 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:52:34.296 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:52:34.312 [WARN]: WebSocket authentication failed {"socketId":"5lg0Lxyw2PNjB1rZAAAE","error":"Token verification failed"}
2025-07-08 09:54:09.206 [INFO]: ::1 - - [08/Jul/2025:01:54:09 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:54:09.207 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:54:09.222 [WARN]: WebSocket authentication failed {"socketId":"67-dAonOzMRpe4DqAAAH","error":"Token verification failed"}
2025-07-08 09:54:38.840 [INFO]: ::1 - - [08/Jul/2025:01:54:38 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:54:38.840 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:54:38.856 [WARN]: WebSocket authentication failed {"socketId":"b2yYqu22Y-m9gROqAAAK","error":"Token verification failed"}
2025-07-08 09:55:00.003 [INFO]: ::1 - - [08/Jul/2025:01:55:00 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:55:00.003 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:55:00.071 [WARN]: WebSocket authentication failed {"socketId":"C3FnjnJWsxVZrtu2AAAN","error":"Token verification failed"}
2025-07-08 09:55:33.284 [INFO]: ::1 - - [08/Jul/2025:01:55:33 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:55:33.284 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:55:33.306 [WARN]: WebSocket authentication failed {"socketId":"jUxMFkZ3o8mkL74rAAAQ","error":"Token verification failed"}
2025-07-08 09:59:27.279 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 09:59:27.280 [INFO]: ::1 - - [08/Jul/2025:01:59:27 +0000] "POST /api/auth/login HTTP/1.1" 500 72 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:59:27.281 [INFO]: POST /api/auth/login - 500 - 12ms - ::1
2025-07-08 09:59:40.835 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:59:40.835 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:59:40.836 [INFO]: HTTP server closed
2025-07-08 09:59:40.837 [INFO]: WebSocket server shutdown completed
2025-07-08 09:59:40.837 [INFO]: WebSocket connections closed
2025-07-08 09:59:53.315 [INFO]: WebSocket server initialized
2025-07-08 09:59:53.316 [INFO]: WebSocket server initialized
2025-07-08 09:59:53.331 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:59:53.332 [INFO]: 📦 初始化数据库...
2025-07-08 09:59:53.341 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:59:53.344 [INFO]: Database tables created successfully
2025-07-08 09:59:53.345 [INFO]: Database initialized successfully
2025-07-08 09:59:53.345 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:59:53.371 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:59:53.372 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:59:53.373 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:59:53.376 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:59:53.381 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:59:53.382 [INFO]: Environment: development
2025-07-08 09:59:53.382 [INFO]: WebSocket server is ready
2025-07-08 09:59:53.384 [INFO]: Database tables created successfully
2025-07-08 09:59:53.384 [INFO]: Database initialized successfully
2025-07-08 09:59:53.385 [INFO]: Database initialized successfully
2025-07-08 10:00:05.584 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:00:05.587 [INFO]: ::1 - - [08/Jul/2025:02:00:05 +0000] "POST /api/auth/login HTTP/1.1" 500 72 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:00:05.588 [INFO]: POST /api/auth/login - 500 - 12ms - ::1
2025-07-08 10:06:23.222 [INFO]: ::1 - - [08/Jul/2025:02:06:23 +0000] "GET /health HTTP/1.1" 200 120 "-" "-"
2025-07-08 10:06:23.222 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:06:23.230 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:77:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:06:23.232 [INFO]: ::1 - - [08/Jul/2025:02:06:23 +0000] "POST /api/auth/login HTTP/1.1" 500 72 "-" "-"
2025-07-08 10:06:23.232 [INFO]: POST /api/auth/login - 500 - 4ms - ::1
2025-07-08 10:06:54.075 [INFO]: WebSocket server initialized
2025-07-08 10:06:54.076 [INFO]: WebSocket server initialized
2025-07-08 10:06:54.087 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:06:54.093 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:06:54.095 [INFO]: Database tables created successfully
2025-07-08 10:06:54.096 [INFO]: Database initialized successfully
2025-07-08 10:06:54.097 [INFO]: Database initialized successfully
2025-07-08 10:06:54.120 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:06:54.121 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:06:54.122 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:07:25.307 [INFO]: WebSocket server initialized
2025-07-08 10:07:25.308 [INFO]: WebSocket server initialized
2025-07-08 10:07:25.319 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:07:25.324 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:07:25.326 [INFO]: Database tables created successfully
2025-07-08 10:07:25.327 [INFO]: Database initialized successfully
2025-07-08 10:07:25.328 [INFO]: Database initialized successfully
2025-07-08 10:07:25.351 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:07:25.352 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:07:25.352 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:07:25.360 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:07:25.361 [INFO]: Environment: development
2025-07-08 10:07:25.361 [INFO]: WebSocket server is ready
2025-07-08 10:07:25.361 [INFO]: Database initialized and ready
2025-07-08 10:07:51.196 [INFO]: ::1 - - [08/Jul/2025:02:07:51 +0000] "GET /health HTTP/1.1" 200 120 "-" "-"
2025-07-08 10:07:51.197 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 10:07:51.224 [ERROR]: Login error: Database not connected
Error: Database not connected
    at DatabaseManager.getUserByUsername (G:\codingProject\talking\backend\src\database\DatabaseManager.ts:323:25)
    at G:\codingProject\talking\backend\src\routes\auth.ts:81:32
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at G:\codingProject\talking\backend\src\middleware\validation.ts:99:5
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at next (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (G:\codingProject\talking\backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (G:\codingProject\talking\backend\node_modules\express\lib\router\layer.js:95:5)
    at G:\codingProject\talking\backend\node_modules\express\lib\router\index.js:284:15
2025-07-08 10:07:51.226 [INFO]: ::1 - - [08/Jul/2025:02:07:51 +0000] "POST /api/auth/login HTTP/1.1" 500 72 "-" "-"
2025-07-08 10:07:51.227 [INFO]: POST /api/auth/login - 500 - 12ms - ::1
2025-07-08 10:11:24.919 [INFO]: WebSocket server initialized
2025-07-08 10:11:24.920 [INFO]: WebSocket server initialized
2025-07-08 10:11:24.930 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:11:24.937 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:11:24.939 [INFO]: Database tables created successfully
2025-07-08 10:11:24.941 [INFO]: Database initialized successfully
2025-07-08 10:11:24.941 [INFO]: Database initialized successfully
2025-07-08 10:11:24.965 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:11:24.966 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:11:24.967 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:11:24.975 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:11:24.976 [INFO]: Environment: development
2025-07-08 10:11:24.976 [INFO]: WebSocket server is ready
2025-07-08 10:11:24.976 [INFO]: Database initialized and ready
2025-07-08 10:11:49.663 [INFO]: ::1 - - [08/Jul/2025:02:11:49 +0000] "GET /health HTTP/1.1" 200 120 "-" "-"
2025-07-08 10:11:49.664 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 10:11:49.718 [INFO]: New user created: testuser (ID: 1)
2025-07-08 10:11:49.724 [INFO]: ::1 - - [08/Jul/2025:02:11:49 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "-"
2025-07-08 10:11:49.725 [INFO]: POST /api/auth/login - 200 - 43ms - ::1
2025-07-08 10:11:49.742 [INFO]: WebSocket user authenticated {"socketId":"1J8BKsXOxo3Eq4igAAAB","userId":1,"username":"testuser"}
2025-07-08 10:11:49.750 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:11:49.751 [INFO]: 🛑 正在关闭服务器...
2025-07-08 10:11:49.751 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:11:49.752 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 10:11:49.756 [INFO]: HTTP server closed
2025-07-08 10:11:49.757 [INFO]: WebSocket server shutdown completed
2025-07-08 10:11:49.758 [INFO]: WebSocket connections closed
2025-07-08 10:11:49.842 [INFO]: Database connection closed
2025-07-08 10:12:42.541 [INFO]: WebSocket server initialized
2025-07-08 10:12:42.542 [INFO]: WebSocket server initialized
2025-07-08 10:12:42.556 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:12:42.562 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:12:42.564 [INFO]: Database tables created successfully
2025-07-08 10:12:42.565 [INFO]: Database initialized successfully
2025-07-08 10:12:42.566 [INFO]: Database initialized successfully
2025-07-08 10:12:42.589 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:12:42.590 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:12:42.590 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:12:42.596 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:12:42.596 [INFO]: Environment: development
2025-07-08 10:12:42.597 [INFO]: WebSocket server is ready
2025-07-08 10:12:42.597 [INFO]: Database initialized and ready
2025-07-08 10:13:18.113 [INFO]: New user created: 544 (ID: 2)
2025-07-08 10:13:18.120 [INFO]: ::1 - - [08/Jul/2025:02:13:18 +0000] "POST /api/auth/login HTTP/1.1" 200 612 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:13:18.121 [INFO]: POST /api/auth/login - 200 - 49ms - ::1
2025-07-08 10:13:19.630 [INFO]: ::1 - - [08/Jul/2025:02:13:19 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:13:19.630 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:13:19.650 [INFO]: WebSocket user authenticated {"socketId":"3LOfiTBwRWwTFkWGAAAB","userId":2,"username":"544"}
2025-07-08 10:13:19.660 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:13:19.661 [INFO]: 🛑 正在关闭服务器...
2025-07-08 10:13:19.661 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:13:19.661 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 10:13:19.737 [INFO]: Database connection closed
2025-07-08 10:15:38.717 [INFO]: WebSocket server initialized
2025-07-08 10:15:38.718 [INFO]: WebSocket server initialized
2025-07-08 10:15:38.728 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:15:38.735 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:15:38.738 [INFO]: Database tables created successfully
2025-07-08 10:15:38.739 [INFO]: Database initialized successfully
2025-07-08 10:15:38.739 [INFO]: Database initialized successfully
2025-07-08 10:15:38.766 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:15:38.767 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:15:38.768 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:15:38.774 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:15:38.774 [INFO]: Environment: development
2025-07-08 10:15:38.774 [INFO]: WebSocket server is ready
2025-07-08 10:15:38.775 [INFO]: Database initialized and ready
2025-07-08 10:15:41.873 [INFO]: ::1 - - [08/Jul/2025:02:15:41 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:15:41.874 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 10:15:41.910 [INFO]: WebSocket user authenticated {"socketId":"1TP4wyTP4gNuYtFJAAAB","userId":2,"username":"544"}
2025-07-08 10:15:41.923 [ERROR]: 未捕获的异常: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:15:41.924 [INFO]: 🛑 正在关闭服务器...
2025-07-08 10:15:41.925 [ERROR]: Uncaught Exception: logger_1.logger.websocket is not a function
TypeError: logger_1.logger.websocket is not a function
    at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
    at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
    at Namespace.emit (node:events:518:28)
    at Namespace.emit (node:domain:488:12)
    at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
    at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
    at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-07-08 10:15:41.926 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 10:19:05.575 [INFO]: WebSocket server initialized
2025-07-08 10:19:05.576 [INFO]: WebSocket server initialized
2025-07-08 10:19:05.591 [INFO]: 🚀 启动开发服务器...
2025-07-08 10:19:05.598 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 10:19:05.601 [INFO]: Database tables created successfully
2025-07-08 10:19:05.602 [INFO]: Database initialized successfully
2025-07-08 10:19:05.603 [INFO]: Database initialized successfully
2025-07-08 10:19:05.629 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 10:19:05.630 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 10:19:05.630 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 10:19:05.639 [INFO]: Server is running on http://localhost:3001
2025-07-08 10:19:05.640 [INFO]: Environment: development
2025-07-08 10:19:05.641 [INFO]: WebSocket server is ready
2025-07-08 10:19:05.641 [INFO]: Database initialized and ready
2025-07-08 10:19:32.717 [INFO]: ::1 - - [08/Jul/2025:02:19:32 +0000] "GET /health HTTP/1.1" 200 119 "-" "-"
2025-07-08 10:19:32.718 [INFO]: GET /health - 200 - 5ms - ::1
2025-07-08 10:19:32.774 [INFO]: User login: testuser (ID: 1)
2025-07-08 10:19:32.779 [INFO]: ::1 - - [08/Jul/2025:02:19:32 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "-"
2025-07-08 10:19:32.779 [INFO]: POST /api/auth/login - 200 - 46ms - ::1
2025-07-08 10:19:32.796 [INFO]: WebSocket user authenticated {"socketId":"rzfLvMQzQoaqhMfRAAAB","userId":1,"username":"testuser"}
2025-07-08 10:19:32.799 [INFO]: WebSocket connected {"socketId":"rzfLvMQzQoaqhMfRAAAB","userId":"1","type":"websocket","username":"testuser","displayName":"testuser"}
2025-07-08 10:19:32.803 [INFO]: Client initiated disconnect
2025-07-08 10:19:32.803 [INFO]: WebSocket disconnected {"socketId":"rzfLvMQzQoaqhMfRAAAB","userId":"1","type":"websocket","username":"testuser","reason":"client namespace disconnect","details":{},"duration":4,"recovered":false}
2025-07-08 10:39:01.433 [INFO]: ::1 - - [08/Jul/2025:02:39:01 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:01.434 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 10:39:01.455 [INFO]: WebSocket user authenticated {"socketId":"YudJiF9sPqzdysEbAAAE","userId":2,"username":"544"}
2025-07-08 10:39:01.456 [INFO]: WebSocket connected {"socketId":"YudJiF9sPqzdysEbAAAE","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:39:01.472 [INFO]: User authenticated {"userId":2,"username":"544","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:39:01.476 [INFO]: ::1 - - [08/Jul/2025:02:39:01 +0000] "GET /api/users/online HTTP/1.1" 200 142 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:01.479 [INFO]: GET /api/users/online - 200 - 7ms - ::1
2025-07-08 10:39:04.173 [INFO]: ::1 - - [08/Jul/2025:02:39:04 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:04.174 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:39:04.184 [INFO]: WebSocket user authenticated {"socketId":"awKePKyM97A0HFvNAAAH","userId":2,"username":"544"}
2025-07-08 10:39:04.185 [INFO]: WebSocket connected {"socketId":"awKePKyM97A0HFvNAAAH","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:39:04.192 [INFO]: User authenticated {"userId":2,"username":"544","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:39:04.195 [INFO]: ::1 - - [08/Jul/2025:02:39:04 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:04.196 [INFO]: GET /api/users/online - 304 - 5ms - ::1
2025-07-08 10:39:07.823 [INFO]: ::1 - - [08/Jul/2025:02:39:07 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:07.824 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 10:39:07.834 [INFO]: WebSocket user authenticated {"socketId":"Vr243j3hBeVM2vzDAAAK","userId":2,"username":"544"}
2025-07-08 10:39:07.835 [INFO]: WebSocket connected {"socketId":"Vr243j3hBeVM2vzDAAAK","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:39:07.846 [INFO]: User authenticated {"userId":2,"username":"544","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:39:07.848 [INFO]: ::1 - - [08/Jul/2025:02:39:07 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:07.848 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 10:39:11.186 [INFO]: ::1 - - [08/Jul/2025:02:39:11 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:11.187 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 10:39:11.195 [INFO]: WebSocket user authenticated {"socketId":"hbBSZvfJ_mp3uAObAAAN","userId":2,"username":"544"}
2025-07-08 10:39:11.196 [INFO]: WebSocket connected {"socketId":"hbBSZvfJ_mp3uAObAAAN","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:39:11.208 [INFO]: User authenticated {"userId":2,"username":"544","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:39:11.210 [INFO]: ::1 - - [08/Jul/2025:02:39:11 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:39:11.210 [INFO]: GET /api/users/online - 304 - 3ms - ::1
2025-07-08 10:39:15.268 [INFO]: Client initiated disconnect
2025-07-08 10:39:15.269 [INFO]: WebSocket disconnected {"socketId":"hbBSZvfJ_mp3uAObAAAN","userId":"2","type":"websocket","username":"544","reason":"client namespace disconnect","details":{},"duration":4071,"recovered":false}
2025-07-08 10:39:16.874 [INFO]: WebSocket user authenticated {"socketId":"W2yAub7av2mYOpPuAAAQ","userId":2,"username":"544"}
2025-07-08 10:39:16.876 [INFO]: WebSocket connected {"socketId":"W2yAub7av2mYOpPuAAAQ","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:05.957 [WARN]: Connection timeout {"socketId":"YudJiF9sPqzdysEbAAAE","userId":2,"username":"544","timeSinceLastActivity":304500}
2025-07-08 10:44:05.958 [INFO]: Server initiated disconnect
2025-07-08 10:44:05.959 [INFO]: WebSocket disconnected {"socketId":"YudJiF9sPqzdysEbAAAE","userId":"2","type":"websocket","username":"544","reason":"server namespace disconnect","details":{},"duration":304501,"recovered":false}
2025-07-08 10:44:05.960 [WARN]: Connection timeout {"socketId":"awKePKyM97A0HFvNAAAH","userId":2,"username":"544","timeSinceLastActivity":301771}
2025-07-08 10:44:05.960 [INFO]: Server initiated disconnect
2025-07-08 10:44:05.960 [INFO]: WebSocket disconnected {"socketId":"awKePKyM97A0HFvNAAAH","userId":"2","type":"websocket","username":"544","reason":"server namespace disconnect","details":{},"duration":301775,"recovered":false}
2025-07-08 10:44:08.976 [INFO]: WebSocket user authenticated {"socketId":"jESxRp4QJ3qjxaGTAAAT","userId":2,"username":"544"}
2025-07-08 10:44:08.977 [INFO]: WebSocket connected {"socketId":"jESxRp4QJ3qjxaGTAAAT","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:11.972 [INFO]: WebSocket user authenticated {"socketId":"FqXDp-6FdXE76EOLAAAW","userId":2,"username":"544"}
2025-07-08 10:44:11.973 [INFO]: WebSocket connected {"socketId":"FqXDp-6FdXE76EOLAAAW","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:35.970 [WARN]: Connection timeout {"socketId":"Vr243j3hBeVM2vzDAAAK","userId":2,"username":"544","timeSinceLastActivity":328135}
2025-07-08 10:44:35.971 [INFO]: Server initiated disconnect
2025-07-08 10:44:35.972 [INFO]: WebSocket disconnected {"socketId":"Vr243j3hBeVM2vzDAAAK","userId":"2","type":"websocket","username":"544","reason":"server namespace disconnect","details":{},"duration":328136,"recovered":false}
2025-07-08 10:44:35.972 [WARN]: Connection timeout {"socketId":"W2yAub7av2mYOpPuAAAQ","userId":2,"username":"544","timeSinceLastActivity":319094}
2025-07-08 10:44:35.973 [INFO]: Server initiated disconnect
2025-07-08 10:44:35.973 [INFO]: WebSocket disconnected {"socketId":"W2yAub7av2mYOpPuAAAQ","userId":"2","type":"websocket","username":"544","reason":"server namespace disconnect","details":{},"duration":319097,"recovered":false}
2025-07-08 10:44:38.990 [INFO]: WebSocket user authenticated {"socketId":"8SNxuAdruSAiBQGmAAAZ","userId":2,"username":"544"}
2025-07-08 10:44:38.991 [INFO]: WebSocket connected {"socketId":"8SNxuAdruSAiBQGmAAAZ","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:41.991 [INFO]: WebSocket user authenticated {"socketId":"dtO5CURnmG4UM5MAAAAc","userId":2,"username":"544"}
2025-07-08 10:44:41.991 [INFO]: WebSocket connected {"socketId":"dtO5CURnmG4UM5MAAAAc","userId":"2","type":"websocket","username":"544","displayName":"544"}
2025-07-08 10:44:49.697 [INFO]: User login: testuser (ID: 1)
2025-07-08 10:44:49.699 [INFO]: ::1 - - [08/Jul/2025:02:44:49 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
2025-07-08 10:44:49.699 [INFO]: POST /api/auth/login - 200 - 3ms - ::1
2025-07-08 10:44:49.716 [INFO]: WebSocket user authenticated {"socketId":"1IoDniu8aMZE04HNAAAf","userId":1,"username":"testuser"}
2025-07-08 10:44:49.717 [INFO]: WebSocket connected {"socketId":"1IoDniu8aMZE04HNAAAf","userId":"1","type":"websocket","username":"testuser","displayName":"testuser"}
2025-07-08 10:44:51.732 [INFO]: Client initiated disconnect
2025-07-08 10:44:51.733 [INFO]: WebSocket disconnected {"socketId":"1IoDniu8aMZE04HNAAAf","userId":"1","type":"websocket","username":"testuser","reason":"client namespace disconnect","details":{},"duration":2016,"recovered":false}
2025-07-08 10:45:12.902 [INFO]: WebSocket disconnected {"socketId":"jESxRp4QJ3qjxaGTAAAT","userId":"2","type":"websocket","username":"544","reason":"transport close","details":{},"duration":63924,"recovered":false}
2025-07-08 10:45:12.904 [INFO]: WebSocket disconnected {"socketId":"dtO5CURnmG4UM5MAAAAc","userId":"2","type":"websocket","username":"544","reason":"transport close","details":{},"duration":30913,"recovered":false}
2025-07-08 10:45:12.906 [INFO]: WebSocket disconnected {"socketId":"8SNxuAdruSAiBQGmAAAZ","userId":"2","type":"websocket","username":"544","reason":"transport close","details":{},"duration":33915,"recovered":false}
2025-07-08 10:45:12.907 [INFO]: WebSocket disconnected {"socketId":"FqXDp-6FdXE76EOLAAAW","userId":"2","type":"websocket","username":"544","reason":"transport close","details":{},"duration":60935,"recovered":false}
2025-07-08 10:46:20.286 [INFO]: New user created: 45654 (ID: 3)
2025-07-08 10:46:20.289 [INFO]: ::1 - - [08/Jul/2025:02:46:20 +0000] "POST /api/auth/login HTTP/1.1" 200 617 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:46:20.290 [INFO]: POST /api/auth/login - 200 - 5ms - ::1
2025-07-08 10:50:11.192 [INFO]: ::1 - - [08/Jul/2025:02:50:11 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:50:11.193 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 10:50:11.248 [INFO]: WebSocket user authenticated {"socketId":"J9gNDTYh5tvvTqTHAAAi","userId":3,"username":"45654"}
2025-07-08 10:50:11.249 [INFO]: WebSocket connected {"socketId":"J9gNDTYh5tvvTqTHAAAi","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:50:11.283 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:50:11.285 [INFO]: ::1 - - [08/Jul/2025:02:50:11 +0000] "GET /api/users/online HTTP/1.1" 200 186 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:50:11.286 [INFO]: GET /api/users/online - 200 - 5ms - ::1
2025-07-08 10:50:11.997 [INFO]: ::1 - - [08/Jul/2025:02:50:11 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:50:11.998 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 10:50:12.097 [INFO]: WebSocket user authenticated {"socketId":"iPlP14OUpiGbru60AAAl","userId":3,"username":"45654"}
2025-07-08 10:50:12.098 [INFO]: WebSocket connected {"socketId":"iPlP14OUpiGbru60AAAl","userId":"3","type":"websocket","username":"45654","displayName":"45654"}
2025-07-08 10:50:12.106 [INFO]: User authenticated {"userId":3,"username":"45654","ip":"::1","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"}
2025-07-08 10:50:12.109 [INFO]: ::1 - - [08/Jul/2025:02:50:12 +0000] "GET /api/users/online HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 10:50:12.109 [INFO]: GET /api/users/online - 304 - 4ms - ::1

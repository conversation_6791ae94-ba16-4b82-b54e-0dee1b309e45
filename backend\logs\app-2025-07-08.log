2025-07-08 08:32:09.488 [INFO]: WebSocket server initialized
2025-07-08 08:32:09.489 [INFO]: WebSocket server initialized
2025-07-08 08:32:09.506 [INFO]: 🚀 启动开发服务器...
2025-07-08 08:32:09.507 [INFO]: 📦 初始化数据库...
2025-07-08 08:32:09.514 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:32:09.517 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:32:09.518 [INFO]: Database tables created successfully
2025-07-08 08:32:09.519 [INFO]: Database initialized successfully
2025-07-08 08:32:09.519 [INFO]: ✅ 数据库初始化完成
2025-07-08 08:32:09.545 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 08:32:09.545 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 08:32:09.546 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 08:32:09.552 [INFO]: Server is running on http://localhost:3001
2025-07-08 08:32:09.553 [INFO]: Environment: development
2025-07-08 08:32:09.553 [INFO]: WebSocket server is ready
2025-07-08 08:32:09.554 [INFO]: Database tables created successfully
2025-07-08 08:32:09.555 [INFO]: Database initialized successfully
2025-07-08 08:32:09.555 [INFO]: Database initialized successfully
2025-07-08 08:32:12.463 [INFO]: 🛑 正在关闭服务器...
2025-07-08 08:32:12.464 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 08:32:12.465 [INFO]: HTTP server closed
2025-07-08 08:32:12.467 [INFO]: WebSocket server shutdown completed
2025-07-08 08:32:12.467 [INFO]: WebSocket connections closed
2025-07-08 08:53:40.443 [INFO]: WebSocket server initialized
2025-07-08 08:53:40.444 [INFO]: WebSocket server initialized
2025-07-08 08:53:40.459 [INFO]: 🚀 启动开发服务器...
2025-07-08 08:53:40.460 [INFO]: 📦 初始化数据库...
2025-07-08 08:53:40.467 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:53:40.470 [INFO]: Database tables created successfully
2025-07-08 08:53:40.471 [INFO]: Database initialized successfully
2025-07-08 08:53:40.472 [INFO]: ✅ 数据库初始化完成
2025-07-08 08:53:40.502 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 08:53:40.503 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 08:53:40.504 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 08:53:40.508 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:53:40.513 [INFO]: Server is running on http://localhost:3001
2025-07-08 08:53:40.514 [INFO]: Environment: development
2025-07-08 08:53:40.514 [INFO]: WebSocket server is ready
2025-07-08 08:53:40.516 [INFO]: Database tables created successfully
2025-07-08 08:53:40.517 [INFO]: Database initialized successfully
2025-07-08 08:53:40.517 [INFO]: Database initialized successfully
2025-07-08 08:53:56.072 [INFO]: ::1 - - [08/Jul/2025:00:53:56 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:53:56.073 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 08:53:56.108 [WARN]: WebSocket authentication failed {"socketId":"9ghcKK1xBx9Fp4ozAAAB","error":"Token verification failed"}
2025-07-08 08:55:53.547 [INFO]: ::1 - - [08/Jul/2025:00:55:53 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:55:53.547 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 08:55:53.573 [WARN]: WebSocket authentication failed {"socketId":"R6Dsz8QbhQV9ZahCAAAD","error":"Token verification failed"}
2025-07-08 08:56:52.216 [INFO]: ::1 - - [08/Jul/2025:00:56:52 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:56:52.217 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:56:52.233 [WARN]: WebSocket authentication failed {"socketId":"5-Teq65wIksdQzAdAAAF","error":"Token verification failed"}
2025-07-08 08:57:13.492 [INFO]: ::1 - - [08/Jul/2025:00:57:13 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:13.493 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:13.510 [WARN]: WebSocket authentication failed {"socketId":"qiPJXLo5fUDWNRnDAAAH","error":"Token verification failed"}
2025-07-08 08:57:31.501 [INFO]: ::1 - - [08/Jul/2025:00:57:31 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:31.501 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:31.522 [WARN]: WebSocket authentication failed {"socketId":"H4bGPPGYkq-DD0O9AAAJ","error":"Token verification failed"}
2025-07-08 08:57:48.727 [INFO]: ::1 - - [08/Jul/2025:00:57:48 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:48.728 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:48.754 [WARN]: WebSocket authentication failed {"socketId":"alSIMBS1hwc_PTuWAAAL","error":"Token verification failed"}
2025-07-08 08:58:09.435 [INFO]: ::1 - - [08/Jul/2025:00:58:09 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:58:09.436 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 08:58:09.451 [WARN]: WebSocket authentication failed {"socketId":"3yt-dOe1iwPEd3drAAAN","error":"Token verification failed"}
2025-07-08 08:58:25.601 [INFO]: ::1 - - [08/Jul/2025:00:58:25 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:58:25.601 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:58:25.629 [WARN]: WebSocket authentication failed {"socketId":"hYU9glokUcEwl9SqAAAP","error":"Token verification failed"}
2025-07-08 08:59:13.753 [INFO]: ::1 - - [08/Jul/2025:00:59:13 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:59:13.754 [INFO]: GET /health - 200 - 4ms - ::1
2025-07-08 08:59:13.771 [WARN]: WebSocket authentication failed {"socketId":"TfcGikiNQit0BhkpAAAR","error":"Token verification failed"}
2025-07-08 08:59:29.249 [INFO]: ::1 - - [08/Jul/2025:00:59:29 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:59:29.249 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:59:29.262 [WARN]: WebSocket authentication failed {"socketId":"FdA9-3DoUmPjPVI7AAAT","error":"Token verification failed"}
2025-07-08 09:19:09.864 [INFO]: ::1 - - [08/Jul/2025:01:19:09 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:19:09.864 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:19:22.557 [INFO]: ::1 - - [08/Jul/2025:01:19:22 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:19:22.558 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:20:04.570 [INFO]: ::1 - - [08/Jul/2025:01:20:04 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:20:04.570 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:20:44.787 [INFO]: ::1 - - [08/Jul/2025:01:20:44 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:20:44.788 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:21:28.252 [INFO]: ::1 - - [08/Jul/2025:01:21:28 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:21:28.252 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:21:48.114 [INFO]: ::1 - - [08/Jul/2025:01:21:48 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:21:48.114 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:22:12.918 [INFO]: WebSocket server initialized
2025-07-08 09:22:12.919 [INFO]: WebSocket server initialized
2025-07-08 09:22:12.932 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:12.932 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:12.936 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:12.938 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:12.939 [INFO]: Database tables created successfully
2025-07-08 09:22:12.941 [INFO]: Database initialized successfully
2025-07-08 09:22:12.942 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:12.966 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:12.966 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:12.966 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:12.975 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.975 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:22:12.976 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.976 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 09:22:12.978 [INFO]: HTTP server closed
2025-07-08 09:22:12.979 [INFO]: WebSocket server shutdown completed
2025-07-08 09:22:12.979 [INFO]: WebSocket connections closed
2025-07-08 09:22:47.753 [INFO]: WebSocket server initialized
2025-07-08 09:22:47.754 [INFO]: WebSocket server initialized
2025-07-08 09:22:47.770 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:47.770 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:47.778 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:47.781 [INFO]: Database tables created successfully
2025-07-08 09:22:47.783 [INFO]: Database initialized successfully
2025-07-08 09:22:47.784 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:47.814 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:47.815 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:47.815 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:47.818 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:47.821 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:22:47.821 [INFO]: Environment: development
2025-07-08 09:22:47.822 [INFO]: WebSocket server is ready
2025-07-08 09:22:47.825 [INFO]: Database tables created successfully
2025-07-08 09:22:47.826 [INFO]: Database initialized successfully
2025-07-08 09:22:47.827 [INFO]: Database initialized successfully
2025-07-08 09:22:51.389 [INFO]: ::1 - - [08/Jul/2025:01:22:51 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:22:51.390 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 09:22:54.663 [INFO]: WebSocket server initialized
2025-07-08 09:22:54.664 [INFO]: WebSocket server initialized
2025-07-08 09:22:54.676 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:54.677 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:54.682 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:54.684 [INFO]: Database tables created successfully
2025-07-08 09:22:54.685 [INFO]: Database initialized successfully
2025-07-08 09:22:54.686 [INFO]: Database initialized successfully
2025-07-08 09:22:54.688 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:54.690 [INFO]: Database tables created successfully
2025-07-08 09:22:54.690 [INFO]: Database initialized successfully
2025-07-08 09:22:54.691 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:54.714 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:54.714 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:54.715 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:54.726 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.726 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:22:54.727 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.727 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 09:22:54.728 [INFO]: HTTP server closed
2025-07-08 09:22:54.729 [INFO]: WebSocket server shutdown completed
2025-07-08 09:22:54.729 [INFO]: WebSocket connections closed
2025-07-08 09:23:18.057 [INFO]: WebSocket server initialized
2025-07-08 09:23:18.059 [INFO]: WebSocket server initialized
2025-07-08 09:23:18.068 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:23:18.069 [INFO]: 📦 初始化数据库...
2025-07-08 09:23:18.075 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:18.076 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:18.078 [INFO]: Database tables created successfully
2025-07-08 09:23:18.079 [INFO]: Database tables created successfully
2025-07-08 09:23:18.081 [INFO]: Database initialized successfully
2025-07-08 09:23:18.082 [INFO]: Database initialized successfully
2025-07-08 09:23:18.083 [INFO]: Database initialized successfully
2025-07-08 09:23:18.083 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:23:18.106 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:23:18.106 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:23:18.107 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:23:18.115 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:23:18.116 [INFO]: Environment: development
2025-07-08 09:23:18.116 [INFO]: WebSocket server is ready
2025-07-08 09:23:24.053 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:23:24.054 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:23:24.055 [INFO]: HTTP server closed
2025-07-08 09:23:24.057 [INFO]: WebSocket server shutdown completed
2025-07-08 09:23:24.057 [INFO]: WebSocket connections closed

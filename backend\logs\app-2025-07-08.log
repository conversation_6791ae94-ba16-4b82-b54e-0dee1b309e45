2025-07-08 08:32:09.488 [INFO]: WebSocket server initialized
2025-07-08 08:32:09.489 [INFO]: WebSocket server initialized
2025-07-08 08:32:09.506 [INFO]: 🚀 启动开发服务器...
2025-07-08 08:32:09.507 [INFO]: 📦 初始化数据库...
2025-07-08 08:32:09.514 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:32:09.517 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:32:09.518 [INFO]: Database tables created successfully
2025-07-08 08:32:09.519 [INFO]: Database initialized successfully
2025-07-08 08:32:09.519 [INFO]: ✅ 数据库初始化完成
2025-07-08 08:32:09.545 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 08:32:09.545 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 08:32:09.546 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 08:32:09.552 [INFO]: Server is running on http://localhost:3001
2025-07-08 08:32:09.553 [INFO]: Environment: development
2025-07-08 08:32:09.553 [INFO]: WebSocket server is ready
2025-07-08 08:32:09.554 [INFO]: Database tables created successfully
2025-07-08 08:32:09.555 [INFO]: Database initialized successfully
2025-07-08 08:32:09.555 [INFO]: Database initialized successfully
2025-07-08 08:32:12.463 [INFO]: 🛑 正在关闭服务器...
2025-07-08 08:32:12.464 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 08:32:12.465 [INFO]: HTTP server closed
2025-07-08 08:32:12.467 [INFO]: WebSocket server shutdown completed
2025-07-08 08:32:12.467 [INFO]: WebSocket connections closed
2025-07-08 08:53:40.443 [INFO]: WebSocket server initialized
2025-07-08 08:53:40.444 [INFO]: WebSocket server initialized
2025-07-08 08:53:40.459 [INFO]: 🚀 启动开发服务器...
2025-07-08 08:53:40.460 [INFO]: 📦 初始化数据库...
2025-07-08 08:53:40.467 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:53:40.470 [INFO]: Database tables created successfully
2025-07-08 08:53:40.471 [INFO]: Database initialized successfully
2025-07-08 08:53:40.472 [INFO]: ✅ 数据库初始化完成
2025-07-08 08:53:40.502 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 08:53:40.503 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 08:53:40.504 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 08:53:40.508 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 08:53:40.513 [INFO]: Server is running on http://localhost:3001
2025-07-08 08:53:40.514 [INFO]: Environment: development
2025-07-08 08:53:40.514 [INFO]: WebSocket server is ready
2025-07-08 08:53:40.516 [INFO]: Database tables created successfully
2025-07-08 08:53:40.517 [INFO]: Database initialized successfully
2025-07-08 08:53:40.517 [INFO]: Database initialized successfully
2025-07-08 08:53:56.072 [INFO]: ::1 - - [08/Jul/2025:00:53:56 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:53:56.073 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 08:53:56.108 [WARN]: WebSocket authentication failed {"socketId":"9ghcKK1xBx9Fp4ozAAAB","error":"Token verification failed"}
2025-07-08 08:55:53.547 [INFO]: ::1 - - [08/Jul/2025:00:55:53 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:55:53.547 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 08:55:53.573 [WARN]: WebSocket authentication failed {"socketId":"R6Dsz8QbhQV9ZahCAAAD","error":"Token verification failed"}
2025-07-08 08:56:52.216 [INFO]: ::1 - - [08/Jul/2025:00:56:52 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:56:52.217 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:56:52.233 [WARN]: WebSocket authentication failed {"socketId":"5-Teq65wIksdQzAdAAAF","error":"Token verification failed"}
2025-07-08 08:57:13.492 [INFO]: ::1 - - [08/Jul/2025:00:57:13 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:13.493 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:13.510 [WARN]: WebSocket authentication failed {"socketId":"qiPJXLo5fUDWNRnDAAAH","error":"Token verification failed"}
2025-07-08 08:57:31.501 [INFO]: ::1 - - [08/Jul/2025:00:57:31 +0000] "GET /health HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:31.501 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:31.522 [WARN]: WebSocket authentication failed {"socketId":"H4bGPPGYkq-DD0O9AAAJ","error":"Token verification failed"}
2025-07-08 08:57:48.727 [INFO]: ::1 - - [08/Jul/2025:00:57:48 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:57:48.728 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:57:48.754 [WARN]: WebSocket authentication failed {"socketId":"alSIMBS1hwc_PTuWAAAL","error":"Token verification failed"}
2025-07-08 08:58:09.435 [INFO]: ::1 - - [08/Jul/2025:00:58:09 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:58:09.436 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 08:58:09.451 [WARN]: WebSocket authentication failed {"socketId":"3yt-dOe1iwPEd3drAAAN","error":"Token verification failed"}
2025-07-08 08:58:25.601 [INFO]: ::1 - - [08/Jul/2025:00:58:25 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:58:25.601 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:58:25.629 [WARN]: WebSocket authentication failed {"socketId":"hYU9glokUcEwl9SqAAAP","error":"Token verification failed"}
2025-07-08 08:59:13.753 [INFO]: ::1 - - [08/Jul/2025:00:59:13 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:59:13.754 [INFO]: GET /health - 200 - 4ms - ::1
2025-07-08 08:59:13.771 [WARN]: WebSocket authentication failed {"socketId":"TfcGikiNQit0BhkpAAAR","error":"Token verification failed"}
2025-07-08 08:59:29.249 [INFO]: ::1 - - [08/Jul/2025:00:59:29 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 08:59:29.249 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 08:59:29.262 [WARN]: WebSocket authentication failed {"socketId":"FdA9-3DoUmPjPVI7AAAT","error":"Token verification failed"}
2025-07-08 09:19:09.864 [INFO]: ::1 - - [08/Jul/2025:01:19:09 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:19:09.864 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:19:22.557 [INFO]: ::1 - - [08/Jul/2025:01:19:22 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:19:22.558 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:20:04.570 [INFO]: ::1 - - [08/Jul/2025:01:20:04 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:20:04.570 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:20:44.787 [INFO]: ::1 - - [08/Jul/2025:01:20:44 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:20:44.788 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:21:28.252 [INFO]: ::1 - - [08/Jul/2025:01:21:28 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:21:28.252 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:21:48.114 [INFO]: ::1 - - [08/Jul/2025:01:21:48 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:21:48.114 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:22:12.918 [INFO]: WebSocket server initialized
2025-07-08 09:22:12.919 [INFO]: WebSocket server initialized
2025-07-08 09:22:12.932 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:12.932 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:12.936 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:12.938 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:12.939 [INFO]: Database tables created successfully
2025-07-08 09:22:12.941 [INFO]: Database initialized successfully
2025-07-08 09:22:12.942 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:12.966 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:12.966 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:12.966 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:12.975 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.975 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:22:12.976 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:12.976 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 09:22:12.978 [INFO]: HTTP server closed
2025-07-08 09:22:12.979 [INFO]: WebSocket server shutdown completed
2025-07-08 09:22:12.979 [INFO]: WebSocket connections closed
2025-07-08 09:22:47.753 [INFO]: WebSocket server initialized
2025-07-08 09:22:47.754 [INFO]: WebSocket server initialized
2025-07-08 09:22:47.770 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:47.770 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:47.778 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:47.781 [INFO]: Database tables created successfully
2025-07-08 09:22:47.783 [INFO]: Database initialized successfully
2025-07-08 09:22:47.784 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:47.814 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:47.815 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:47.815 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:47.818 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:47.821 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:22:47.821 [INFO]: Environment: development
2025-07-08 09:22:47.822 [INFO]: WebSocket server is ready
2025-07-08 09:22:47.825 [INFO]: Database tables created successfully
2025-07-08 09:22:47.826 [INFO]: Database initialized successfully
2025-07-08 09:22:47.827 [INFO]: Database initialized successfully
2025-07-08 09:22:51.389 [INFO]: ::1 - - [08/Jul/2025:01:22:51 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:22:51.390 [INFO]: GET /health - 200 - 6ms - ::1
2025-07-08 09:22:54.663 [INFO]: WebSocket server initialized
2025-07-08 09:22:54.664 [INFO]: WebSocket server initialized
2025-07-08 09:22:54.676 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:22:54.677 [INFO]: 📦 初始化数据库...
2025-07-08 09:22:54.682 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:54.684 [INFO]: Database tables created successfully
2025-07-08 09:22:54.685 [INFO]: Database initialized successfully
2025-07-08 09:22:54.686 [INFO]: Database initialized successfully
2025-07-08 09:22:54.688 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:22:54.690 [INFO]: Database tables created successfully
2025-07-08 09:22:54.690 [INFO]: Database initialized successfully
2025-07-08 09:22:54.691 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:22:54.714 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:22:54.714 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:22:54.715 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:22:54.726 [ERROR]: 未捕获的异常: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.726 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:22:54.727 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001 {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::1","port":3001}
Error: listen EADDRINUSE: address already in use ::1:3001
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at GetAddrInfoReqWrap.doListen (node:net:2075:7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:8)
2025-07-08 09:22:54.727 [INFO]: Received uncaughtException, starting graceful shutdown...
2025-07-08 09:22:54.728 [INFO]: HTTP server closed
2025-07-08 09:22:54.729 [INFO]: WebSocket server shutdown completed
2025-07-08 09:22:54.729 [INFO]: WebSocket connections closed
2025-07-08 09:23:18.057 [INFO]: WebSocket server initialized
2025-07-08 09:23:18.059 [INFO]: WebSocket server initialized
2025-07-08 09:23:18.068 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:23:18.069 [INFO]: 📦 初始化数据库...
2025-07-08 09:23:18.075 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:18.076 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:18.078 [INFO]: Database tables created successfully
2025-07-08 09:23:18.079 [INFO]: Database tables created successfully
2025-07-08 09:23:18.081 [INFO]: Database initialized successfully
2025-07-08 09:23:18.082 [INFO]: Database initialized successfully
2025-07-08 09:23:18.083 [INFO]: Database initialized successfully
2025-07-08 09:23:18.083 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:23:18.106 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:23:18.106 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:23:18.107 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:23:18.115 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:23:18.116 [INFO]: Environment: development
2025-07-08 09:23:18.116 [INFO]: WebSocket server is ready
2025-07-08 09:23:24.053 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:23:24.054 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:23:24.055 [INFO]: HTTP server closed
2025-07-08 09:23:24.057 [INFO]: WebSocket server shutdown completed
2025-07-08 09:23:24.057 [INFO]: WebSocket connections closed
2025-07-08 09:23:39.169 [INFO]: WebSocket server initialized
2025-07-08 09:23:39.170 [INFO]: WebSocket server initialized
2025-07-08 09:23:39.180 [INFO]: 🚀 启动开发服务器...
2025-07-08 09:23:39.181 [INFO]: 📦 初始化数据库...
2025-07-08 09:23:39.187 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:39.191 [INFO]: Database tables created successfully
2025-07-08 09:23:39.191 [INFO]: Database connection opened: G:\codingProject\talking\backend\data\talking.db
2025-07-08 09:23:39.192 [INFO]: Database initialized successfully
2025-07-08 09:23:39.193 [INFO]: Database initialized successfully
2025-07-08 09:23:39.194 [INFO]: Database tables created successfully
2025-07-08 09:23:39.195 [INFO]: Database initialized successfully
2025-07-08 09:23:39.195 [INFO]: ✅ 数据库初始化完成
2025-07-08 09:23:39.220 [INFO]: 🌐 服务器启动成功（包含WebSocket）
2025-07-08 09:23:39.220 [INFO]: 📍 HTTP地址: http://localhost:3001
2025-07-08 09:23:39.220 [INFO]: 🔌 WebSocket地址: ws://localhost:3001/socket.io
2025-07-08 09:23:39.227 [INFO]: Server is running on http://localhost:3001
2025-07-08 09:23:39.227 [INFO]: Environment: development
2025-07-08 09:23:39.227 [INFO]: WebSocket server is ready
2025-07-08 09:23:42.939 [INFO]: ::1 - - [08/Jul/2025:01:23:42 +0000] "GET /health HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:23:42.940 [INFO]: GET /health - 200 - 7ms - ::1
2025-07-08 09:24:01.492 [INFO]: ::1 - - [08/Jul/2025:01:24:01 +0000] "GET /health HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202"
2025-07-08 09:24:01.492 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:28:14.412 [INFO]: ::1 - - [08/Jul/2025:01:28:14 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:28:14.412 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 09:28:14.432 [WARN]: WebSocket authentication failed {"socketId":"8_XR2tmFBQIAJr-_AAAD","error":"Token verification failed"}
2025-07-08 09:28:20.991 [WARN]: WebSocket authentication failed {"socketId":"B9KAdXKwhGr8srKgAAAF","error":"Token verification failed"}
2025-07-08 09:28:26.928 [WARN]: WebSocket authentication failed {"socketId":"RRbcR3xhhPzvzn-mAAAH","error":"Token verification failed"}
2025-07-08 09:28:34.481 [INFO]: ::1 - - [08/Jul/2025:01:28:34 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:28:34.482 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:28:34.495 [WARN]: WebSocket authentication failed {"socketId":"2vrEqFrfIpWTafzBAAAJ","error":"Token verification failed"}
2025-07-08 09:29:55.530 [INFO]: ::1 - - [08/Jul/2025:01:29:55 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:29:55.531 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:29:55.547 [WARN]: WebSocket authentication failed {"socketId":"T7-m2x7K-21syW-FAAAL","error":"Token verification failed"}
2025-07-08 09:30:16.363 [INFO]: ::1 - - [08/Jul/2025:01:30:16 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:16.364 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:16.381 [WARN]: WebSocket authentication failed {"socketId":"THfATFZzJTzCXEoRAAAN","error":"Token verification failed"}
2025-07-08 09:30:32.400 [INFO]: ::1 - - [08/Jul/2025:01:30:32 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:32.401 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:32.468 [WARN]: WebSocket authentication failed {"socketId":"gdzD1gJT6i1OPOUDAAAP","error":"Token verification failed"}
2025-07-08 09:30:53.539 [INFO]: ::1 - - [08/Jul/2025:01:30:53 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:30:53.539 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:30:53.556 [WARN]: WebSocket authentication failed {"socketId":"bXWDHLGbqs39TzcIAAAR","error":"Token verification failed"}
2025-07-08 09:31:11.960 [INFO]: ::1 - - [08/Jul/2025:01:31:11 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:31:11.961 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:31:11.992 [WARN]: WebSocket authentication failed {"socketId":"bqmskIyz9cb4ebTlAAAT","error":"Token verification failed"}
2025-07-08 09:37:42.691 [INFO]: ::1 - - [08/Jul/2025:01:37:42 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:37:42.691 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:37:42.702 [WARN]: WebSocket authentication failed {"socketId":"H_7_D1wQKPd-zii1AAAV","error":"Token verification failed"}
2025-07-08 09:43:19.567 [INFO]: ::1 - - [08/Jul/2025:01:43:19 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:43:19.567 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:43:19.583 [WARN]: WebSocket authentication failed {"socketId":"Q2xGX0OnllMx9R0FAAAX","error":"Token verification failed"}
2025-07-08 09:43:35.334 [INFO]: ::1 - - [08/Jul/2025:01:43:35 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:43:35.335 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:43:35.344 [WARN]: WebSocket authentication failed {"socketId":"bEV0haHfau6HVq6JAAAZ","error":"Token verification failed"}
2025-07-08 09:47:10.876 [INFO]: ::1 - - [08/Jul/2025:01:47:10 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:10.877 [INFO]: GET /health - 200 - 3ms - ::1
2025-07-08 09:47:10.884 [WARN]: WebSocket authentication failed {"socketId":"PcjOsZYItFO8HYelAAAb","error":"Token verification failed"}
2025-07-08 09:47:13.833 [INFO]: ::1 - - [08/Jul/2025:01:47:13 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:13.833 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:47:13.841 [WARN]: WebSocket authentication failed {"socketId":"CJcxVzKEGo4jnMhkAAAd","error":"Token verification failed"}
2025-07-08 09:47:27.316 [INFO]: ::1 - - [08/Jul/2025:01:47:27 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:47:27.317 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:47:27.332 [WARN]: WebSocket authentication failed {"socketId":"Qa3-CHQRuArmflK1AAAf","error":"Token verification failed"}
2025-07-08 09:48:45.452 [INFO]: ::1 - - [08/Jul/2025:01:48:45 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:48:45.453 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:48:45.471 [WARN]: WebSocket authentication failed {"socketId":"WlOgxJqjh_aC37_XAAAh","error":"Token verification failed"}
2025-07-08 09:49:01.621 [INFO]: ::1 - - [08/Jul/2025:01:49:01 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:01.621 [INFO]: GET /health - 200 - 1ms - ::1
2025-07-08 09:49:01.648 [WARN]: WebSocket authentication failed {"socketId":"N_SpfSZ0r7BkIRIUAAAj","error":"Token verification failed"}
2025-07-08 09:49:19.552 [INFO]: ::1 - - [08/Jul/2025:01:49:19 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:19.553 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:19.567 [WARN]: WebSocket authentication failed {"socketId":"LHea6jtb9ITji3nmAAAl","error":"Token verification failed"}
2025-07-08 09:49:34.533 [INFO]: ::1 - - [08/Jul/2025:01:49:34 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:34.533 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:34.549 [WARN]: WebSocket authentication failed {"socketId":"JWFQXlQ8th4a6BD8AAAn","error":"Token verification failed"}
2025-07-08 09:49:49.962 [INFO]: ::1 - - [08/Jul/2025:01:49:49 +0000] "GET /health HTTP/1.1" 200 121 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:49:49.962 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:49:49.998 [WARN]: WebSocket authentication failed {"socketId":"tPbA1pp8-c09eswWAAAp","error":"Token verification failed"}
2025-07-08 09:50:05.536 [INFO]: ::1 - - [08/Jul/2025:01:50:05 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:05.537 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:05.551 [WARN]: WebSocket authentication failed {"socketId":"euq5g-orUDj6tGxOAAAr","error":"Token verification failed"}
2025-07-08 09:50:20.607 [INFO]: ::1 - - [08/Jul/2025:01:50:20 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:20.608 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:20.625 [WARN]: WebSocket authentication failed {"socketId":"KNMcroly7YTLhfJbAAAt","error":"Token verification failed"}
2025-07-08 09:50:54.476 [INFO]: ::1 - - [08/Jul/2025:01:50:54 +0000] "GET /health HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
2025-07-08 09:50:54.477 [INFO]: GET /health - 200 - 2ms - ::1
2025-07-08 09:50:54.494 [WARN]: WebSocket authentication failed {"socketId":"JLMcU9AwvAysLA2TAAAv","error":"Token verification failed"}
2025-07-08 09:52:06.695 [INFO]: 🛑 正在关闭服务器...
2025-07-08 09:52:06.696 [INFO]: Received SIGINT, starting graceful shutdown...
2025-07-08 09:52:06.697 [INFO]: HTTP server closed
2025-07-08 09:52:06.698 [INFO]: WebSocket server shutdown completed
2025-07-08 09:52:06.698 [INFO]: WebSocket connections closed

/**
 * 消息管理相关路由
 * 
 * 功能说明：
 * 1. 发送消息
 * 2. 获取消息历史
 * 3. 消息搜索和过滤
 * 4. 后台管理消息处理
 * 5. 消息状态管理
 */

import { Router, Request, Response } from 'express'
import { logger } from '@/utils/logger'
import { DatabaseManager } from '@/database/DatabaseManager'
import { validateRequest } from '@/middleware/validation'

const router = Router()
const dbManager = DatabaseManager.getInstance()

// 发送消息验证规则
const sendMessageValidation = {
  content: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 1000
  },
  type: {
    required: false,
    type: 'string',
    enum: ['text', 'voice', 'system', 'backend-call']
  },
  targetUserId: {
    required: false,
    type: 'number',
    min: 1
  },
  priority: {
    required: false,
    type: 'string',
    enum: ['low', 'normal', 'high', 'urgent']
  }
}

// 消息查询验证规则
const messageQueryValidation = {
  page: {
    required: false,
    type: 'number',
    min: 1
  },
  limit: {
    required: false,
    type: 'number',
    min: 1,
    max: 100
  },
  type: {
    required: false,
    type: 'string',
    enum: ['text', 'voice', 'system', 'backend-call', 'all']
  },
  startDate: {
    required: false,
    type: 'string'
  },
  endDate: {
    required: false,
    type: 'string'
  },
  keyword: {
    required: false,
    type: 'string',
    minLength: 1,
    maxLength: 100
  }
}

/**
 * 发送消息
 * POST /api/messages/send
 */
router.post('/send', validateRequest(sendMessageValidation), async (req: Request, res: Response) => {
  try {
    const senderId = (req as any).userId
    const { content, type = 'text', targetUserId, priority = 'normal' } = req.body

    // 创建消息记录
    const messageId = await dbManager.createMessage({
      senderId,
      receiverId: targetUserId || null,
      content,
      type,
      priority,
      status: 'sent',
      createdAt: new Date()
    })

    const message = await dbManager.getMessageById(messageId)
    const sender = await dbManager.getUserById(senderId)

    logger.info(`Message sent: ${sender.username} (ID: ${senderId}) -> ${targetUserId ? `User ${targetUserId}` : 'Broadcast'}`)

    res.json({
      success: true,
      message: 'Message sent successfully',
      data: {
        messageId: message.id,
        content: message.content,
        type: message.type,
        priority: message.priority,
        status: message.status,
        createdAt: message.createdAt,
        sender: {
          id: sender.id,
          username: sender.username,
          displayName: sender.displayName
        }
      }
    })

  } catch (error) {
    logger.error('Send message error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'SEND_MESSAGE_ERROR'
    })
  }
})

/**
 * 获取消息历史
 * GET /api/messages/history
 */
router.get('/history', validateRequest(messageQueryValidation, 'query'), async (req: Request, res: Response) => {
  try {
    const userId = (req as any).userId
    const { 
      page = 1, 
      limit = 20, 
      type = 'all', 
      startDate, 
      endDate, 
      keyword 
    } = req.query

    const queryOptions = {
      userId,
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      type: type as string,
      startDate: startDate ? new Date(startDate as string) : undefined,
      endDate: endDate ? new Date(endDate as string) : undefined,
      keyword: keyword as string
    }

    const result = await dbManager.getMessageHistory(queryOptions)

    res.json({
      success: true,
      message: 'Message history retrieved successfully',
      data: {
        messages: result.messages.map(msg => ({
          id: msg.id,
          content: msg.content,
          type: msg.type,
          priority: msg.priority,
          status: msg.status,
          createdAt: msg.createdAt,
          sender: msg.sender ? {
            id: msg.sender.id,
            username: msg.sender.username,
            displayName: msg.sender.displayName
          } : null,
          receiver: msg.receiver ? {
            id: msg.receiver.id,
            username: msg.receiver.username,
            displayName: msg.receiver.displayName
          } : null
        })),
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / result.limit)
        }
      }
    })

  } catch (error) {
    logger.error('Get message history error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_MESSAGE_HISTORY_ERROR'
    })
  }
})

/**
 * 获取与特定用户的对话历史
 * GET /api/messages/conversation/:targetUserId
 */
router.get('/conversation/:targetUserId', validateRequest(messageQueryValidation, 'query'), async (req: Request, res: Response) => {
  try {
    const userId = (req as any).userId
    const { targetUserId } = req.params
    const { page = 1, limit = 50 } = req.query

    const targetUserIdNum = parseInt(targetUserId)
    if (isNaN(targetUserIdNum)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid target user ID',
        code: 'INVALID_TARGET_USER_ID'
      })
    }

    const queryOptions = {
      userId,
      targetUserId: targetUserIdNum,
      page: parseInt(page as string),
      limit: parseInt(limit as string)
    }

    const result = await dbManager.getConversationHistory(queryOptions)

    res.json({
      success: true,
      message: 'Conversation history retrieved successfully',
      data: {
        messages: result.messages.map(msg => ({
          id: msg.id,
          content: msg.content,
          type: msg.type,
          priority: msg.priority,
          status: msg.status,
          createdAt: msg.createdAt,
          isFromCurrentUser: msg.senderId === userId,
          sender: msg.sender ? {
            id: msg.sender.id,
            username: msg.sender.username,
            displayName: msg.sender.displayName
          } : null
        })),
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / result.limit)
        }
      }
    })

  } catch (error) {
    logger.error('Get conversation history error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_CONVERSATION_ERROR'
    })
  }
})

/**
 * 发送后台管理消息
 * POST /api/messages/backend-call
 */
router.post('/backend-call', validateRequest({
  message: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 500
  },
  priority: {
    required: false,
    type: 'string',
    enum: ['low', 'normal', 'high', 'urgent']
  }
}), async (req: Request, res: Response) => {
  try {
    const senderId = (req as any).userId
    const { message, priority = 'normal' } = req.body

    // 创建后台管理消息记录
    const messageId = await dbManager.createMessage({
      senderId,
      receiverId: null, // 广播消息
      content: message,
      type: 'backend-call',
      priority,
      status: 'sent',
      createdAt: new Date()
    })

    const messageRecord = await dbManager.getMessageById(messageId)
    const sender = await dbManager.getUserById(senderId)

    logger.info(`Backend call message sent: ${sender.username} (ID: ${senderId}) - Priority: ${priority}`)

    res.json({
      success: true,
      message: 'Backend call message sent successfully',
      data: {
        messageId: messageRecord.id,
        content: messageRecord.content,
        type: messageRecord.type,
        priority: messageRecord.priority,
        status: messageRecord.status,
        createdAt: messageRecord.createdAt,
        sender: {
          id: sender.id,
          username: sender.username,
          displayName: sender.displayName
        }
      }
    })

  } catch (error) {
    logger.error('Send backend call message error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'SEND_BACKEND_CALL_ERROR'
    })
  }
})

/**
 * 标记消息为已读
 * PUT /api/messages/:messageId/read
 */
router.put('/:messageId/read', async (req: Request, res: Response) => {
  try {
    const { messageId } = req.params
    const userId = (req as any).userId

    const messageIdNum = parseInt(messageId)
    if (isNaN(messageIdNum)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid message ID',
        code: 'INVALID_MESSAGE_ID'
      })
    }

    const message = await dbManager.getMessageById(messageIdNum)
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found',
        code: 'MESSAGE_NOT_FOUND'
      })
    }

    // 只有接收者可以标记消息为已读
    if (message.receiverId !== userId && message.receiverId !== null) {
      return res.status(403).json({
        success: false,
        message: 'Permission denied',
        code: 'PERMISSION_DENIED'
      })
    }

    await dbManager.updateMessage(messageIdNum, {
      status: 'read',
      readAt: new Date()
    })

    res.json({
      success: true,
      message: 'Message marked as read successfully',
      data: {
        messageId: messageIdNum,
        status: 'read',
        readAt: new Date()
      }
    })

  } catch (error) {
    logger.error('Mark message as read error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'MARK_READ_ERROR'
    })
  }
})

/**
 * 获取未读消息数量
 * GET /api/messages/unread-count
 */
router.get('/unread-count', async (req: Request, res: Response) => {
  try {
    const userId = (req as any).userId
    const unreadCount = await dbManager.getUnreadMessageCount(userId)

    res.json({
      success: true,
      message: 'Unread message count retrieved successfully',
      data: {
        unreadCount
      }
    })

  } catch (error) {
    logger.error('Get unread message count error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'GET_UNREAD_COUNT_ERROR'
    })
  }
})

export default router

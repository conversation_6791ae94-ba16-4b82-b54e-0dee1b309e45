import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 用户信息接口定义
export interface UserInfo {
  id?: number // 用户ID（后端返回）
  username: string
  displayName?: string // 显示名称
  loginTime: number
  isLoggedIn: boolean
  lastActiveTime?: number
  permissions?: {
    microphone: boolean
    speaker: boolean
  }
}

// 用户状态枚举
export enum UserStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  TALKING = 'talking',
  BUSY = 'busy'
}

// 用户状态管理Store
export const useUserStore = defineStore('user', () => {
  // 响应式状态
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref(false)
  const userStatus = ref<UserStatus>(UserStatus.OFFLINE)
  const sessionToken = ref<string>('')
  const lastActiveTime = ref<number>(Date.now())
  const permissions = ref({
    microphone: false,
    speaker: false
  })

  // 计算属性
  const username = computed(() => userInfo.value?.username || '')
  const isOnline = computed(() => userStatus.value !== UserStatus.OFFLINE)
  const isTalking = computed(() => userStatus.value === UserStatus.TALKING)
  const canStartCall = computed(() => 
    isLoggedIn.value && 
    userStatus.value === UserStatus.ONLINE && 
    permissions.value.microphone
  )

  // 会话超时时间（30分钟）
  const SESSION_TIMEOUT = 30 * 60 * 1000

  // 登录方法 - 调用后端API
  const login = async (username: string): Promise<boolean> => {
    try {
      console.log('开始登录流程:', username)

      // 调用后端登录API
      const response = await uni.request({
        url: 'http://localhost:3001/api/auth/login',
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          username: username.trim()
        }
      })

      console.log('登录API响应:', response)

      if (response.statusCode === 200 && response.data?.success) {
        const loginData = response.data.data
        const loginTime = Date.now()

        const newUserInfo: UserInfo = {
          id: loginData.user.id,
          username: loginData.user.username,
          displayName: loginData.user.displayName || loginData.user.username,
          loginTime,
          isLoggedIn: true,
          lastActiveTime: loginTime,
          permissions: loginData.user.permissions || permissions.value
        }

        // 保存JWT token和用户信息到本地存储
        uni.setStorageSync('userInfo', newUserInfo)
        uni.setStorageSync('sessionToken', loginData.token) // 使用后端返回的JWT token
        uni.setStorageSync('refreshToken', loginData.refreshToken)

        // 更新状态
        userInfo.value = newUserInfo
        isLoggedIn.value = true
        userStatus.value = UserStatus.ONLINE
        sessionToken.value = loginData.token
        lastActiveTime.value = loginTime

        console.log('用户登录成功:', username, '用户ID:', loginData.user.id)
        return true
      } else {
        console.error('登录失败:', response.data?.message || '未知错误')
        return false
      }
    } catch (error) {
      console.error('登录请求失败:', error)
      return false
    }
  }

  // 登出方法
  const logout = async (): Promise<void> => {
    try {
      // 先调用后端登出API更新数据库状态
      if (sessionToken.value) {
        try {
          await uni.request({
            url: 'http://localhost:3001/api/auth/logout',
            method: 'POST',
            header: {
              'Authorization': `Bearer ${sessionToken.value}`,
              'Content-Type': 'application/json'
            }
          })
          console.log('后端登出API调用成功')
        } catch (apiError) {
          console.warn('后端登出API调用失败，继续前端清理:', apiError)
          // 即使API调用失败也要继续前端清理
        }
      }

      // 清除本地存储
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('sessionToken')
      uni.removeStorageSync('refreshToken')
      uni.removeStorageSync('hasUsedBefore')

      // 重置状态
      userInfo.value = null
      isLoggedIn.value = false
      userStatus.value = UserStatus.OFFLINE
      sessionToken.value = ''
      permissions.value = {
        microphone: false,
        speaker: false
      }

      console.log('用户登出成功')
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  // 更新用户状态
  const updateUserStatus = (status: UserStatus): void => {
    userStatus.value = status
    updateLastActiveTime()
    
    // 同步到本地存储
    if (userInfo.value) {
      userInfo.value.lastActiveTime = lastActiveTime.value
      uni.setStorageSync('userInfo', userInfo.value)
    }
    
    console.log('用户状态更新:', status)
  }

  // 更新最后活跃时间
  const updateLastActiveTime = (): void => {
    lastActiveTime.value = Date.now()
  }

  // 检查会话是否有效
  const isSessionValid = (): boolean => {
    if (!isLoggedIn.value || !userInfo.value) {
      return false
    }

    const now = Date.now()
    const timeSinceLastActive = now - lastActiveTime.value

    return timeSinceLastActive < SESSION_TIMEOUT
  }

  // 刷新会话
  const refreshSession = (): void => {
    if (isLoggedIn.value) {
      updateLastActiveTime()
      sessionToken.value = generateSessionToken()
      uni.setStorageSync('sessionToken', sessionToken.value)
    }
  }

  // 自动登出（会话超时）
  const autoLogout = async (shouldRedirect: boolean = true): Promise<void> => {
    console.log('会话超时，自动登出')
    await logout()
    
    // 只有在需要时才跳转到登录页（避免初始化时的跳转冲突）
    if (shouldRedirect) {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }
  }

  // 检查并处理会话超时
  const checkSessionTimeout = (): void => {
    if (isLoggedIn.value && !isSessionValid()) {
      // 非应用启动时的会话超时，需要重定向到登录页
      autoLogout(true)
    }
  }

  // 设置权限
  const setPermissions = (newPermissions: Partial<typeof permissions.value>): void => {
    permissions.value = { ...permissions.value, ...newPermissions }
    
    // 更新用户信息中的权限
    if (userInfo.value) {
      userInfo.value.permissions = permissions.value
      uni.setStorageSync('userInfo', userInfo.value)
    }
  }

  // 初始化用户状态（从本地存储恢复）
  const initializeUserState = (isAppStartup: boolean = false): void => {
    try {
      const storedUserInfo = uni.getStorageSync('userInfo')
      const storedToken = uni.getStorageSync('sessionToken')

      if (storedUserInfo && storedToken) {
        userInfo.value = storedUserInfo
        sessionToken.value = storedToken
        isLoggedIn.value = storedUserInfo.isLoggedIn
        lastActiveTime.value = storedUserInfo.lastActiveTime || Date.now()
        
        if (storedUserInfo.permissions) {
          permissions.value = storedUserInfo.permissions
        }

        // 检查会话是否仍然有效
        if (isSessionValid()) {
          userStatus.value = UserStatus.ONLINE
          console.log('用户状态已恢复:', storedUserInfo.username)
        } else {
          // 会话已过期，清除状态
          // 如果是应用启动时，不进行页面跳转
          autoLogout(!isAppStartup)
        }
      } else {
        // 没有存储的用户信息，确保状态为未登录
        console.log('未找到用户登录信息，保持未登录状态')
      }
    } catch (error) {
      console.error('初始化用户状态失败:', error)
    }
  }

  // 生成会话Token
  const generateSessionToken = (): string => {
    const timestamp = Date.now().toString()
    const random = Math.random().toString(36).substring(2)
    return `${timestamp}_${random}`
  }

  // 获取用户显示信息
  const getUserDisplayInfo = () => {
    return {
      username: username.value,
      status: userStatus.value,
      isOnline: isOnline.value,
      isTalking: isTalking.value,
      loginTime: userInfo.value?.loginTime,
      lastActiveTime: lastActiveTime.value
    }
  }

  // 验证用户权限
  const hasPermission = (permission: keyof typeof permissions.value): boolean => {
    return permissions.value[permission]
  }

  // 请求权限
  const requestPermission = async (permission: keyof typeof permissions.value): Promise<boolean> => {
    try {
      let granted = false

      if (permission === 'microphone') {
        // 请求麦克风权限
        granted = await requestMicrophonePermission()
      } else if (permission === 'speaker') {
        // 音频播放权限通常不需要特殊申请
        granted = true
      }

      if (granted) {
        setPermissions({ [permission]: true })
      }

      return granted
    } catch (error) {
      console.error(`请求${permission}权限失败:`, error)
      return false
    }
  }

  // 请求麦克风权限
  const requestMicrophonePermission = (): Promise<boolean> => {
    return new Promise((resolve) => {
      // #ifdef H5
      // H5环境下使用浏览器原生API
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ audio: true })
          .then(() => {
            console.log('麦克风权限申请成功 (H5)')
            resolve(true)
          })
          .catch((error) => {
            console.error('麦克风权限申请失败 (H5):', error)
            resolve(false)
          })
      } else {
        console.warn('浏览器不支持 getUserMedia API')
        resolve(false)
      }
      // #endif
      
      // #ifndef H5
      // 非H5环境使用uni.authorize
      uni.authorize({
        scope: 'scope.record',
        success: () => {
          console.log('麦克风权限申请成功')
          resolve(true)
        },
        fail: (error) => {
          console.error('麦克风权限申请失败:', error)
          resolve(false)
        }
      })
      // #endif
    })
  }

  // 检查登录状态
  const checkLoginStatus = async (): Promise<void> => {
    try {
      // 初始化用户状态（从本地存储恢复）
      // 传入true表示这是应用启动时的初始化，避免页面跳转冲突
      initializeUserState(true)
      console.log('登录状态检查完成，当前状态:', isLoggedIn.value)
    } catch (error) {
      console.error('检查登录状态失败:', error)
      // 确保在错误情况下状态为未登录
      isLoggedIn.value = false
      userStatus.value = UserStatus.OFFLINE
    }
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    userStatus,
    sessionToken,
    lastActiveTime,
    permissions,
    
    // 计算属性
    username,
    isOnline,
    isTalking,
    canStartCall,
    
    // 方法
    login,
    logout,
    updateUserStatus,
    updateLastActiveTime,
    isSessionValid,
    refreshSession,
    autoLogout,
    checkSessionTimeout,
    setPermissions,
    initializeUserState,
    checkLoginStatus,
    getUserDisplayInfo,
    hasPermission,
    requestPermission
  }
})

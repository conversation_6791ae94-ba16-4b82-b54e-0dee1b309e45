/**
 * 营业厅语音对讲系统后端主应用文件
 * 
 * 功能说明：
 * 1. Express服务器初始化和配置
 * 2. 中间件配置（CORS、安全、日志等）
 * 3. 路由配置和API接口
 * 4. WebSocket服务器配置
 * 5. 数据库连接和初始化
 * 6. 错误处理和日志记录
 * 7. 服务器启动和优雅关闭
 */

import express from 'express'
import { createServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'
import path from 'path'

// 导入自定义模块
import { logger } from '@/utils/logger'
import { config } from '@/config/config'
import { globalErrorHandler, notFoundHandler } from '@/middleware/errorHandler'
import { authMiddleware } from '@/middleware/auth'
import { DatabaseManager } from '@/database/DatabaseManager'
import { WebSocketManager } from '@/websocket/WebSocketManager'

// 导入路由
import authRoutes from '@/routes/auth'
import userRoutes from '@/routes/user'
import messageRoutes from '@/routes/message'
import systemRoutes from '@/routes/system'

// 加载环境变量
dotenv.config()

// 扩展Express Request接口
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      id?: string
      user?: UserInfo
    }
  }
}

// 用户信息接口
interface UserInfo {
  id: number
  username: string
  displayName?: string
  status: string
}

class Application {
  public app: express.Application
  public server: any
  public io: SocketIOServer
  private dbManager: DatabaseManager
  private wsManager: WebSocketManager

  constructor() {
    this.app = express()
    this.server = createServer(this.app)
    // 使用Socket.io最佳实践配置
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: config.websocket.corsOrigin,
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling'],
      // 连接状态恢复配置（最佳实践）
      connectionStateRecovery: {
        maxDisconnectionDuration: 2 * 60 * 1000, // 2分钟内可恢复
        skipMiddlewares: true, // 跳过中间件以提高恢复性能
      },
      // 心跳配置优化
      pingTimeout: 20000,  // 20秒心跳超时
      pingInterval: 25000, // 25秒心跳间隔
      // 性能优化配置
      maxHttpBufferSize: 1e6, // 1MB最大HTTP缓冲区
      connectTimeout: 45000,  // 45秒连接超时
      upgradeTimeout: 10000,  // 10秒WebSocket升级超时
      // 启用压缩
      compression: true,
      // 禁用尾随斜杠（可选）
      addTrailingSlash: false
    })

    this.dbManager = DatabaseManager.getInstance()
    this.wsManager = new WebSocketManager(this.io)

    this.initializeMiddleware()
    this.initializeRoutes()
    this.initializeErrorHandling()
    this.initializeWebSocket()
    // 注意：数据库初始化将在start()方法中异步执行
  }

  /**
   * 初始化中间件
   */
  private initializeMiddleware(): void {
    // 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "ws:", "wss:"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"]
        }
      },
      crossOriginEmbedderPolicy: false
    }))

    // CORS配置
    this.app.use(cors({
      origin: config.server.corsOrigin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }))

    // 压缩中间件
    this.app.use(compression())

    // 请求解析中间件
    this.app.use(express.json({ limit: '10mb' }))
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }))

    // 静态文件服务
    this.app.use('/uploads', express.static(path.join(__dirname, '../uploads')))
    this.app.use('/public', express.static(path.join(__dirname, '../public')))

    // 日志中间件
    this.app.use(morgan('combined', {
      stream: {
        write: (message: string) => {
          logger.info(message.trim())
        }
      }
    }))

    // 请求限制中间件
    const limiter = rateLimit({
      windowMs: config.security.rateLimitWindowMs, // 15分钟
      max: config.security.rateLimitMaxRequests, // 限制每个IP 100个请求
      message: {
        error: 'Too many requests from this IP, please try again later.',
        code: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false
    })
    this.app.use('/api/', limiter)

    // 请求ID中间件
    this.app.use((req, res, next) => {
      req.id = Math.random().toString(36).substr(2, 9)
      res.setHeader('X-Request-ID', req.id)
      next()
    })

    // 请求日志中间件
    this.app.use((req, res, next) => {
      const start = Date.now()
      res.on('finish', () => {
        const duration = Date.now() - start
        logger.info(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms - ${req.ip}`)
      })
      next()
    })
  }

  /**
   * 初始化路由
   */
  private initializeRoutes(): void {
    // 健康检查接口
    this.app.get('/health', (_, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env['npm_package_version'] || '1.0.0',
        environment: config.server.nodeEnv
      })
    })

    // API路由
    this.app.use('/api/auth', authRoutes)
    this.app.use('/api/users', authMiddleware, userRoutes)
    this.app.use('/api/messages', authMiddleware, messageRoutes)
    this.app.use('/api/system', authMiddleware, systemRoutes)

    // 根路径
    this.app.get('/', (_, res) => {
      res.json({
        name: '营业厅语音对讲系统',
        version: '1.0.0',
        description: 'Real-time voice intercom system for business hall',
        endpoints: {
          health: '/health',
          api: '/api',
          websocket: '/socket.io'
        }
      })
    })
  }

  /**
   * 初始化错误处理
   */
  private initializeErrorHandling(): void {
    // 404处理
    this.app.use(notFoundHandler)
    
    // 全局错误处理
    this.app.use(globalErrorHandler)
  }

  /**
   * 初始化数据库
   */
  private async initializeDatabase(): Promise<void> {
    try {
      await this.dbManager.initialize()
      logger.info('Database initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize database:', error)
      process.exit(1)
    }
  }

  /**
   * 初始化WebSocket
   */
  private initializeWebSocket(): void {
    this.wsManager.initialize()
    logger.info('WebSocket server initialized')
  }

  /**
   * 启动服务器
   */
  public async start(): Promise<void> {
    try {
      // 首先初始化数据库
      await this.initializeDatabase()

      const port = config.server.port
      const host = config.server.host

      this.server.listen(port, host, () => {
        logger.info(`Server is running on http://${host}:${port}`)
        logger.info(`Environment: ${config.server.nodeEnv}`)
        logger.info(`WebSocket server is ready`)
        logger.info(`Database initialized and ready`)

        // 发送就绪信号给PM2
        if (process.send) {
          process.send('ready')
        }
      })

      // 优雅关闭处理
      this.setupGracefulShutdown()
    } catch (error) {
      logger.error('Failed to start server:', error)
      process.exit(1)
    }
  }

  /**
   * 设置优雅关闭
   */
  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`)
      
      // 停止接受新连接
      this.server.close(async () => {
        logger.info('HTTP server closed')
        
        try {
          // 关闭WebSocket连接
          this.wsManager.shutdown()
          logger.info('WebSocket connections closed')
          
          // 关闭数据库连接
          await this.dbManager.close()
          logger.info('Database connections closed')
          
          logger.info('Graceful shutdown completed')
          process.exit(0)
        } catch (error) {
          logger.error('Error during graceful shutdown:', error)
          process.exit(1)
        }
      })

      // 强制关闭超时
      setTimeout(() => {
        logger.error('Graceful shutdown timeout, forcing exit')
        process.exit(1)
      }, 30000)
    }

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))
    
    // 监听未捕获的异常
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error)
      gracefulShutdown('uncaughtException')
    })
    
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason)
      gracefulShutdown('unhandledRejection')
    })
  }
}

// 创建应用实例
const application = new Application()

// 导出应用实例的 app 属性，这样可以在其他地方使用
export default application

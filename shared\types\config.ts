/**
 * 共享配置类型定义
 * 
 * 功能说明：
 * 1. 统一前后端的配置类型定义
 * 2. 环境配置管理
 * 3. 配置验证规则
 * 4. 配置默认值
 * 5. 配置更新机制
 */

// 环境类型枚举
export enum Environment {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  STAGING = 'staging',
  PRODUCTION = 'production'
}

// 日志级别枚举
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose'
}

// 音频格式枚举
export enum AudioFormat {
  AAC = 'aac',
  MP3 = 'mp3',
  WAV = 'wav',
  OGG = 'ogg'
}

// 服务器配置接口
export interface ServerConfig {
  port: number
  host: string
  environment: Environment
  corsOrigin: string[]
  trustProxy: boolean
  requestTimeout: number
  bodyLimit: string
  rateLimitWindowMs: number
  rateLimitMaxRequests: number
}

// 数据库配置接口
export interface DatabaseConfig {
  filename: string
  maxConnections: number
  connectionTimeout: number
  queryTimeout: number
  enableWAL: boolean
  enableForeignKeys: boolean
  backupInterval: number
  backupRetention: number
}

// WebSocket配置接口
export interface WebSocketConfig {
  corsOrigin: string[]
  pingTimeout: number
  pingInterval: number
  maxConnections: number
  compressionEnabled: boolean
  heartbeatInterval: number
  reconnectAttempts: number
  reconnectDelay: number
}

// 安全配置接口
export interface SecurityConfig {
  jwtSecret: string
  jwtRefreshSecret: string
  jwtExpiresIn: string
  jwtRefreshExpiresIn: string
  jwtIssuer: string
  jwtAudience: string
  bcryptRounds: number
  sessionTimeout: number
  maxLoginAttempts: number
  lockoutDuration: number
  enableCSRF: boolean
  enableHelmet: boolean
}

// 音频配置接口
export interface AudioConfig {
  sampleRate: number
  bitRate: number
  channels: number
  format: AudioFormat
  frameSize: number
  maxDuration: number
  uploadPath: string
  maxFileSize: number
  enableCompression: boolean
  compressionQuality: number
  enableNoiseReduction: boolean
  enableEchoCancellation: boolean
}

// 日志配置接口
export interface LoggingConfig {
  level: LogLevel
  file: string
  maxSize: string
  maxFiles: number
  datePattern: string
  enableConsole: boolean
  enableFile: boolean
  enableRemote: boolean
  remoteEndpoint?: string
  format: string
}

// 系统配置接口
export interface SystemConfig {
  maxConcurrentCalls: number
  callTimeout: number
  heartbeatInterval: number
  userTimeout: number
  messageRetention: number
  enableMetrics: boolean
  metricsInterval: number
  enableHealthCheck: boolean
  healthCheckInterval: number
  maintenanceMode: boolean
}

// 缓存配置接口
export interface CacheConfig {
  enabled: boolean
  type: 'memory' | 'redis'
  host?: string
  port?: number
  password?: string
  database?: number
  ttl: number
  maxKeys: number
  enableCompression: boolean
}

// 通知配置接口
export interface NotificationConfig {
  enabled: boolean
  providers: {
    email?: {
      enabled: boolean
      smtp: {
        host: string
        port: number
        secure: boolean
        auth: {
          user: string
          pass: string
        }
      }
      from: string
      templates: Record<string, string>
    }
    sms?: {
      enabled: boolean
      provider: string
      apiKey: string
      apiSecret: string
      from: string
    }
    push?: {
      enabled: boolean
      fcmServerKey: string
      apnsCertificate: string
      apnsKey: string
    }
  }
  retryAttempts: number
  retryDelay: number
}

// 监控配置接口
export interface MonitoringConfig {
  enabled: boolean
  endpoint?: string
  apiKey?: string
  sampleRate: number
  enableTracing: boolean
  enableProfiling: boolean
  alertThresholds: {
    errorRate: number
    responseTime: number
    memoryUsage: number
    cpuUsage: number
  }
}

// 前端配置接口
export interface FrontendConfig {
  apiBaseUrl: string
  websocketUrl: string
  enableDevTools: boolean
  enableMocking: boolean
  theme: {
    primaryColor: string
    secondaryColor: string
    darkMode: boolean
  }
  features: {
    voiceChat: boolean
    fileUpload: boolean
    notifications: boolean
    analytics: boolean
  }
  performance: {
    enableLazyLoading: boolean
    enableCodeSplitting: boolean
    enableServiceWorker: boolean
    cacheStrategy: 'cache-first' | 'network-first' | 'stale-while-revalidate'
  }
}

// 完整应用配置接口
export interface AppConfig {
  server: ServerConfig
  database: DatabaseConfig
  websocket: WebSocketConfig
  security: SecurityConfig
  audio: AudioConfig
  logging: LoggingConfig
  system: SystemConfig
  cache: CacheConfig
  notification: NotificationConfig
  monitoring: MonitoringConfig
  frontend: FrontendConfig
}

// 配置验证规则接口
export interface ConfigValidationRule {
  field: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  required: boolean
  min?: number
  max?: number
  pattern?: RegExp
  enum?: string[]
  validator?: (value: any) => boolean
}

// 配置更新事件接口
export interface ConfigUpdateEvent {
  field: string
  oldValue: any
  newValue: any
  timestamp: number
  source: 'file' | 'env' | 'api' | 'cli'
  userId?: number
}

// 配置源接口
export interface ConfigSource {
  type: 'file' | 'env' | 'remote' | 'database'
  priority: number
  path?: string
  url?: string
  refreshInterval?: number
}

// 配置常量
export const CONFIG_CONSTANTS = {
  DEFAULT_PORT: 3000,
  DEFAULT_HOST: 'localhost',
  DEFAULT_ENVIRONMENT: Environment.DEVELOPMENT,
  DEFAULT_LOG_LEVEL: LogLevel.INFO,
  DEFAULT_AUDIO_FORMAT: AudioFormat.AAC,
  DEFAULT_SAMPLE_RATE: 16000,
  DEFAULT_BIT_RATE: 64000,
  DEFAULT_CHANNELS: 1,
  DEFAULT_JWT_EXPIRES_IN: '24h',
  DEFAULT_JWT_REFRESH_EXPIRES_IN: '7d',
  DEFAULT_SESSION_TIMEOUT: 5 * 60 * 1000, // 5分钟（缩短超时时间）
  DEFAULT_HEARTBEAT_INTERVAL: 10000, // 10秒（更频繁的检查）
  DEFAULT_PING_TIMEOUT: 5000, // 5秒
  DEFAULT_PING_INTERVAL: 25000, // 25秒
  DEFAULT_MAX_CONNECTIONS: 100,
  DEFAULT_RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15分钟
  DEFAULT_RATE_LIMIT_MAX_REQUESTS: 100,
  DEFAULT_BCRYPT_ROUNDS: 12,
  DEFAULT_MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  DEFAULT_MESSAGE_RETENTION: 30, // 30天
  DEFAULT_CACHE_TTL: 3600, // 1小时
  DEFAULT_RETRY_ATTEMPTS: 3,
  DEFAULT_RETRY_DELAY: 2000 // 2秒
} as const

// 默认配置值
export const DEFAULT_CONFIG: AppConfig = {
  server: {
    port: CONFIG_CONSTANTS.DEFAULT_PORT,
    host: CONFIG_CONSTANTS.DEFAULT_HOST,
    environment: CONFIG_CONSTANTS.DEFAULT_ENVIRONMENT,
    corsOrigin: ['http://localhost:8080', 'http://127.0.0.1:8080'],
    trustProxy: false,
    requestTimeout: 30000,
    bodyLimit: '10mb',
    rateLimitWindowMs: CONFIG_CONSTANTS.DEFAULT_RATE_LIMIT_WINDOW,
    rateLimitMaxRequests: CONFIG_CONSTANTS.DEFAULT_RATE_LIMIT_MAX_REQUESTS
  },
  database: {
    filename: 'talking.db',
    maxConnections: 10,
    connectionTimeout: 30000,
    queryTimeout: 10000,
    enableWAL: true,
    enableForeignKeys: true,
    backupInterval: 24 * 60 * 60 * 1000, // 24小时
    backupRetention: 7 // 7天
  },
  websocket: {
    corsOrigin: ['http://localhost:8080', 'http://127.0.0.1:8080'],
    pingTimeout: CONFIG_CONSTANTS.DEFAULT_PING_TIMEOUT,
    pingInterval: CONFIG_CONSTANTS.DEFAULT_PING_INTERVAL,
    maxConnections: CONFIG_CONSTANTS.DEFAULT_MAX_CONNECTIONS,
    compressionEnabled: true,
    heartbeatInterval: CONFIG_CONSTANTS.DEFAULT_HEARTBEAT_INTERVAL,
    reconnectAttempts: 5,
    reconnectDelay: 3000
  },
  security: {
    jwtSecret: 'your-super-secret-jwt-key-change-in-production',
    jwtRefreshSecret: 'your-super-secret-refresh-key-change-in-production',
    jwtExpiresIn: CONFIG_CONSTANTS.DEFAULT_JWT_EXPIRES_IN,
    jwtRefreshExpiresIn: CONFIG_CONSTANTS.DEFAULT_JWT_REFRESH_EXPIRES_IN,
    jwtIssuer: 'talking-system',
    jwtAudience: 'talking-client',
    bcryptRounds: CONFIG_CONSTANTS.DEFAULT_BCRYPT_ROUNDS,
    sessionTimeout: CONFIG_CONSTANTS.DEFAULT_SESSION_TIMEOUT,
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15分钟
    enableCSRF: true,
    enableHelmet: true
  },
  audio: {
    sampleRate: CONFIG_CONSTANTS.DEFAULT_SAMPLE_RATE,
    bitRate: CONFIG_CONSTANTS.DEFAULT_BIT_RATE,
    channels: CONFIG_CONSTANTS.DEFAULT_CHANNELS,
    format: CONFIG_CONSTANTS.DEFAULT_AUDIO_FORMAT,
    frameSize: 1024,
    maxDuration: 60000, // 60秒
    uploadPath: './uploads/audio',
    maxFileSize: CONFIG_CONSTANTS.DEFAULT_MAX_FILE_SIZE,
    enableCompression: true,
    compressionQuality: 0.8,
    enableNoiseReduction: true,
    enableEchoCancellation: true
  },
  logging: {
    level: CONFIG_CONSTANTS.DEFAULT_LOG_LEVEL,
    file: './logs/app-%DATE%.log',
    maxSize: '20m',
    maxFiles: 14,
    datePattern: 'YYYY-MM-DD',
    enableConsole: true,
    enableFile: true,
    enableRemote: false,
    format: 'json'
  },
  system: {
    maxConcurrentCalls: 8,
    callTimeout: 300000, // 5分钟
    heartbeatInterval: CONFIG_CONSTANTS.DEFAULT_HEARTBEAT_INTERVAL,
    userTimeout: CONFIG_CONSTANTS.DEFAULT_SESSION_TIMEOUT,
    messageRetention: CONFIG_CONSTANTS.DEFAULT_MESSAGE_RETENTION,
    enableMetrics: true,
    metricsInterval: 60000, // 1分钟
    enableHealthCheck: true,
    healthCheckInterval: 30000, // 30秒
    maintenanceMode: false
  },
  cache: {
    enabled: true,
    type: 'memory',
    ttl: CONFIG_CONSTANTS.DEFAULT_CACHE_TTL,
    maxKeys: 1000,
    enableCompression: true
  },
  notification: {
    enabled: false,
    providers: {},
    retryAttempts: CONFIG_CONSTANTS.DEFAULT_RETRY_ATTEMPTS,
    retryDelay: CONFIG_CONSTANTS.DEFAULT_RETRY_DELAY
  },
  monitoring: {
    enabled: false,
    sampleRate: 0.1,
    enableTracing: false,
    enableProfiling: false,
    alertThresholds: {
      errorRate: 0.05,
      responseTime: 2000,
      memoryUsage: 0.8,
      cpuUsage: 0.8
    }
  },
  frontend: {
    apiBaseUrl: 'http://localhost:3001/api',
    websocketUrl: 'ws://localhost:3001',
    enableDevTools: true,
    enableMocking: false,
    theme: {
      primaryColor: '#667eea',
      secondaryColor: '#764ba2',
      darkMode: false
    },
    features: {
      voiceChat: true,
      fileUpload: true,
      notifications: true,
      analytics: false
    },
    performance: {
      enableLazyLoading: true,
      enableCodeSplitting: true,
      enableServiceWorker: false,
      cacheStrategy: 'stale-while-revalidate'
    }
  }
}

// 类型守卫函数
export const isValidEnvironment = (env: string): env is Environment => {
  return Object.values(Environment).includes(env as Environment)
}

export const isValidLogLevel = (level: string): level is LogLevel => {
  return Object.values(LogLevel).includes(level as LogLevel)
}

export const isValidAudioFormat = (format: string): format is AudioFormat => {
  return Object.values(AudioFormat).includes(format as AudioFormat)
}

// 工具类型
export type ConfigPath = string
export type ConfigValue = string | number | boolean | object | null | undefined
export type ConfigChangeHandler = (path: ConfigPath, oldValue: ConfigValue, newValue: ConfigValue) => void

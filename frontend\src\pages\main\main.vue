<template>
  <view class="main-container">
    <!-- 顶部状态栏 -->
    <view class="status-bar">
      <view class="user-info-card">
        <!-- 用户基本信息 -->
        <view class="user-basic-info">
          <view class="user-avatar">
            <text class="avatar-text">{{ userStore.username.charAt(0).toUpperCase() }}</text>
          </view>
          <view class="user-details">
            <text class="welcome-text">欢迎，{{ userStore.username }}</text>
            <view class="status-row">
              <!-- 用户状态 -->
              <view class="status-indicator" :class="statusClass">
                <text class="status-dot"></text>
                <text class="status-text">{{ statusText }}</text>
              </view>
              <!-- 连接状态 -->
              <view class="connection-indicator" :class="connectionClass">
                <text class="connection-dot"></text>
                <text class="connection-text">{{ connectionText }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <Button @click="handleRefresh" size="small" type="default" :loading="isLoading">
            {{ isWebSocketConnected ? '刷新列表' : '重新连接' }}
          </Button>
          <Button @click="handleTestSocket" size="small" type="primary">
            测试连接
          </Button>
          <Button @click="handleLogout" size="small" type="danger">
            退出
          </Button>
        </view>
      </view>
    </view>

    <!-- 后台管理消息卡片（置顶显示） -->
    <view v-if="backendMessage" class="backend-message-section">
      <view class="section-title">
        <text class="title-icon">📢</text>
        <text class="title-text">后台管理消息</text>
        <text class="message-time">{{ formatTime(backendMessage.timestamp) }}</text>
      </view>
      <MessageCard
        :id="backendMessage.id"
        :type="MessageType.BACKEND_CALL"
        :title="backendMessage.from || '后台管理'"
        :content="backendMessage.content"
        :timestamp="backendMessage.timestamp"
        :status="MessageStatus.UNREAD"
        :clickable="true"
        @click="handleBackendMessageClick"
        class="backend-message-card"
      />
    </view>

    <!-- 在线服务人员列表 -->
    <view class="online-users-section">
      <view class="section-title">
        <text class="title-icon">👥</text>
        <text class="title-text">在线服务人员</text>
        <text class="count-badge">{{ onlineUsers.length }}</text>
      </view>

      <!-- 用户列表 - 瀑布流布局 -->
      <view class="users-waterfall">
        <view
          v-for="user in onlineUsers"
          :key="user.id"
          class="user-item"
          @click="handleUserClick(user)"
        >
          <UserCard
            :user="user"
            :show-status="true"
            :clickable="true"
            class="user-card"
          />
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="onlineUsers.length === 0" class="empty-state">
        <text class="empty-icon">😴</text>
        <text class="empty-text">暂无在线服务人员</text>
        <Button @click="handleRefresh" size="small" type="primary">
          刷新列表
        </Button>
      </view>
    </view>

    <!-- 加载状态 -->
    <Loading v-if="isLoading" />

    <!-- Toast提示 -->
    <Toast 
      v-model:visible="toastVisible" 
      :message="toastMessage" 
      :type="toastType" 
      :duration="3000"
    />

    <!-- 后台消息管理 -->
    <BackendMessageManager
      :show-overlay="true"
      :show-notification="true"
      :show-message-list="false"
      @message-received="handleBackendMessageReceived"
      @urgent-message="handleUrgentMessage"
    />
  </view>
</template>

<script setup lang="ts">
// 导入必要的模块
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useSocketStore } from '@/stores/socket'
import Toast from '@/components/Toast/Toast.vue'
import { Button, MessageCard, UserCard, Loading, BackendMessageManager, MessageType, MessageStatus } from '@/components'
import { UserStatus } from '@/stores/user'
import { getFrontendConfig } from '@/config/index'

// 使用状态管理
const userStore = useUserStore()
const socketStore = useSocketStore()

// 响应式数据
const isLoading = ref(false)
const backendMessage = ref<any>(null)

// Toast相关状态
const toastVisible = ref(false)
const toastMessage = ref('')
const toastType = ref<'success' | 'error' | 'info' | 'warning'>('info')

// 使用Socket store的状态
const {
  isConnected: isWebSocketConnected,
  onlineUsers,
  messages,
  connect: connectSocket,
  disconnect: disconnectSocket
} = socketStore

// 计算属性
const statusClass = computed(() => {
  switch (userStore.userStatus) {
    case UserStatus.ONLINE:
      return 'status-online'
    case UserStatus.TALKING:
      return 'status-talking'
    case UserStatus.BUSY:
      return 'status-busy'
    default:
      return 'status-offline'
  }
})

const statusText = computed(() => {
  switch (userStore.userStatus) {
    case UserStatus.ONLINE:
      return '在线'
    case UserStatus.TALKING:
      return '通话中'
    case UserStatus.BUSY:
      return '忙碌'
    default:
      return '离线'
  }
})

// 连接状态样式类
const connectionClass = computed(() => {
  return isWebSocketConnected.value ? 'connection-online' : 'connection-offline'
})

// 连接状态文本
const connectionText = computed(() => {
  return isWebSocketConnected.value ? '服务已连接' : '离线模式'
})

// 页面加载
onMounted(async () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.reLaunch({
      url: '/pages/login/login'
    })
    return
  }

  // 初始化页面
  await initializePage()
})

// 页面卸载
onUnmounted(() => {
  cleanup()
})

// 初始化页面
const initializePage = async () => {
  try {
    isLoading.value = true

    // 先检查后端服务是否可用
    const isBackendAvailable = await checkBackendHealth()
    
    if (isBackendAvailable) {
      // 后端服务可用，正常初始化
      try {
        // 连接WebSocket
        await connectWebSocket()
        
        // 获取在线用户列表
        await fetchOnlineUsers()
        
        showToast('页面加载完成', 'success')
      } catch (error) {
        console.error('网络服务连接失败:', error)
        showToast('网络服务暂时不可用，进入离线模式', 'warning')
        enterOfflineMode()
      }
    } else {
      // 后端服务不可用，进入离线模式
      console.warn('后端服务不可用，进入离线模式')
      showToast('服务器连接失败，进入离线模式', 'warning')
      enterOfflineMode()
    }

    // 检查本地缓存的后台消息（离线时也能查看）
    checkBackendMessage()

  } catch (error) {
    console.error('页面初始化失败:', error)
    showToast('页面加载失败，请稍后重试', 'error')
    enterOfflineMode()
  } finally {
    isLoading.value = false
  }
}

// 检查后端服务健康状态
const checkBackendHealth = async (): Promise<boolean> => {
  try {
    const healthCheckUrl = `${getFrontendConfig().apiBaseUrl.replace('/api', '')}/health`
    const response = await uni.request({
      url: healthCheckUrl,
      method: 'GET',
      timeout: 5000, // 5秒超时
      header: {
        'Content-Type': 'application/json'
      }
    })
    
    return response.statusCode === 200
  } catch (error) {
    console.log('后端健康检查失败:', error)
    return false
  }
}

// 进入离线模式
const enterOfflineMode = () => {
  // 断开Socket连接（如果已连接）
  if (isWebSocketConnected.value) {
    disconnectSocket()
  }

  // 从本地存储加载缓存的用户列表
  try {
    const cachedUsers = uni.getStorageSync('cachedOnlineUsers')
    if (cachedUsers && Array.isArray(cachedUsers)) {
      onlineUsers.value = cachedUsers
      console.log('已加载缓存的用户列表:', onlineUsers.value.length)
    } else {
      // 没有缓存数据时显示空列表
      onlineUsers.value = []
    }
  } catch (error) {
    console.error('加载缓存用户列表失败:', error)
    onlineUsers.value = []
  }
}

// 连接WebSocket
const connectWebSocket = async (): Promise<void> => {
  try {
    // 检查用户是否已登录
    if (!userStore.userInfo?.username) {
      throw new Error('用户未登录')
    }

    // 使用Socket store连接
    const config = getFrontendConfig()
    await connectSocket(config.websocketUrl)

    console.log('Socket.io连接成功')

    console.log('Socket.io连接成功')
  } catch (error) {
    console.error('Socket.io连接失败:', error)
    // 优化错误信息
    if (error.message.includes('timeout')) {
      throw new Error('服务器连接超时，请检查网络连接或稍后重试')
    } else {
      throw new Error('网络连接失败，请检查服务器状态')
    }
  }
}

// 注意：WebSocket事件监听器现在由Socket store处理
// 这里不再需要手动设置监听器，Socket store会自动处理所有事件

// 获取在线用户列表
const fetchOnlineUsers = async () => {
  try {
    // 调用后端API获取在线用户列表
    const response = await uni.request({
      url: `${getFrontendConfig().apiBaseUrl}/users/online`,
      method: 'GET',
      timeout: 8000, // 8秒超时
      header: {
        'Authorization': `Bearer ${userStore.sessionToken}`,
        'Content-Type': 'application/json'
      }
    })

    // 检查响应是否为有效的JSON
    if (response.statusCode === 200 && typeof response.data === 'object' && response.data.success) {
      // 更新在线用户列表
      const users = response.data.data.users.map((user: any) => ({
        id: user.id,
        username: user.username,
        displayName: user.displayName || user.username,
        status: user.status,
        avatar: '', // 如果后端有头像字段可以添加
        lastActiveTime: new Date(user.lastLoginAt).getTime()
      }))
      
      onlineUsers.value = users
      
      // 缓存用户列表到本地存储
      uni.setStorageSync('cachedOnlineUsers', users)
      
      console.log('在线用户列表获取成功:', onlineUsers.value)
    } else {
      console.error('获取在线用户列表失败:', response.data)
      throw new Error(`API返回错误: ${response.data?.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('获取在线用户列表异常:', error)
    
    // 检查是否返回了HTML（说明服务器没有正确响应）
    if (typeof error === 'object' && error.data && typeof error.data === 'string' && error.data.includes('<!DOCTYPE html>')) {
      throw new Error('服务器未正确响应，可能服务未启动')
    } else {
      throw new Error('网络连接错误，无法获取用户列表')
    }
  }
}

// 检查后台消息
const checkBackendMessage = () => {
  // 从本地存储获取最新的后台消息
  const storedMessage = uni.getStorageSync('latestBackendMessage')
  if (storedMessage) {
    backendMessage.value = storedMessage
  }
}

// 处理后台呼叫消息
const _handleBackendCall = (data: any) => {
  backendMessage.value = {
    id: data.id || Date.now().toString(),
    content: data.message || '后台管理员呼叫',
    timestamp: data.timestamp || Date.now(),
    priority: data.priority || 'normal',
    from: '后台管理'
  }

  // 保存到本地存储
  uni.setStorageSync('latestBackendMessage', backendMessage.value)

  // 如果正在通话中，需要特殊处理
  if (userStore.isTalking) {
    // 降低通话音量，播放提示音，叠加显示消息
    handleCallInterruption()
  }

  showToast('收到后台管理消息', 'info')
}

// 处理通话中断
const handleCallInterruption = () => {
  // 这里实现通话中的消息处理逻辑
  // 1. 降低通话音量至50%
  // 2. 播放消息提示音
  // 3. 在界面上方叠加显示消息
  console.log('处理通话中断消息')
}

// 处理后台消息接收（BackendMessageManager组件回调）
const handleBackendMessageReceived = (message: any) => {
  console.log('主页面收到后台消息:', message)

  // 添加到本地消息列表（如果需要）
  const localMessage = {
    id: message.id,
    type: 'backend-call',
    title: message.title,
    content: message.content,
    timestamp: message.timestamp,
    priority: message.priority,
    isRead: false
  }

  messages.value.unshift(localMessage)

  // 限制消息数量
  if (messages.value.length > 50) {
    messages.value = messages.value.slice(0, 50)
  }
}

// 处理紧急后台消息
const handleUrgentMessage = (message: any) => {
  console.log('收到紧急后台消息:', message)

  // 紧急消息的特殊处理
  showToast('收到紧急后台消息', 'error')

  // 可以添加震动提醒
  try {
    uni.vibrateShort()
  } catch (error) {
    console.warn('震动提醒失败:', error)
  }
}

// 更新用户状态
const updateUserStatus = (data: any) => {
  const userIndex = onlineUsers.value.findIndex(user => user.id === data.userId)
  if (userIndex !== -1) {
    onlineUsers.value[userIndex].status = data.status
    onlineUsers.value[userIndex].lastActiveTime = data.timestamp
  }
}

// 添加在线用户
const addOnlineUser = (user: any) => {
  // 检查用户是否已存在
  const existingIndex = onlineUsers.value.findIndex(u => u.id === user.id)
  if (existingIndex === -1) {
    // 用户不存在，添加到列表
    onlineUsers.value.push({
      id: user.id,
      username: user.username,
      displayName: user.displayName || user.username,
      status: user.status,
      avatar: user.avatar || '',
      lastActiveTime: Date.now()
    })
    console.log(`用户 ${user.username} 已上线`)
  } else {
    // 用户已存在，更新状态
    onlineUsers.value[existingIndex] = {
      ...onlineUsers.value[existingIndex],
      status: user.status,
      lastActiveTime: Date.now()
    }
  }
}

// 移除离线用户
const removeOnlineUser = (userId: string) => {
  const userIndex = onlineUsers.value.findIndex(user => user.id === userId)
  if (userIndex !== -1) {
    const user = onlineUsers.value[userIndex]
    onlineUsers.value.splice(userIndex, 1)
    console.log(`用户 ${user.username} 已离线`)
  }
}

// 处理用户点击
const handleUserClick = (user: any) => {
  if (user.status === UserStatus.TALKING) {
    showToast('该用户正在通话中', 'warning')
    return
  }

  if (!userStore.canStartCall) {
    showToast('您当前无法发起通话', 'warning')
    return
  }

  // 跳转到对讲页面
  uni.navigateTo({
    url: `/pages/call/call?targetUser=${encodeURIComponent(JSON.stringify(user))}`
  })
}

// 处理后台消息点击
const handleBackendMessageClick = () => {
  // 可以跳转到消息详情页面或执行其他操作
  showToast('查看后台消息详情', 'info')
}

// 处理刷新
const handleRefresh = async () => {
  try {
    isLoading.value = true
    
    // 检查是否在离线模式
    if (!isWebSocketConnected.value) {
      showToast('正在重新连接服务器...', 'info')
      // 重新初始化页面，尝试连接服务器
      await initializePage()
    } else {
      showToast('正在刷新用户列表...', 'info')
      // 只刷新在线用户列表
      await fetchOnlineUsers()
      showToast('用户列表刷新成功', 'success')
    }
  } catch (error) {
    console.error('刷新失败:', error)
    showToast('刷新失败，请检查网络连接', 'error')
  } finally {
    isLoading.value = false
  }
}

// 处理Socket.io测试
const handleTestSocket = () => {
  uni.navigateTo({
    url: '/pages/test-socket/test-socket'
  })
}

// 处理退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        await userStore.logout()
        cleanup()
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

// 清理资源
const cleanup = () => {
  // 断开Socket.io连接
  if (isWebSocketConnected.value) {
    disconnectSocket()
  }
}

// 格式化时间
const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

// 显示提示
const showToast = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
  toastMessage.value = message
  toastType.value = type
  toastVisible.value = true
}
</script>

<style lang="scss" scoped>
.main-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px;
}

.status-bar {
  background: white;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .user-info-card {
    padding: 16px 20px;

    .user-basic-info {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .user-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;

        .avatar-text {
          color: white;
          font-size: 18px;
          font-weight: 600;
        }
      }

      .user-details {
        flex: 1;

        .welcome-text {
          display: block;
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin-bottom: 6px;
        }

        .status-row {
          display: flex;
          align-items: center;
          gap: 16px;
        }
      }
    }

    .status-indicator {
      display: flex;
      align-items: center;

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
      }

      .status-text {
        font-size: 13px;
        color: #666;
        font-weight: 500;
      }

      &.status-online .status-dot {
        background: #52c41a;
      }

      &.status-talking .status-dot {
        background: #1890ff;
      }

      &.status-busy .status-dot {
        background: #faad14;
      }

      &.status-offline .status-dot {
        background: #d9d9d9;
      }
    }

    .connection-indicator {
      display: flex;
      align-items: center;

      .connection-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
      }

      .connection-text {
        font-size: 13px;
        color: #666;
        font-weight: 500;
      }

      &.connection-online .connection-dot {
        background: #52c41a;
      }

      &.connection-online .connection-text {
        color: #52c41a;
      }

      &.connection-offline .connection-dot {
        background: #ff4d4f;
      }

      &.connection-offline .connection-text {
        color: #ff4d4f;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;
      justify-content: flex-end;

      :deep(.uni-btn) {
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 6px;
      }
    }
  }
}

.backend-message-section {
  margin-bottom: 20px;

  .backend-message-card {
    border: 2px solid #ff6b6b;
    background: #fff5f5;
  }
}

.online-users-section {
  .users-waterfall {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    margin-top: 15px;

    .user-item {
      .user-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;

    .empty-icon {
      font-size: 48px;
      display: block;
      margin-bottom: 15px;
    }

    .empty-text {
      font-size: 16px;
      color: #999;
      display: block;
      margin-bottom: 20px;
    }
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  .title-icon {
    font-size: 18px;
    margin-right: 8px;
  }

  .title-text {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    flex: 1;
  }

  .count-badge {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 500;
  }

  .message-time {
    font-size: 12px;
    color: #999;
  }
}
</style>

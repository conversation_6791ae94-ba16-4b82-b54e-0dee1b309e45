[BACKEND] 10:15:41 info: GET /health - 200 - 6ms - ::1
[BACKEND] 10:15:41 info: WebSocket user authenticated
[BACKEND] 10:15:41 error: 未捕获的异常: logger_1.logger.websocket is not a function
[BACKEND] TypeError: logger_1.logger.websocket is not a function
[BACKEND]     at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
[BACKEND]     at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
[BACKEND]     at Namespace.emit (node:events:518:28)
[BACKEND]     at Namespace.emit (node:domain:488:12)
[BACKEND]     at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
[BACKEND]     at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
[BACKEND]     at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
[BACKEND]     at processTicksAndRejections (node:internal/process/task_queues:77:11)
[BACKEND] 10:15:41 info: 🛑 正在关闭服务器...
[BACKEND] 10:15:41 error: Uncaught Exception: logger_1.logger.websocket is not a function
[BACKEND] TypeError: logger_1.logger.websocket is not a function
[BACKEND]     at WebSocketManager.handleConnection (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:92:12)
[BACKEND]     at Namespace.<anonymous> (G:\codingProject\talking\backend\src\websocket\WebSocketManager.ts:60:12)
[BACKEND]     at Namespace.emit (node:events:518:28)
[BACKEND]     at Namespace.emit (node:domain:488:12)
[BACKEND]     at Namespace.emitReserved (G:\codingProject\talking\backend\node_modules\socket.io\dist\typed-events.js:56:22)
[BACKEND]     at Namespace._doConnect (G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:276:14)
[BACKEND]     at G:\codingProject\talking\backend\node_modules\socket.io\dist\namespace.js:238:22
[BACKEND]     at processTicksAndRejections (node:internal/process/task_queues:77:11)
[BACKEND] 10:15:41 info: Received uncaughtException, starting graceful shutdown...
[BACKEND] 10:15:41 info: Database connection closed
[BACKEND] 10:15:41 info: ✅ 数据库连接已关闭
[BACKEND] 10:15:41 info: 👋 服务器已优雅关闭
[BACKEND] npm run dev:backend exited with code 1
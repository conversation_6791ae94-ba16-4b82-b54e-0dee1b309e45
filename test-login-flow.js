const axios = require('axios');
const { io } = require('socket.io-client');

async function testLoginFlow() {
  console.log('🧪 测试完整登录流程...\n');

  try {
    // 1. 测试登录API
    console.log('1️⃣ 测试登录API...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'testuser'
    });

    console.log('✅ 登录API响应:', {
      status: loginResponse.status,
      success: loginResponse.data.success,
      hasToken: !!loginResponse.data.data?.token,
      tokenLength: loginResponse.data.data?.token?.length || 0,
      hasRefreshToken: !!loginResponse.data.data?.refreshToken,
      user: loginResponse.data.data?.user?.username
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败');
    }

    const token = loginResponse.data.data.token;
    console.log('🔑 获取到JWT Token:', token.substring(0, 50) + '...\n');

    // 2. 测试WebSocket连接
    console.log('2️⃣ 测试WebSocket连接...');
    
    const socket = io('http://localhost:3001', {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'],
      timeout: 10000
    });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        socket.disconnect();
        reject(new Error('WebSocket连接超时'));
      }, 15000);

      socket.on('connect', () => {
        console.log('✅ WebSocket连接成功');
        console.log('🔗 Socket ID:', socket.id);
        clearTimeout(timeout);
        
        // 测试发送消息
        socket.emit('ping', { timestamp: Date.now() });
        
        setTimeout(() => {
          socket.disconnect();
          resolve();
        }, 2000);
      });

      socket.on('connect_error', (error) => {
        console.error('❌ WebSocket连接错误:', error.message);
        clearTimeout(timeout);
        socket.disconnect();
        reject(error);
      });

      socket.on('pong', (data) => {
        console.log('🏓 收到pong响应:', data);
      });

      socket.on('user-authenticated', (data) => {
        console.log('✅ 用户认证成功:', data);
      });

      socket.on('disconnect', (reason) => {
        console.log('🔌 WebSocket断开连接:', reason);
      });
    });

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('📄 响应详情:', {
        status: error.response.status,
        data: error.response.data
      });
    }
    throw error;
  }
}

// 运行测试
testLoginFlow()
  .then(() => {
    console.log('\n🎉 登录流程测试完成！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 登录流程测试失败:', error.message);
    process.exit(1);
  });

# Stagewise工具栏集成文档

## 概述

Stagewise是一个强大的浏览器工具栏，它将前端UI与代码编辑器中的AI代理连接起来。开发者可以在Web应用中选择元素、添加注释，并让AI代理基于这些上下文进行代码更改。

## 功能特性

### 🎯 元素选择
- 点击页面元素进行选择
- 高亮显示选中的元素
- 显示元素的详细信息
- 支持Vue组件检测

### 💬 智能注释
- 为选中的元素添加注释
- 支持Markdown格式
- 自动保存注释内容
- 显示时间戳

### 🤖 AI代码生成
- 基于选中元素和注释生成代码
- 支持多种AI模型
- 智能上下文理解
- 代码质量优化

### 🔧 Vue集成
- 自动检测Vue组件
- 显示组件属性和状态
- 跟踪响应式数据变化
- 集成Vue开发工具

## 安装和配置

### 1. 包安装
```bash
npm install --save-dev @stagewise/toolbar-vue @stagewise-plugins/vue
```

### 2. 自动初始化
Stagewise已经集成到项目中，会在以下条件下自动启动：
- 开发环境 (`NODE_ENV === 'development'`)
- 浏览器环境（非SSR）
- uni-app H5平台

### 3. 配置文件
配置文件位于 `stagewise.config.js`，可以自定义：
- 工具栏外观和位置
- AI模型设置
- 元素选择器配置
- 调试选项

## 使用方法

### 基本操作

#### 1. 启动工具栏
- 工具栏会在页面加载后自动出现在右下角
- 如果没有出现，检查控制台是否有错误信息

#### 2. 选择元素
- 点击工具栏中的"选择元素"按钮
- 鼠标悬停在页面元素上会显示高亮
- 点击元素进行选择

#### 3. 添加注释
- 选择元素后，点击"添加注释"按钮
- 在弹出的对话框中输入注释内容
- 支持Markdown语法

#### 4. 生成代码
- 基于选中的元素和注释
- 点击"生成代码"按钮
- AI会分析上下文并生成相应代码

### 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+Shift+S` | 切换工具栏显示/隐藏 |
| `Ctrl+Shift+C` | 进入注释模式 |
| `Ctrl+Click` | 快速选择元素 |
| `Escape` | 退出当前模式 |

### Vue特定功能

#### 组件检测
- 自动识别Vue组件
- 显示组件名称和类型
- 展示组件属性（props）
- 显示组件状态（data）

#### 响应式跟踪
- 监控响应式数据变化
- 显示数据流向
- 性能分析

## 配置选项

### 工具栏配置
```javascript
toolbar: {
  position: 'bottom-right', // 位置
  theme: 'dark',           // 主题
  size: 'medium',          // 大小
  autoHide: false,         // 自动隐藏
  zIndex: 9999,           // 层级
}
```

### AI配置
```javascript
ai: {
  enabled: true,
  provider: 'default',
  model: 'gpt-4',
  contextWindow: 4000,
}
```

### 选择器配置
```javascript
selector: {
  highlightColor: '#667eea',
  borderWidth: 2,
  showTooltip: true,
  excludeSelectors: [
    '.stagewise-toolbar',
    'script',
    'style'
  ],
}
```

## 故障排除

### 工具栏不显示
1. 检查是否在开发环境
2. 确认在浏览器中运行（非SSR）
3. 查看控制台错误信息
4. 检查uni-app平台是否为H5

### 元素选择不工作
1. 检查是否有CSS冲突
2. 确认元素没有被排除
3. 查看控制台错误

### AI功能不可用
1. 检查网络连接
2. 确认AI配置正确
3. 查看API密钥设置

## 开发和调试

### 调试模式
在开发环境中，Stagewise会输出详细的调试信息：
```javascript
debug: {
  enabled: true,
  logLevel: 'info',
  showPerformance: true,
}
```

### 自定义样式
可以通过CSS自定义工具栏外观：
```css
.stagewise-toolbar {
  /* 自定义样式 */
}

.stagewise-highlight {
  /* 自定义高亮样式 */
}
```

### 扩展功能
可以通过插件系统扩展功能：
```javascript
plugins: [
  '@stagewise-plugins/vue',
  // 其他插件
],
```

## VSCode集成

### 推荐扩展
项目已配置VSCode扩展推荐列表，包含：
- `stagewise.stagewise-vscode-extension`
- 其他开发相关扩展

### 安装扩展
1. 打开VSCode
2. 查看推荐扩展
3. 安装Stagewise扩展

## 最佳实践

### 1. 元素命名
- 为重要元素添加有意义的class名
- 使用语义化的HTML标签
- 避免过深的嵌套结构

### 2. 注释规范
- 使用清晰简洁的描述
- 包含设计意图和功能说明
- 提及相关的业务逻辑

### 3. 代码生成
- 提供详细的上下文信息
- 明确指定期望的功能
- 验证生成的代码质量

## 支持和反馈

如果遇到问题或需要帮助：
1. 查看控制台错误信息
2. 检查配置是否正确
3. 联系Stagewise团队：[Discord](https://discord.gg/gkdGsDYaKA)

## 更新日志

### v0.5.0
- 初始集成到项目
- 支持Vue组件检测
- 添加快捷键支持
- 集成环境检测

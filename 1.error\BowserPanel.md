favicon.ico:1   GET http://localhost:3000/favicon.ico 404 (Not Found)
websocket.ts:231  Socket.IO连接错误: Error: Authentication failed
    at a.value (socket.js:466:29)
    at s.<anonymous> (index.mjs:136:20)
    at manager.js:204:18
(匿名) @ websocket.ts:231
(匿名) @ index.mjs:136
value @ socket.js:469
(匿名) @ index.mjs:136
(匿名) @ manager.js:204
Promise.then
ut @ websocket-constructor.browser.js:5
value @ manager.js:203
(匿名) @ index.mjs:136
value @ index.js:146
value @ manager.js:190
(匿名) @ index.mjs:136
value @ socket.js:341
(匿名) @ index.mjs:136
value @ transport.js:98
value @ transport.js:90
r.value.ws.onmessage @ websocket.js:68
main.vue:306  WebSocket连接失败: Error: Authentication failed
    at a.value (socket.js:466:29)
    at s.<anonymous> (index.mjs:136:20)
    at manager.js:204:18
connectWebSocket @ main.vue:306
await in connectWebSocket
initializePage @ main.vue:200
await in initializePage
(匿名) @ main.vue:180
(匿名) @ vue.runtime.esm.js:4209
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
hook.__weh.hook.__weh @ vue.runtime.esm.js:4187
flushPostFlushCbs @ vue.runtime.esm.js:1591
flushJobs @ vue.runtime.esm.js:1629
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
scheduler @ vue.runtime.esm.js:3219
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
replace @ vue-router.mjs:3263
(匿名) @ uni-h5.es.js:7013
navigate @ uni-h5.es.js:7011
(匿名) @ uni-h5.es.js:6998
(匿名) @ uni-h5.es.js:3066
invokeApi @ uni-h5.es.js:2917
(匿名) @ uni-h5.es.js:2939
(匿名) @ uni-h5.es.js:2938
(匿名) @ login.vue:578
setTimeout
performLogin @ login.vue:577
await in performLogin
confirmAudioTest @ login.vue:564
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
emit @ vue.runtime.esm.js:1941
(匿名) @ vue.runtime.esm.js:9171
handleConfirm @ Modal.vue:132
patchedFn @ vue.runtime.esm.js:10283
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10254
main.vue:207  网络服务连接失败: Error: 网络连接失败，请检查服务器状态
    at connectWebSocket (main.vue:311:13)
    at async initializePage (main.vue:200:9)
    at async main.vue:180:3
initializePage @ main.vue:207
await in initializePage
(匿名) @ main.vue:180
(匿名) @ vue.runtime.esm.js:4209
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
hook.__weh.hook.__weh @ vue.runtime.esm.js:4187
flushPostFlushCbs @ vue.runtime.esm.js:1591
flushJobs @ vue.runtime.esm.js:1629
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
scheduler @ vue.runtime.esm.js:3219
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
replace @ vue-router.mjs:3263
(匿名) @ uni-h5.es.js:7013
navigate @ uni-h5.es.js:7011
(匿名) @ uni-h5.es.js:6998
(匿名) @ uni-h5.es.js:3066
invokeApi @ uni-h5.es.js:2917
(匿名) @ uni-h5.es.js:2939
(匿名) @ uni-h5.es.js:2938
(匿名) @ login.vue:578
setTimeout
performLogin @ login.vue:577
await in performLogin
confirmAudioTest @ login.vue:564
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
emit @ vue.runtime.esm.js:1941
(匿名) @ vue.runtime.esm.js:9171
handleConfirm @ Modal.vue:132
patchedFn @ vue.runtime.esm.js:10283
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10254

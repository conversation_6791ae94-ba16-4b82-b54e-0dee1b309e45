socket.ts:144  Socket.io连接错误: Error: Authentication failed
    at Socket.onpacket (socket.js:506:29)
    at Emitter.emit (index.js:136:20)
    at manager.js:209:18
(匿名) @ socket.ts:144
Emitter.emit @ index.js:136
onpacket @ socket.js:509
Emitter.emit @ index.js:136
(匿名) @ manager.js:209
Promise.then
(匿名) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
main.vue:297  Socket.io连接失败: Error: 认证失败，请重新登录
    at Socket.<anonymous> (socket.ts:159:16)
    at Emitter.emit (index.js:136:20)
    at Socket.onpacket (socket.js:509:22)
    at Emitter.emit (index.js:136:20)
    at manager.js:209:18
connectWebSocket @ main.vue:297
await in connectWebSocket
initializePage @ main.vue:208
await in initializePage
(匿名) @ main.vue:188
(匿名) @ vue.runtime.esm.js:4209
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
hook.__weh.hook.__weh @ vue.runtime.esm.js:4187
flushPostFlushCbs @ vue.runtime.esm.js:1591
flushJobs @ vue.runtime.esm.js:1629
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
scheduler @ vue.runtime.esm.js:3219
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
replace @ vue-router.mjs:3263
(匿名) @ uni-h5.es.js:7013
navigate @ uni-h5.es.js:7011
(匿名) @ uni-h5.es.js:6998
(匿名) @ uni-h5.es.js:3066
invokeApi @ uni-h5.es.js:2917
(匿名) @ uni-h5.es.js:2939
(匿名) @ uni-h5.es.js:2938
(匿名) @ login.vue:578
setTimeout
performLogin @ login.vue:577
await in performLogin
confirmAudioTest @ login.vue:564
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
emit @ vue.runtime.esm.js:1941
(匿名) @ vue.runtime.esm.js:9171
handleConfirm @ Modal.vue:132
patchedFn @ vue.runtime.esm.js:10283
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10254
main.vue:215  网络服务连接失败: Error: 网络连接失败，请检查服务器状态
    at connectWebSocket (main.vue:302:13)
    at async initializePage (main.vue:208:9)
    at async main.vue:188:3
initializePage @ main.vue:215
await in initializePage
(匿名) @ main.vue:188
(匿名) @ vue.runtime.esm.js:4209
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
hook.__weh.hook.__weh @ vue.runtime.esm.js:4187
flushPostFlushCbs @ vue.runtime.esm.js:1591
flushJobs @ vue.runtime.esm.js:1629
Promise.then
queueFlush @ vue.runtime.esm.js:1532
queueJob @ vue.runtime.esm.js:1526
scheduler @ vue.runtime.esm.js:3219
resetScheduling @ vue.runtime.esm.js:263
triggerEffects @ vue.runtime.esm.js:307
triggerRefValue @ vue.runtime.esm.js:1067
set value @ vue.runtime.esm.js:1112
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
replace @ vue-router.mjs:3263
(匿名) @ uni-h5.es.js:7013
navigate @ uni-h5.es.js:7011
(匿名) @ uni-h5.es.js:6998
(匿名) @ uni-h5.es.js:3066
invokeApi @ uni-h5.es.js:2917
(匿名) @ uni-h5.es.js:2939
(匿名) @ uni-h5.es.js:2938
(匿名) @ login.vue:578
setTimeout
performLogin @ login.vue:577
await in performLogin
confirmAudioTest @ login.vue:564
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
emit @ vue.runtime.esm.js:1941
(匿名) @ vue.runtime.esm.js:9171
handleConfirm @ Modal.vue:132
patchedFn @ vue.runtime.esm.js:10283
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10254
favicon.ico:1   GET http://localhost:3000/favicon.ico 404 (Not Found)

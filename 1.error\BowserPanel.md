user.ts:58   POST http://localhost:3001/api/auth/login 500 (Internal Server Error)
(匿名) @ uni-h5.es.js:20538
(匿名) @ uni-h5.es.js:3066
invokeApi @ uni-h5.es.js:2917
(匿名) @ uni-h5.es.js:2939
(匿名) @ uni-h5.es.js:2938
login @ user.ts:58
wrappedAction @ pinia.mjs:1399
store.<computed> @ pinia.mjs:932
performLogin @ login.vue:571
handleLogin @ login.vue:286
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
emit @ vue.runtime.esm.js:1941
(匿名) @ vue.runtime.esm.js:9171
handleClick @ Button.vue:72
patchedFn @ vue.runtime.esm.js:10283
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10254
user.ts:100  登录失败: Internal server error
login @ user.ts:100
await in login
wrappedAction @ pinia.mjs:1399
store.<computed> @ pinia.mjs:932
performLogin @ login.vue:571
handleLogin @ login.vue:286
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
emit @ vue.runtime.esm.js:1941
(匿名) @ vue.runtime.esm.js:9171
handleClick @ Button.vue:72
patchedFn @ vue.runtime.esm.js:10283
callWithErrorHandling @ vue.runtime.esm.js:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js:1425
invoker @ vue.runtime.esm.js:10254

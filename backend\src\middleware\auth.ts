/**
 * 认证中间件 - 重构版本
 *
 * 功能说明：
 * 1. JWT Token验证（使用共享工具）
 * 2. 用户身份认证
 * 3. 权限检查
 * 4. Token刷新
 * 5. 会话管理
 */

import { Request, Response, NextFunction } from 'express'
import { config } from '@/config/config'
import { logger } from '@/utils/logger'
import { createAuthError, createForbiddenError } from './errorHandler'
import jwt from 'jsonwebtoken'

// 本地类型定义
interface JWTPayload {
  userId: number
  username: string
  iat?: number
  exp?: number
}

interface UserInfo {
  id: number
  username: string
  displayName?: string
  status: string
}

// 简化的认证工具
const extractToken = (authHeader: string): string | null => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }
  return authHeader.substring(7)
}

// 扩展Request接口
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user?: UserInfo
      token?: string
    }
  }
}

// 简化的认证工具
const authUtils = {
  generateToken: (userInfo: any): string => {
    // 构建JWT payload
    const payload: JWTPayload = {
      userId: userInfo.id,
      username: userInfo.username
    }
    return jwt.sign(payload as any, config.security.jwtSecret, {
      expiresIn: config.security.jwtExpiresIn
    })
  },

  generateRefreshToken: (userId: number): string => {
    const payload = {
      userId,
      type: 'refresh'
    }
    return jwt.sign(payload, config.security.jwtRefreshSecret, {
      expiresIn: config.security.jwtRefreshExpiresIn
    })
  },

  verifyToken: (token: string): JWTPayload | null => {
    try {
      return jwt.verify(token, config.security.jwtSecret) as JWTPayload
    } catch {
      return null
    }
  },

  getDefaultPermissions: () => {
    return {
      microphone: true,
      speaker: true,
      voice_call: true,
      message_send: true,
      message_receive: true,
      backend_call: true
    }
  }
}

// 生成JWT Token
export const generateToken = (user: UserInfo): string => {
  const payload: JWTPayload = {
    userId: user.id,
    username: user.username
  }
  return authUtils.generateToken(payload)
}

// 验证JWT Token
export const verifyToken = (token: string): JWTPayload => {
  const result = authUtils.verifyToken(token)

  if (!result) {
    throw createAuthError('Token verification failed')
  }

  return result
}

// 从请求中提取Token
const extractTokenFromRequest = (req: Request): string | null => {
  const authHeader = req.headers.authorization
  return extractToken(authHeader || '')
}

// 认证中间件（重构版本）
export const authMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 提取Token
    const token = extractTokenFromRequest(req)
    if (!token) {
      throw createAuthError('Authentication token required')
    }

    // 验证Token
    const payload = verifyToken(token)

    // 设置用户信息到请求对象
    req.user = {
      id: payload.userId,
      username: payload.username,
      displayName: payload.username,
      status: 'online'
    }
    req.token = token

    // 为了兼容路由中的使用，也设置userId
    ;(req as any).userId = payload.userId

    // 记录认证日志
    logger.info('User authenticated', {
      userId: payload.userId,
      username: payload.username,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    })

    next()
  } catch (error) {
    // 记录认证失败日志
    logger.warn('Authentication failed', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      error: error.message
    })

    next(error)
  }
}

// 可选认证中间件（不强制要求认证）
export const optionalAuthMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const token = extractTokenFromRequest(req)
    if (token) {
      const payload = verifyToken(token)
      req.user = {
        id: payload.userId,
        username: payload.username,
        displayName: payload.username,
        status: 'online'
      }
      req.token = token
    }
    next()
  } catch (error: any) {
    // 可选认证失败时不抛出错误，继续执行
    logger.debug('Optional authentication failed', {
      error: error.message,
      ip: req.ip
    })
    next()
  }
}

// 简化的权限检查中间件
export const requirePermission = (_permission: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(createAuthError('Authentication required'))
    }

    // 简化的权限检查 - 所有认证用户都有基本权限
    next()
  }
}

// 管理员权限检查
export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    return next(createAuthError('Authentication required'))
  }

  // 简化处理：检查用户名是否包含admin
  if (!req.user.username.toLowerCase().includes('admin')) {
    return next(createForbiddenError('Administrator access required'))
  }

  next()
}

// 用户自身或管理员权限检查
export const requireSelfOrAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    return next(createAuthError('Authentication required'))
  }

  const targetUserId = parseInt(req.params.userId || req.params.id, 10)
  const isAdmin = req.user.username.toLowerCase().includes('admin')
  const isSelf = req.user.id === targetUserId

  if (!isSelf && !isAdmin) {
    return next(createForbiddenError('Access denied'))
  }

  next()
}

// WebSocket认证
export const authenticateSocket = (socket: any, next: any): void => {
  try {
    const token = socket.handshake.auth?.token || socket.handshake.query?.token

    if (!token) {
      return next(new Error('Authentication token required'))
    }

    const payload = verifyToken(token)

    // 设置socket用户信息
    socket.userId = payload.userId
    socket.username = payload.username
    socket.displayName = payload.username

    logger.info('WebSocket user authenticated', {
      socketId: socket.id,
      userId: payload.userId,
      username: payload.username
    })

    next()
  } catch (error: any) {
    logger.warn('WebSocket authentication failed', {
      socketId: socket.id,
      error: error.message
    })
    next(new Error('Authentication failed'))
  }
}

// Token刷新
export const refreshToken = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (!req.user) {
      throw createAuthError('Authentication required')
    }

    // 生成新Token
    const newToken = generateToken(req.user)

    // 返回新Token
    res.json({
      success: true,
      data: {
        token: newToken,
        user: req.user
      }
    })
  } catch (error: any) {
    next(error)
  }
}

// 登出处理
export const logout = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (req.user) {
      logger.info('User logged out', {
        userId: req.user.id,
        username: req.user.username
      })
    }

    // 清除Cookie中的Token
    res.clearCookie('token')

    res.json({
      success: true,
      message: 'Logged out successfully'
    })
  } catch (error: any) {
    next(error)
  }
}

// 导出认证工具实例和类型
export { authUtils }
export type { JWTPayload, UserInfo }

export default {
  generateToken,
  verifyToken,
  authMiddleware,
  optionalAuthMiddleware,
  requirePermission,
  requireAdmin,
  requireSelfOrAdmin,
  authenticateSocket,
  refreshToken,
  logout,
  authUtils
}

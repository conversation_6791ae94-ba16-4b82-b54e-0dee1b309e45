# 质量检查脚本使用指南

## 📋 脚本概述

`quality-check.ts` 是一个全面的代码质量检查工具，用于评估项目的代码质量、检测问题并提供改进建议。

## 🎯 主要功能

### 1. 代码质量检查

- **代码重复度检测** - 使用jscpd工具检查重复代码
- **TypeScript类型检查** - 验证类型安全性
- **ESLint规则检查** - 代码风格和质量检查
- **测试覆盖率分析** - 评估测试完整性
- **模块复杂度评估** - 检查文件大小和复杂度

### 2. 项目结构验证

- **依赖关系检查** - 识别未使用的依赖包
- **配置一致性验证** - 检查前后端配置统一性
- **重构效果评估** - 验证代码重构的效果

### 3. 报告生成

- **综合评分** - 0-100分的质量评分
- **详细报告** - 生成Markdown格式的质量报告
- **改进建议** - 提供具体的优化建议

## 🚀 使用方法

### 方法一：直接运行（推荐）

```bash
# 在项目根目录执行
npx ts-node scripts/quality-check.ts
```

### 方法二：添加到package.json脚本

在根目录的 `package.json` 中添加：

```json
{
  "scripts": {
    "quality-check": "ts-node scripts/quality-check.ts",
    "quality": "npm run quality-check"
  }
}
```

然后运行：

```bash
npm run quality-check
# 或
npm run quality
```

### 方法三：集成到CI/CD流程

```bash
# 在CI/CD脚本中使用
npm run quality-check
# 如果质量评分低于70分，脚本会返回错误码1
```

## 📊 输出示例

### 控制台输出

```
🔍 开始代码质量检查...

📊 检查代码重复度...
   代码重复度: 3.2%

🔧 检查TypeScript类型错误...
   ✅ 无TypeScript类型错误

📝 检查ESLint规则...
   ⚠️  发现0个错误，5个警告

🧪 检查测试覆盖率...
   测试覆盖率: 75%

📈 检查模块复杂度...
   平均文件行数: 180
   复杂文件数量: 2

📦 检查依赖关系...
   总依赖数: 45
   未使用依赖: 3

⚙️  检查配置一致性...
   配置问题: 1

🔄 验证重构效果...
   重构效果评分: 85/100

✅ 代码质量检查完成

📊 质量检查报告:
总体评分: 82/100
通过检查: 6
失败检查: 2
改进建议: 4

📄 详细报告已保存至: quality-report.md
```

### 生成的报告文件

脚本会在项目根目录生成 `quality-report.md` 文件，包含：

- 详细的质量指标
- 通过和失败的检查项
- 具体的改进建议

## ⏰ 使用时机

### 1. 开发阶段

- **每日开发结束前** - 检查当天代码质量
- **功能开发完成后** - 验证新功能的代码质量
- **重构后** - 验证重构效果

### 2. 代码审查

- **Pull Request前** - 确保代码质量达标
- **代码审查过程中** - 提供客观的质量评估

### 3. 项目里程碑

- **版本发布前** - 全面质量检查
- **项目交付前** - 最终质量验证

### 4. 持续集成

- **每次提交后** - 自动质量检查
- **定期检查** - 每日/每周自动运行

## 🔧 依赖要求

### 必需工具

- **TypeScript** - 类型检查
- **ESLint** - 代码规范检查
- **Jest** - 测试覆盖率（如果有测试）

### 可选工具

- **jscpd** - 代码重复度检查

```bash
npm install -g jscpd
# 或项目内安装
npm install --save-dev jscpd
```

## 📈 评分标准

### 评分计算

- **基础分**: 100分
- **扣分项**:
  - 代码重复度: 每1%扣2分
  - TypeScript错误: 每个错误扣5分
  - ESLint错误: 每个错误扣2分
  - ESLint警告: 每个警告扣0.5分
  - 测试覆盖率: 低于80%按差值扣分
  - 依赖问题: 每个问题扣2分
  - 配置问题: 每个问题扣5分

### 质量等级

- **90-100分**: 优秀 ⭐⭐⭐⭐⭐
- **70-89分**: 良好 ⭐⭐⭐⭐
- **50-69分**: 一般 ⭐⭐⭐
- **30-49分**: 较差 ⭐⭐
- **0-29分**: 很差 ⭐

## 💡 优化建议

### 提高代码质量

1. **减少重复代码** - 提取公共函数和组件
2. **修复类型错误** - 完善TypeScript类型定义
3. **遵循代码规范** - 修复ESLint警告和错误
4. **增加测试覆盖** - 编写更多单元测试
5. **简化复杂模块** - 拆分过大的文件

### 项目结构优化

1. **清理无用依赖** - 移除未使用的包
2. **统一配置管理** - 使用共享配置
3. **模块化重构** - 建立清晰的模块边界

## 🚨 注意事项

1. **首次运行** - 可能需要安装额外的工具
2. **大型项目** - 检查时间可能较长
3. **CI/CD集成** - 注意脚本的退出码
4. **报告文件** - 会覆盖之前的报告

## 🔗 相关命令

```bash
# 单独运行各项检查
npx tsc --noEmit                    # TypeScript检查
npx eslint . --format json          # ESLint检查
npx jscpd --threshold 10            # 重复代码检查
npm test -- --coverage              # 测试覆盖率
```

## 📋 实际运行示例

### 成功运行的输出

```
🔍 开始代码质量检查...

📊 检查代码重复度...
   ⚠️  无法检查代码重复度 (可能需要安装jscpd)
🔧 检查TypeScript类型错误...
   ✅ 无TypeScript类型错误
📝 检查ESLint规则...
   ⚠️  无法运行ESLint检查
🧪 检查测试覆盖率...
   ⚠️  无法获取测试覆盖率
📈 检查模块复杂度...
   平均文件行数: 348
   复杂文件数量: 33
📦 检查依赖关系...
   总依赖数: 20
   未使用依赖: 16
⚙️  检查配置一致性...
   配置问题: 1
🔄 验证重构效果...
   重构效果评分: 70/100

✅ 代码质量检查完成

📊 质量检查报告:
总体评分: 0/100
通过检查: 3
失败检查: 1
改进建议: 8

📄 详细报告已保存至: quality-report.md
```

### 生成的报告内容

报告包含：

- **质量指标** - 各项具体数值
- **通过的检查** - 已达标的项目
- **失败的检查** - 需要改进的项目
- **改进建议** - 具体的优化方向

## ✅ 当前项目状态

经过测试，脚本在当前项目中可以正常运行，主要发现：

### 优点

- ✅ TypeScript类型检查通过
- ✅ 共享工具库和类型定义已建立
- ✅ 重构效果良好（70/100）

### 需要改进的地方

- ⚠️ 需要安装和配置jscpd工具
- ⚠️ 需要配置ESLint规则
- ⚠️ 需要设置测试覆盖率
- ⚠️ 有33个文件过于复杂，建议拆分
- ⚠️ 有16个未使用的依赖需要清理
- ⚠️ 配置管理需要统一

## 🛠️ 快速改进建议

1. **安装质量检查工具**

```bash
npm install --save-dev jscpd eslint
npm init @eslint/config
```

2. **清理未使用依赖**

```bash
npm uninstall [未使用的包名]
```

3. **配置测试覆盖率**

```bash
# 在package.json中添加测试脚本
"test": "jest --coverage"
```
